# 聚单客预约到达时间改造文档

## 概述

本次改造将聚单客订单创建中的预约时间处理逻辑进行了优化，使用 `TpmOrder.scheduledTime` 字段（HH:mm格式）替代原来的 `mealPickupTime` 字段，并将预约时间设置到聚单客的 `reserve_arrival_time` 字段中，格式为 "yyyy-MM-dd HH:mm:ss"。

## 主要改造内容

### 1. 字段使用变更

#### 原有逻辑
```java
// 使用 mealPickupTime 字段（LocalDateTime类型）
if (tpmOrder.getMealPickupTime() != null) {
    jdkOrderDto.setBooking(1);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    String pickupTimeStr = tpmOrder.getMealPickupTime().format(formatter);
    jdkOrderDto.setPickup_time(pickupTimeStr);
}
```

#### 新的逻辑
```java
// 使用 scheduledTime 字段（String类型，HH:mm格式）
if (tpmOrder.getScheduledTime() != null && !tpmOrder.getScheduledTime().trim().isEmpty()) {
    jdkOrderDto.setBooking(1);
    
    // 使用 scheduledTime 字段（HH:mm格式）拼接为当天的完整时间
    String scheduledTimeStr = tpmOrder.getScheduledTime().trim();
    
    // 获取当天日期
    LocalDate today = LocalDate.now();
    
    // 拼接为完整的日期时间格式 "yyyy-MM-dd HH:mm:ss"
    String reserveArrivalTime = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + scheduledTimeStr + ":00";
    
    jdkOrderDto.setReserve_arrival_time(reserveArrivalTime);
    log.info("设置聚单客预约到达时间: {} (原始预约时间: {})", reserveArrivalTime, scheduledTimeStr);
}
```

### 2. 字段映射变更

| 变更项 | 原有字段 | 新字段 | 说明 |
|--------|----------|--------|------|
| 数据源 | `mealPickupTime` (LocalDateTime) | `scheduledTime` (String) | 使用用户输入的原始时间格式 |
| 目标字段 | `pickup_time` | `reserve_arrival_time` | 更准确地表示预约到达时间 |
| 时间格式 | 完整日期时间 | HH:mm + 当天日期拼接 | 简化用户输入，系统自动拼接日期 |

### 3. 时间格式处理

#### 输入格式
- **用户输入**：HH:mm 格式（如 "20:30"）
- **存储字段**：`TpmOrder.scheduledTime`

#### 输出格式
- **聚单客字段**：`reserve_arrival_time`
- **格式**：yyyy-MM-dd HH:mm:ss（如 "2024-12-25 20:30:00"）
- **日期**：自动使用当天日期

#### 转换示例
```
输入: "20:30"
当天日期: 2024-12-25
输出: "2024-12-25 20:30:00"
```

## 功能特性

### 1. 智能时间拼接
- 自动获取当天日期
- 将 HH:mm 格式的时间拼接为完整的日期时间
- 自动添加秒数（:00）

### 2. 空值处理
- 支持 null 值处理
- 支持空字符串和空白字符串处理
- 只有有效的预约时间才会设置 booking 标识

### 3. 日志记录
- 记录预约时间的设置过程
- 包含原始输入和最终输出的对比
- 便于调试和问题排查

## 代码示例

### 1. 基本使用
```java
TpmOrder order = new TpmOrder();
order.setScheduledTime("18:30"); // 设置预约时间

// 生成聚单客订单数据
String orderData = JuDanKeOrderUtils.getPreCreateOrderData(order, goodsList, shopId, userId);

// 结果中包含：
// "booking": 1
// "reserve_arrival_time": "2024-12-25 18:30:00"
```

### 2. 不同时间格式
```java
// 早餐时间
order.setScheduledTime("08:00");
// 输出: "2024-12-25 08:00:00"

// 午餐时间
order.setScheduledTime("12:30");
// 输出: "2024-12-25 12:30:00"

// 晚餐时间
order.setScheduledTime("19:45");
// 输出: "2024-12-25 19:45:00"
```

### 3. 无预约时间
```java
TpmOrder order = new TpmOrder();
// 不设置 scheduledTime 或设置为 null

// 生成聚单客订单数据
String orderData = JuDanKeOrderUtils.getPreCreateOrderData(order, goodsList, shopId, userId);

// 结果中不包含：
// "booking" 字段
// "reserve_arrival_time" 字段
```

## 测试验证

### 1. 单元测试
```java
@Test
public void testScheduledTimeToReserveArrivalTime() throws Exception {
    TpmOrder testOrder = new TpmOrder();
    testOrder.setScheduledTime("20:30");
    
    String orderData = getPreCreateOrderData(testOrder, goodsList, "SHOP001", "USER001");
    
    JSONObject jsonObject = JSON.parseObject(orderData);
    String reserveArrivalTime = jsonObject.getString("reserve_arrival_time");
    Integer booking = jsonObject.getInteger("booking");
    
    String expectedDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    String expectedTime = expectedDate + " 20:30:00";
    
    assert booking != null && booking == 1;
    assert expectedTime.equals(reserveArrivalTime);
}
```

### 2. 集成测试
- 测试不同时间格式的转换
- 测试空值和边界情况
- 测试与聚单客API的集成

## 兼容性说明

### 1. 向后兼容
- 原有的 `mealPickupTime` 字段保持不变
- 不影响其他使用 `mealPickupTime` 的功能
- 新逻辑只影响聚单客订单创建

### 2. 数据兼容
- `scheduledTime` 字段为新增字段，原有数据为 null
- 系统能正确处理 null 值情况
- 不会影响现有订单的处理

### 3. API兼容
- 聚单客API接口保持不变
- 只是字段映射发生变化
- 聚单客系统能正确识别新的字段

## 优势分析

### 1. 用户体验优化
- **简化输入**：用户只需输入 HH:mm 格式
- **直观理解**：时间格式更符合用户习惯
- **减少错误**：避免用户输入完整日期时间的错误

### 2. 系统设计优化
- **数据一致性**：使用统一的时间格式
- **逻辑清晰**：时间处理逻辑更加明确
- **维护性强**：代码更易理解和维护

### 3. 业务逻辑优化
- **当天预约**：符合餐饮行业当天预约的业务特点
- **自动拼接**：减少手动处理日期的复杂性
- **准确映射**：使用更准确的字段名称

## 注意事项

### 1. 时区处理
- 当前使用系统默认时区
- 如需支持多时区，需要额外处理
- 建议在配置中统一时区设置

### 2. 日期边界
- 当前只支持当天预约
- 跨天预约需要额外的业务逻辑
- 建议在前端限制预约时间范围

### 3. 格式验证
- 依赖前端的时间格式校验
- 建议在聚单客集成前再次验证格式
- 异常情况需要有降级处理

## 后续优化建议

### 1. 功能增强
- 支持跨天预约（明天、后天等）
- 添加时区配置支持
- 支持更灵活的时间格式

### 2. 错误处理
- 添加时间格式验证
- 完善异常处理机制
- 提供更详细的错误信息

### 3. 监控和日志
- 添加预约时间使用统计
- 监控聚单客API调用成功率
- 记录时间转换的性能指标
