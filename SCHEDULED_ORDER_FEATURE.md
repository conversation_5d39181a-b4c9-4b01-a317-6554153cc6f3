# 预约订单功能改造文档

## 概述

本次改造为订单系统添加了预约时间功能，支持用户预约取餐或配送时间。该功能适用于所有订单类型：外卖配送、自提取餐、堂食用餐。

## 功能特性

### 1. 统一的预约时间字段
- 使用单一字段 `scheduledTime` 处理所有类型的预约
- 外卖订单：预约配送时间
- 自提订单：预约取餐时间  
- 堂食订单：预约用餐时间

### 2. 数据存储
- 预约时间存储在 `TpmOrder.mealPickupTime` 字段中
- 使用 `LocalDateTime` 类型，支持精确到秒的时间设置
- 兼容原有数据结构，无需数据库迁移

### 3. 第三方集成
- 自动将预约时间传递给聚单客配送平台
- 设置 `booking=1` 标识为预约订单
- 时间格式转换为聚单客要求的 `yyyy-MM-dd HH:mm:ss` 格式

## 改造内容

### 1. 请求参数扩展

#### TpmOrderSubmitReqVo.java
```java
/**
 * 预约取餐或配送时间
 * 适用于所有订单类型：
 * - 外卖订单：预约配送时间
 * - 自提订单：预约取餐时间
 * - 堂食订单：预约用餐时间
 */
private LocalDateTime scheduledTime;
```

### 2. 订单处理逻辑

#### WxOrderService.submit() 方法
- 接收预约时间参数
- 将预约时间设置到订单的 `mealPickupTime` 字段
- 记录预约时间日志

```java
// 设置预约时间
if (tpmOrderSubmitReqVo.getScheduledTime() != null) {
    order.setMealPickupTime(tpmOrderSubmitReqVo.getScheduledTime());
    log.info("订单设置预约时间: {}", tpmOrderSubmitReqVo.getScheduledTime());
}
```

### 3. 聚单客集成

#### JuDanKeOrderUtils.getPreCreateOrderData() 方法
- 检查订单是否有预约时间
- 设置聚单客的 `booking` 和 `pickup_time` 字段
- 时间格式转换

```java
// 处理预约时间
if (tpmOrder.getMealPickupTime() != null) {
    jdkOrderDto.setBooking(1);
    // 将LocalDateTime转换为聚单客需要的时间格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    String pickupTimeStr = tpmOrder.getMealPickupTime().format(formatter);
    jdkOrderDto.setPickup_time(pickupTimeStr);
}
```

## 使用示例

### 1. 外卖预约配送
```java
TpmOrderSubmitReqVo reqVo = new TpmOrderSubmitReqVo();
reqVo.setUserId(2001);
reqVo.setFreightType((byte) 0); // 外卖配送

// 预约明天中午12点配送
LocalDateTime scheduledTime = LocalDateTime.now().plusDays(1).withHour(12).withMinute(0);
reqVo.setScheduledTime(scheduledTime);

WxResult<Map<String, Object>> result = wxOrderService.submit(reqVo);
```

### 2. 自提预约取餐
```java
TpmOrderSubmitReqVo reqVo = new TpmOrderSubmitReqVo();
reqVo.setUserId(2002);
reqVo.setFreightType((byte) 1); // 自提

// 预约今天下午3点取餐
LocalDateTime scheduledTime = LocalDateTime.now().withHour(15).withMinute(0);
reqVo.setScheduledTime(scheduledTime);

WxResult<Map<String, Object>> result = wxOrderService.submit(reqVo);
```

### 3. 堂食预约用餐
```java
TpmOrderSubmitReqVo reqVo = new TpmOrderSubmitReqVo();
reqVo.setUserId(2003);
reqVo.setFreightType((byte) 2); // 堂食

// 预约今晚7点用餐
LocalDateTime scheduledTime = LocalDateTime.now().withHour(19).withMinute(0);
reqVo.setScheduledTime(scheduledTime);

WxResult<Map<String, Object>> result = wxOrderService.submit(reqVo);
```

## 兼容性保证

### 1. 向后兼容
- 不设置 `scheduledTime` 的订单仍然正常工作
- 原有的立即订单逻辑保持不变
- 数据库字段复用，无需结构变更

### 2. 可选功能
- 预约时间为可选参数
- 未设置预约时间时，订单按立即处理
- 不影响现有业务流程

## 业务场景

### 1. 餐饮预约
- **早餐预约**：前一天晚上预约第二天早餐
- **午餐预约**：上午预约中午用餐
- **晚餐预约**：下午预约晚上用餐
- **聚餐预约**：提前预约多人聚餐

### 2. 特殊场合
- **生日蛋糕**：指定生日当天配送时间
- **节日订餐**：节假日提前预约
- **商务用餐**：会议前预约工作餐
- **活动配送**：活动开始前预约配送

### 3. 运营优化
- **错峰配送**：引导用户选择非高峰时段
- **产能规划**：根据预约情况安排生产
- **人员调度**：根据预约量安排配送人员
- **库存管理**：根据预约订单准备食材

## 验证建议

### 1. 时间验证
```java
// 预约时间不能是过去时间
if (scheduledTime.isBefore(LocalDateTime.now())) {
    return WxResult.error("预约时间不能是过去时间");
}

// 预约时间至少提前30分钟
if (scheduledTime.isBefore(LocalDateTime.now().plusMinutes(30))) {
    return WxResult.error("预约时间至少需要提前30分钟");
}

// 预约时间不能超过7天
if (scheduledTime.isAfter(LocalDateTime.now().plusDays(7))) {
    return WxResult.error("预约时间不能超过7天");
}
```

### 2. 营业时间验证
```java
// 检查预约时间是否在营业时间内
int hour = scheduledTime.getHour();
if (hour < 8 || hour > 22) {
    return WxResult.error("预约时间必须在营业时间内（8:00-22:00）");
}
```

### 3. 配送范围验证
```java
// 对于外卖订单，验证预约时间的配送可用性
if (freightType == 0 && !isDeliveryAvailable(scheduledTime, addressId)) {
    return WxResult.error("该时间段暂不支持配送到此地址");
}
```

## 监控和统计

### 1. 预约订单统计
- 预约订单占比
- 预约时间分布
- 预约准时率
- 用户预约习惯

### 2. 运营指标
- 预约订单取消率
- 预约时间准确性
- 配送准时率
- 用户满意度

### 3. 系统性能
- 预约订单处理时间
- 聚单客接口响应时间
- 数据库查询性能
- 缓存命中率

## 后续优化建议

### 1. 功能增强
- 支持预约时间段选择
- 添加预约提醒功能
- 支持预约订单修改
- 增加预约容量限制

### 2. 用户体验
- 预约时间智能推荐
- 可视化时间选择器
- 预约状态实时更新
- 预约确认通知

### 3. 运营工具
- 预约订单管理后台
- 预约容量配置
- 预约规则设置
- 预约数据分析

## 注意事项

### 1. 时区处理
- 确保服务器和客户端时区一致
- 预约时间存储使用统一时区
- 显示时间考虑用户本地时区

### 2. 并发控制
- 预约容量限制
- 同一时间段订单数量控制
- 防止重复预约

### 3. 异常处理
- 预约时间冲突处理
- 店铺临时关闭处理
- 配送异常处理
- 系统故障恢复
