import request from '@/utils/request'

export function listGoods(query) {
  return request({
    url: '/goods/list',
    method: 'get',
    params: query
  })
}

export function deleteGoods(data) {
  return request({
    url: '/goods/delete',
    method: 'post',
    data
  })
}

export function publishGoods(data) {
  return request({
    url: '/goods/create',
    method: 'post',
    data
  })
}

export function detailGoods(id) {
  return request({
    url: '/goods/detail',
    method: 'get',
    params: { id }
  })
}

export function editGoods(data) {
  return request({
    url: '/goods/update',
    method: 'post',
    data
  })
}

export function batchSaleOut(data) {
  return request({
    url: '/goods/batchSaleOut',
    method: 'post',
    data
  })
}

export function batchOnSale(data) {
  return request({
    url: '/goods/batchOnSale',
    method: 'post',
    data
  })
}

export function listCatAndBrand(params) {
  return request({
    url: '/goods/catAndBrand',
    method: 'get',
    params
  })
}

export function listCatalogCateL2(params) {
  return request({
    url: '/category/L2',
    method: 'get',
    params
  })
}

export function listCatalogCateL1(params) {
  return request({
    url: '/category/l1',
    method: 'get',
    params
  })
}

export function updateInventoryNum(data) {
  return request({
    url: '/goods/updateInventoryNum',
    method: 'post',
    data
  })
}

