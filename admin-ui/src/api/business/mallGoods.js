import request from '@/utils/request'

export function listGoods(query) {
  const newquery = {
    ...query,
    businessType: 2
  }
  return request({
    url: '/goods/list',
    method: 'get',
    params: newquery
  })
}

export function deleteGoods(data) {
  return request({
    url: '/goods/delete',
    method: 'post',
    data
  })
}

export function publishGoods(data) {
  console.log('data', data)

  const newquery = {
    ...data,
    businessType: 2
  }
  console.log('newquery', newquery)
  return request({
    url: '/goods/create',
    method: 'post',
    data: newquery
  })
}

export function detailGoods(id) {
  return request({
    url: '/goods/detail',
    method: 'get',
    params: { id }
  })
}

export function editGoods(data) {
  return request({
    url: '/goods/update',
    method: 'post',
    data
  })
}

export function batchSaleOut(data) {
  return request({
    url: '/goods/batchSaleOut',
    method: 'post',
    data
  })
}

export function batchOnSale(data) {
  return request({
    url: '/goods/batchOnSale',
    method: 'post',
    data
  })
}

export function listCatAndBrand() {
  const query = {
    businessType: 2
  }
  return request({
    url: '/goods/catAndBrand',
    method: 'get',
    params: query
  })
}

export function listCatalogCateL2(params) {
  const query = {
    businessType: 2,
    ...params
  }
  return request({
    url: '/category/L2',
    method: 'get',
    params: query
  })
}

export function updateInventoryNum(data) {
  return request({
    url: '/goods/updateInventoryNum',
    method: 'post',
    data
  })
}

