import request from '@/utils/request'
import Qs from 'qs'

export function listOrder(query) {
  console.log('query', query)
  const newquery = {
    businessType: 2,
    ...query
  }
  console.log('newquery', newquery)
  return request({
    url: '/order/list',
    method: 'get',
    params: newquery,
    paramsSerializer: function(params) {
      return Qs.stringify(params, { arrayFormat: 'repeat' })
    }
  })
}

export function detailOrder(id) {
  return request({
    url: '/order/detail',
    method: 'get',
    params: { id }
  })
}

export function shipOrder(data) {
  return request({
    url: '/order/ship',
    method: 'post',
    data
  })
}

export function refundOrder(data) {
  return request({
    url: '/order/refund',
    method: 'post',
    data
  })
}

export function manualUpdateStatus(data) {
  return request({
    url: '/order/manualUpdateStatus',
    method: 'post',
    data
  })
}

export function replyComment(data) {
  return request({
    url: '/order/reply',
    method: 'post',
    data
  })
}

export function listShipChannel() {
  return request({
    url: '/order/listShipChannel',
    method: 'get'
  })
}

export function finishOrder(data) {
  return request({
    url: '/order/prepare',
    method: 'post',
    data
  })
}

export function printOrder(data) {
  console.log('data:', data)
  return request({
    url: '/order/mallPrint',
    method: 'post',
    data
  })
}

export function manualShipsn(data) {
  console.log('data:', data)
  return request({
    url: '/order/shipSn/add',
    method: 'post',
    data
  })
}
