import request from '@/utils/request'

export function listPointConfig(query) {
  return request({
    url: '/point/config/list',
    method: 'get',
    params: query
  })
}

export function createPointConfig(data) {
  return request({
    url: '/point/config/create',
    method: 'post',
    data
  })
}

export function readPointConfig(data) {
  return request({
    url: '/point/config/read',
    method: 'get',
    data
  })
}

export function updatePointConfig(data) {
  return request({
    url: '/point/config/update',
    method: 'post',
    data
  })
}

export function deletePointConfig(data) {
  return request({
    url: '/point/config/delete',
    method: 'post',
    data
  })
}
