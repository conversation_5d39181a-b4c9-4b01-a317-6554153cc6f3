import request from '@/utils/request'

export function listPointFlow(query) {
  return request({
    url: '/point/list',
    method: 'get',
    params: query
  })
}

export function createPointFlow(data) {
  return request({
    url: '/point/config/create',
    method: 'post',
    data
  })
}

export function readPointFlow(data) {
  return request({
    url: '/point/config/read',
    method: 'get',
    data
  })
}

export function updatePointFlow(data) {
  return request({
    url: '/point/config/update',
    method: 'post',
    data
  })
}

export function deletePointFlow(data) {
  return request({
    url: '/point/config/delete',
    method: 'post',
    data
  })
}
