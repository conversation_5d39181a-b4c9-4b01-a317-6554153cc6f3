import request from '@/utils/request'

export function listRecharge(query) {
  return request({
    url: '/recharge/list',
    method: 'get',
    params: query
  })
}

export function createRecharge(data) {
  return request({
    url: '/recharge/create',
    method: 'post',
    data
  })
}

export function readRecharge(data) {
  return request({
    url: '/recharge/read',
    method: 'get',
    data
  })
}

export function updateRecharge(data) {
  return request({
    url: '/recharge/update',
    method: 'post',
    data
  })
}

export function deleteRecharge(data) {
  return request({
    url: '/recharge/delete',
    method: 'post',
    data
  })
}
