import request from '@/utils/request'

export function _list(query) {
  return request({
    url: '/static/page/list',
    method: 'get',
    params: query
  })
}

export function _delete(data) {
  return request({
    url: '/static/page/delete',
    method: 'post',
    data
  })
}

export function _create(data) {
  return request({
    url: '/static/page/create',
    method: 'post',
    data
  })
}

export function _update(data) {
  return request({
    url: '/static/page/update',
    method: 'post',
    data
  })
}
