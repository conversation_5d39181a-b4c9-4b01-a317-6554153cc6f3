import request from '@/utils/request'

export function listVip(query) {
  return request({
    url: '/membership/ui/list',
    method: 'get',
    params: query
  })
}

export function createVip(data) {
  return request({
    url: '/membership/ui/create',
    method: 'post',
    data
  })
}

export function readVip(data) {
  return request({
    url: '/membership/ui/detail',
    method: 'get',
    data
  })
}

export function updateVip(data) {
  return request({
    url: '/membership/ui/update',
    method: 'post',
    data
  })
}

export function deleteVip(data) {
  return request({
    url: '/membership/ui/delete',
    method: 'post',
    data
  })
}
