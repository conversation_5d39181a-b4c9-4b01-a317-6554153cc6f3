// /admin/goods / setShopStatus

import request from '@/utils/request'

// 查询登录日志列表
export function setShopStatus(query) {
  return request({
    url: '/shop/setShopStatus',
    method: 'post',
    data: query
  })
}

export function setShopTimes(query) {
  return request({
    url: '/shop/setShopHourSetting',
    method: 'post',

    data: query
  })
}

export function getshopDetail(query) {
  return request({
    url: '/shop/getShopSetting',
    method: 'get',
    params: query
  })
}

export function getShopList(query) {
  return request({
    url: '/shop/user/shop/list',
    method: 'get',
    params: query
  })
}

export default {
  setShopStatus,
  setShopTimes,
  getshopDetail,
  getShopList
}
