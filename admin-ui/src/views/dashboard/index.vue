<template>
  <div class="dashboard-editor-container">

    <el-row :gutter="40" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('newVisitis')">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="peoples" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">用户数量</div>
            <count-to :start-val="0" :end-val="userTotal" :duration="2600" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('messages')">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="message" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">商品数量</div>
            <count-to :start-val="0" :end-val="goodsTotal" :duration="3000" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('purchases')">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="message" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">货品数量</div>
            <count-to :start-val="0" :end-val="productTotal" :duration="3200" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('shoppings')">
          <div class="card-panel-icon-wrapper icon-shoppingCard">
            <svg-icon icon-class="money" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">订单数量</div>
            <count-to :start-val="0" :end-val="orderTotal" :duration="3600" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <line-chart :chart-data="userOrderCnt" />
    </el-row>

    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="16">
        <div class="chart-wrapper">
          <bar-chart :chart-data="orderAmts"/>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <pie-chart :chart-data="categorySell"/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { info, chart, verifyQRCode } from '@/api/business/dashboard'
import CountTo from 'vue-count-to'
import LineChart from './LineChart'
import PieChart from './PieChart'
import BarChart from './BarChart'

export default {
  components: {
    CountTo,
    LineChart,
    PieChart,
    BarChart
  },
  data() {
    return {
      userTotal: 0,
      goodsTotal: 0,
      productTotal: 0,
      orderTotal: 0,
      dialogVisible: true,
      userOrderCnt: { dayData: [], userCnt: [], orderCnt: [] },
      orderAmts: { dayData: [], orderAmtData: [], orderCntData: [] },
      categorySell: { categoryNames: [], categorySellData: [] },
      qrCodeText: ''
    }
  },
  mounted() {
    document.addEventListener('keydown', (e) => {
      if (e.key && e.key.length === 1) {
        this.qrManage(e.key)
      }
    })
  },
  created() {
    chart().then(response => {
      this.userOrderCnt = response.data.data.userOrderCnt
      this.orderAmts = response.data.data.orderAmts
      this.categorySell = response.data.data.categorySell
    })
    info().then(response => {
      this.userTotal = response.data.data.userTotal
      this.goodsTotal = response.data.data.goodsTotal
      this.productTotal = response.data.data.productTotal
      this.orderTotal = response.data.data.orderTotal
    })
  },
  methods: {
    qrManage(key) {
      if (key === '<') {
        this.qrStart = true
        this.qrTimer = setTimeout(() => {
          clearTimeout(this.qrTimer)
        }, 1200)
      }
      if (this.qrStart && key !== '>' && key !== '<') {
        this.qrCodeText += key
      }
      if (key === '>') {
        this.qrStart = false
        this.verify(this.qrCodeText)
        clearTimeout(this.qrTimer)
      }
    },
    verify(val) {
      verifyQRCode({ qrCode: val }).then((res) => {
        if (res.data.errno === 0) {
          this.$notify({
            title: '取餐成功',
            message: res.data.data,
            duration: 0,
            type: 'success'
          })
        } else {
          this.$notify.error({
            title: '错误',
            message: res.data.errmsg,
            duration: 0
          })
        }
        this.qrCodeText = ''
      }).catch((err) => {
        console.log(err)
        this.qrCodeText = ''
        this.$notify.error({
          title: err.data.errmsg || '未知错误',
          duration: 0
        })
      })
    },
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
    serviceConnect() {
      console.log('检测USB设备连接')
      // 检测USB设备连接
      let currentDevice = null
      navigator.usb.getDevices().then((devices) => {
        console.log(`Total devices: ${devices.length}`)

        devices.forEach((device) => {
          console.log(
            `Product name: ${device.productName}, serial number ${device.serialNumber}`,
          )
          if (device.productName === 'HIDKeyBoard') {
            currentDevice = device
          }
        })
        console.log('currentDevice:', currentDevice)
        return currentDevice.open()
      })
        .then(() => currentDevice.selectConfiguration(1)) // 选择配置
        // .then(() => currentDevice.claimInterface(0)) // 请求接口
        .then(() => {
          //
          console.log('设备已连接并准备好进行通信')
          // 获取设备信息
          // const deviceInfo = currentDevice.getInfo();
          // console.log(deviceInfo);
        })
        .catch(error => {
          console.error(error)
        })
    },
    requestDevice() {
      navigator.usb.requestDevice({ filters: [] })
        .then(device => {
          // 用户已选择设备并且网页已获得设备访问权限
          console.log(device) // 打印新获取的设备信息
          this.serviceConnect()
        })
        .catch(error => {
          console.error(error) // 错误处理
        })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

.panel-group {
  margin-top: 18px;

  .card-panel-col{
    margin-bottom: 32px;
  }
  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);
    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-people {
         background: #40c9c6;
      }
      .icon-message {
        background: #36a3f7;
      }
      .icon-money {
        background: #f4516c;
      }
      .icon-shoppingCard {
        background: #34bfa3
      }
    }
    .icon-people {
      color: #40c9c6;
    }
    .icon-message {
      color: #36a3f7;
    }
    .icon-money {
      color: #f4516c;
    }
    .icon-shoppingCard {
      color: #34bfa3
    }
    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }
    .card-panel-icon {
      float: left;
      font-size: 48px;
    }
    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;
      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }
      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
</style>
