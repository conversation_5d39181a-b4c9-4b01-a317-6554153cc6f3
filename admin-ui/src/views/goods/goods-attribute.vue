<template>
  <div>
    <div v-for="(item,index) in goodsAttributes" :key="index" class="attrs-item">
      <div>
        <div class="attrs-item-head">
          <el-col :span="8"> <div>
            <el-input v-model="item.value" placeholder="请输入属性名称" class="none-border-input"/>
          </div></el-col>
          <el-col :span="6">
            <div style="display:flex;flex-direction:row;align-items:center;"> <div style="display:flex;flex-direction:row;align-items:center;" >
              是否必选
              <el-switch
                v-model="item.required"/>
            </div>
            </div>
          </el-col>

          <el-col :span="12">
            <div style="display:flex;flex-direction:row;align-items:center;">
              <div style="display:flex;flex-direction:row;align-items:center;" >
                任选多件
                <el-switch
                  v-model="item.selectRequired"/>
              </div>
              <el-input-number v-if="item.selectRequired" v-model="item.selectNumber" :precision="2" :step="0.1" />
            </div>

          </el-col>
          <el-col :span="4">
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteAttr(index)">删除</el-button>
          </el-col>
        </div>
        <el-table
          :data="item.goodsSpecifications"
          style="width: 100%">
          <el-table-column
            label="属性值"
            width="180">
            <template slot-scope="scope">
              <el-input v-model="scope.row.value" placeholder="请输入内容"/>
            </template>
          </el-table-column>
          <el-table-column
            label="加价"
            width="250">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.price" :precision="2" :step="0.1" />
            </template>
          </el-table-column>
          <el-table-column
            label="商品图片"
            width="250">
            <template slot-scope="scope">

              <el-upload
                :action="uploadPath"
                :headers="headers"
                :limit="1"
                :file-list="scope.row.picUrl"
                :on-exceed="uploadOverrun"
                :on-success="(response, file, fileList) => handleGalleryUrl(response, file, fileList, index,scope.row)"
                :on-remove="(file, fileList) => handleRemove(file, fileList, index,scope.row)"
                multiple
                accept=".jpg,.jpeg,.png,.gif"
                list-type="picture-card">
                <i class="el-icon-plus"/>
              </el-upload>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                @click="handleEdit(index,scope.$index, 'enable' ,!scope.row.enable)">{{ scope.row.enable?'下架':'上架' }}</el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(index,scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button
          size="mini"
          icon="el-icon-plus"
          class="add-btn-values"
          @click="addgoodsSpecifications(index)"
        >添加属性值</el-button>
      </div>

    </div>
    <el-button
      type="primary"
      icon="el-icon-plus"
      style="margin-top: 10px;"
      @click="addAttr">新增属性</el-button>
  </div>

</template>
  <style>
  .attrs-item-head{
    display: flex;
    height: 50px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
    .none-border-input .el-input__inner{
        border: none;
        padding-left: 0px;
        border: none;
        min-width: 200px;
    }
    .add-btn-values{
        margin-top: 8px;
    }
    .attrs-item{
        padding-top: 10px;
        padding-bottom: 10px;
        border-bottom: 1px #f5f5f5 solid;
    }
  </style>
<script>
import { uploadPath } from '@/api/business/storage'
import { getToken } from '@/utils/auth'
export default {
  name: 'GoodsAttribute',
  props: {
    attributes: {
      type: Array,
      default: function() {}
    }
  },
  data() {
    return {
      uploadPath,
      goodsAttributes: []
    }
  },
  computed: {
    headers() {
      console.log('getToken():', getToken())
      return {
        'X-Dts-Admin-Token': getToken()
      }
    }
  },
  watch: {
    attributes(newVal, oldVal) {
      console.log('newVal:', newVal)
      this.goodsAttributes = newVal.map((item) => {
        item.selectRequired = item.selectNumber > 0
        item.goodsSpecifications.map(speItem => {
          speItem.picUrl = speItem.picUrl ? [{ url: speItem.picUrl }] : []
        })
        return {
          ...item
        }
      })
    }
  },
  mounted(e) {
    console.log('mounted:', e)
  },
  methods: {
    uploadOverrun: function() {
      this.$message({
        type: 'error',
        message: '上传文件个数超出限制!最多上传5张图片!'
      })
    },
    handleGalleryUrl(response, file, fileList, index, row) {
      console.log('response:', response)
      if (response.errno === 0) {
        if (!row.picUrl) {
          row.picUrl = []
        }
        row.picUrl.push({ url: response.data.url, uid: response.data.uid })
      }
    },
    handleRemove: function(file, fileList, index, row) {
      row.picUrl = []
    },
    handleEdit(attrIndex, valuesIndex, key, value) {
      //   console.log(index, row);
      this.goodsAttributes[attrIndex].goodsSpecifications[valuesIndex][key] = value
      this.goodsAttributes = [...this.goodsAttributes]
    },
    handleDeleteAttr(attrIndex) {
      this.goodsAttributes.splice(attrIndex, 1)
      this.goodsAttributes = [...this.goodsAttributes]
    },
    handleDelete(attrIndex, valuesIndex) {
      //   console.log(index, row);
      this.goodsAttributes[attrIndex].goodsSpecifications.splice(valuesIndex, 1)
      this.goodsAttributes = [...this.goodsAttributes]
    },
    addAttr() {
      console.log('this.goodsAttributes', this.goodsAttributes)
      if (!this.goodsAttributes) this.goodsAttributes = []
      this.goodsAttributes.push({
        value: '',
        required: false,
        goodsSpecifications: []
      })
    },
    addgoodsSpecifications(index) {
      this.goodsAttributes[index].goodsSpecifications.push({
        'value': '',
        'price': '0.00',
        'enable': true,
        picUrl: []
      })
      this.goodsAttributes = [...this.goodsAttributes]
    }
  }
}
</script>
