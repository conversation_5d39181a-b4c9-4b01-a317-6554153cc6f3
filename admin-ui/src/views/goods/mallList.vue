<template>
  <div class="app-container">

    <!-- 查询和其他操作 -->
    <div class="filter-container">
      <el-input v-model="listQuery.goodsSn" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入商品编号"/>
      <el-input v-model="listQuery.name" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入商品名称"/>
      <el-select v-model="listQuery.shopId" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入店铺名称">
        <el-option v-for="item in useShopInfoList" :key="item.id" :label="item.shopName" :value="item.id"/>
      </el-select>
      <el-select
        v-model="listQuery.categoryId"
        clearable
        size="mini"
        class="filter-item"
        style="width: 200px;"
        placeholder="请选择二级类目">
        <el-option v-for="item in categoryList" :key="item.value" :label="item.label" :value="item.value"/>
      </el-select>
      <el-button size="mini" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查找</el-button>
      <el-button size="mini" class="filter-item" type="primary" icon="el-icon-edit" @click="handleCreate">添加</el-button>
      <el-button size="mini" class="filter-item" type="primary" icon="el-icon-edit" @click="batchOnSale">批量上下架</el-button>
      <el-button size="mini" class="filter-item" type="primary" icon="el-icon-edit" @click="batchSaleOut">批量售罄</el-button>
      <el-button :loading="downloadLoading" size="mini" class="filter-item" type="warning" icon="el-icon-download" @click="handleDownload">导出</el-button>
    </div>

    <!-- 查询结果 -->
    <el-table v-loading="listLoading" :data="list" size="small" element-loading-text="正在查询中。。。" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column fixed="left" align="center" type="selection" min-width="110" label="选项" @selection-change="handleSelectionChange"/>

      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" class="table-expand">
            <el-form-item label="宣传画廊">
              <img v-for="pic in props.row.gallery" :key="pic" :src="pic" class="gallery">
            </el-form-item>
            <el-form-item label="商品介绍">
              <span>{{ props.row.brief }}</span>
            </el-form-item>
            <el-form-item label="商品单位">
              <span>{{ props.row.unit }}</span>
            </el-form-item>
            <el-form-item label="关键字">
              <span>{{ props.row.keywords }}</span>
            </el-form-item>
            <el-form-item label="类目ID">
              <span>{{ props.row.categoryId }}</span>
            </el-form-item>
            <el-form-item label="品牌商ID">
              <span>{{ props.row.brandId }}</span>
            </el-form-item>

          </el-form>
        </template>
      </el-table-column>

      <el-table-column align="center" min-width="110" label="序号" prop="sortOrder"/>
      <el-table-column align="center" min-width="110" label="店铺" prop="shopName"/>

      <el-table-column align="center" min-width="110" label="商品编号" prop="goodsSn"/>

      <el-table-column align="center" min-width="200" label="名称" prop="name" sortable/>

      <el-table-column align="center" property="iconUrl" label="图片">
        <template slot-scope="scope">
          <img :src="scope.row.picUrl" width="40">
        </template>
      </el-table-column>

      <el-table-column align="center" property="iconUrl" label="分享图">
        <template slot-scope="scope">
          <img :src="scope.row.shareUrl" width="40">
        </template>
      </el-table-column>

      <el-table-column align="center" label="详情" prop="detail">
        <template slot-scope="scope">
          <el-dialog :visible.sync="detailDialogVisible" title="商品详情">
            <div v-html="goodsDetail"/>
          </el-dialog>
          <el-button type="primary" size="mini" @click="showDetail(scope.row.detail)">查看</el-button>
        </template>
      </el-table-column>

      <el-table-column align="center" label="专柜价格" prop="counterPrice"/>

      <el-table-column align="center" label="当前价格" prop="retailPrice"/>
      <el-table-column align="center" label="库存数量" prop="inventoryNum">
        <template slot-scope="scope">
          <div width="40" style="color: blueviolet;" @click="openUpdateNum(scope.row)">{{ scope.row.inventoryNum }}

            <i class="el-icon-edit"/>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="是否新品" prop="isNew">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isNew ? 'success' : 'error' ">{{ scope.row.isNew ? '新品' : '非新品' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="是否热品" prop="isHot">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isHot ? 'success' : 'error' ">{{ scope.row.isHot ? '热品' : '非热品' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="是否售罄" prop="isSellOut">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isSellOut ? 'success' : 'error' ">{{ scope.row.isSellOut ? '已售罄' : '未售罄' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否上架" prop="isOnSale">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isOnSale ? 'success' : 'error' ">{{ scope.row.isOnSale ? '已上架' : '未上架' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="150" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList"/>

    <el-tooltip placement="top" content="返回顶部">
      <back-to-top :visibility-height="100"/>
    </el-tooltip>

    <el-dialog :title="textMap[batchSaleOut]" :visible.sync="batchSaleOutVisible">
      <el-form ref="dataForm" :model="dataForm" status-icon label-position="right" label-width="150px" style="width: 300px; margin-left:20px;">
        <el-form-item label="选中商品售罄状态" prop="enable">
          <el-select v-model="dataForm.isSaleOut" placeholder="请选择">
            <el-option :value="true" label="已售罄"/>
            <el-option :value="false" label="未售罄"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchSaleOutVisible = false">取消</el-button>
        <el-button type="primary" @click="sendBatchSaleOut">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="textMap[batchOnSale]" :visible.sync="batchOnSaleVisible">
      <el-form ref="dataForm" :model="dataForm" status-icon label-position="right" label-width="150px" style="width: 300px; margin-left:20px;">
        <el-form-item label="选中商品上架状态" prop="enable">
          <el-select v-model="dataForm.isOnSale" placeholder="请选择">
            <el-option :value="true" label="已上架"/>
            <el-option :value="false" label="未上架"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchOnSaleVisible = false">取消</el-button>
        <el-button type="primary" @click="sendBatchOnSale">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="numOpen" title="修改库存数量">
      <el-form ref="dataForm" :rules="rules" :model="dataForm" status-icon label-position="right" label-width="150px" style="width: 300px; margin-left:20px;">
        <el-form-item label="库存数量" prop="inventoryNum">
          <el-input-number v-model="dataForm.inventoryNum" :step="1" step-strictly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="numOpen = false">取消</el-button>
        <el-button type="primary" @click="updateInventoryNum">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style>
.el-dialog {
  width: 60%;
}

.table-expand {
  font-size: 0;
}

.table-expand label {
  width: 100px;
  color: #99a9bf;
}

.table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
}

.gallery {
  width: 80px;
  margin-right: 10px;
}
</style>

<script>
import { listGoods, deleteGoods, listCatalogCateL2, batchSaleOut, batchOnSale, updateInventoryNum } from '@/api/business/mallGoods'
import BackToTop from '@/components/BackToTop'
import { listUserShopInfo } from '@/api/business/shopInfo'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination

export default {
  name: 'MallGoodsList',
  components: { BackToTop, Pagination },
  data() {
    var checkAge = (rule, value, callback) => {
      if (!value && value !== 0) {
        return callback(new Error('数量不能为空'))
      }
      if (value < 0) {
        return callback(new Error('数量不能为负数'))
      }
      setTimeout(() => {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else {
          callback()
        }
      }, 500)
    }
    return {
      numOpen: false,
      list: [],
      total: 0,
      listLoading: true,
      editRow: {},
      useShopInfoList: {},
      listQuery: {
        page: 1,
        limit: 20,
        goodsSn: undefined,
        name: undefined,
        categoryId: undefined,
        businessType: 2,
        shopId: undefined,
        sort: 'sort_order',
        order: 'desc'
      },
      textMap: {
        batchSaleOut: '批量售罄',
        batchOnSale: '批量上下架'
      },
      dataForm: {
        goodsIdList: [],
        isSaleOut: undefined,
        businessType: 2,
        inventoryNum: 0
      },
      goodsIdList: [],
      categoryList: [],
      goodsDetail: '',
      detailDialogVisible: false,
      batchSaleOutVisible: false,
      batchOnSaleVisible: false,
      downloadLoading: false,
      rules: { inventoryNum: [
        { validator: checkAge, trigger: 'blur' }
      ] }
    }
  },
  created() {
    this.getList()
    this.getUserShopInfoList()
    this.listCatalogCateL2()
  },
  methods: {
    getUserShopInfoList() {
      listUserShopInfo().then(response => {
        this.useShopInfoList = response.data.data
      })
    },
    getList() {
      this.listLoading = true
      listGoods(this.listQuery).then(response => {
        this.list = response.data.data.items
        this.total = response.data.data.total
        this.listLoading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    listCatalogCateL2: function() {
      listCatalogCateL2().then(response => {
        this.categoryList = response.data.data.categoryL2List
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    openUpdateNum(row) {
      this.dataForm = { ...row }
      this.numOpen = true
    },
    updateInventoryNum() {
      updateInventoryNum({ ...this.dataForm, id: this.dataForm.id })
        .then(response => {
          // this.list.unshift(response.data.data)
          this.numOpen = false
          this.$notify.success({
            title: '成功',
            message: '修改成功'
          })
          // 刷新下页面
          this.getList()
        }).catch((response) => {
          console.log(response)
          this.$notify.error({
            title: '失败',
            message: response.data.errmsg
          })
        })
    },
    handleCreate() {
      this.$router.push({ path: '/mallGoods/mallCreate' })
    },
    handleUpdate(row) {
      this.$router.push({ path: '/mallGoods/mallEdit', query: { id: row.id }})
    },
    showDetail(detail) {
      this.goodsDetail = detail
      this.detailDialogVisible = true
    },
    batchSaleOut() {
      this.batchSaleOutVisible = true
    },
    sendBatchSaleOut() {
      batchSaleOut(this.dataForm)
        .then(response => {
          // this.list.unshift(response.data.data)
          this.batchSaleOutVisible = false
          this.$notify.success({
            title: '成功',
            message: '创建成功'
          })
          // 刷新下页面
          this.getList()
        })
        .catch(response => {
          this.$notify.error({
            title: '失败',
            message: response.data.errmsg
          })
        })
    },
    batchOnSale() {
      this.batchOnSaleVisible = true
    },
    sendBatchOnSale() {
      batchOnSale(this.dataForm)
        .then(response => {
          // this.list.unshift(response.data.data)
          this.batchOnSaleVisible = false
          this.$notify.success({
            title: '成功',
            message: '创建成功'
          })
          // 刷新下页面
          this.getList()
        })
        .catch(response => {
          this.$notify.error({
            title: '失败',
            message: response.data.errmsg
          })
        })
    },
    handleSelectionChange(selection) {
      console.log(selection)
      this.dataForm.goodsIdList = selection.map(item => item.id)
      this.goodsIdList = selection.map(item => item.id)
      console.log('goodsIdList ==>' + this.goodsIdList)
      console.log('dataForm.goodsIdList ==>' + this.goodsIdList)
    },
    handleDelete(row) {
      deleteGoods(row).then(response => {
        this.$notify.success({
          title: '成功',
          message: '删除成功'
        })
        const index = this.list.indexOf(row)
        this.list.splice(index, 1)
      }).catch(response => {
        this.$notify.error({
          title: '失败',
          message: response.data.errmsg
        })
      })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['商品ID', '商品编号', '名称', '专柜价格', '当前价格', '是否新品', '是否热品', '是否在售', '首页主图', '宣传图片列表', '商品介绍', '详细介绍', '商品图片', '商品单位', '关键字', '类目ID', '品牌商ID']
        const filterVal = ['id', 'goodsSn', 'name', 'counterPrice', 'retailPrice', 'isNew', 'isHot', 'isOnSale', 'listPicUrl', 'gallery', 'brief', 'detail', 'picUrl', 'goodsUnit', 'keywords', 'categoryId', 'brandId']
        excel.export_json_to_excel2(tHeader, this.list, filterVal, '商品信息')
        this.downloadLoading = false
      })
    }
  }
}
</script>
