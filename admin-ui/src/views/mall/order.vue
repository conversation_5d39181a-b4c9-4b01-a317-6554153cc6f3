<template>
  <div class="app-container">

    <!-- 查询和其他操作 -->
    <div class="filter-container">
      <el-input v-model="listQuery.userId" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入用户ID"/>
      <el-input v-model="listQuery.orderSn" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入订单编号"/>
      <el-select v-model="listQuery.orderStatusArray" multiple size="mini" style="width: 200px" class="filter-item" placeholder="请选择订单状态">
        <el-option v-for="(key, value) in statusMap" :key="key" :label="key" :value="value"/>
      </el-select>
      <el-select v-model="listQuery.shopId" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入店铺名称">
        <el-option v-for="item in useShopInfoList" :key="item.id" :label="item.shopName" :value="item.id"/>
      </el-select>
      <el-button v-permission="['GET /admin/order/list']" size="mini" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查找</el-button>
      <el-button :loading="downloadLoading" size="mini" class="filter-item" type="warning" icon="el-icon-download" @click="handleDownload">导出</el-button>
    </div>

    <!-- 查询结果 -->
    <el-table v-loading="listLoading" :data="list" size="small" element-loading-text="正在查询中。。。" border fit highlight-current-row>

      <el-table-column align="center" min-width="100" label="店铺名称" prop="shopName"/>

      <el-table-column align="center" min-width="120" label="订单编号" prop="orderSn" sortable/>

      <el-table-column align="center" min-width="90" label="取餐码" prop="mealCode"/>

      <el-table-column align="center" min-width="80px" label="用户ID" prop="userId"/>

      <el-table-column align="center" min-width="100px" label="订单状态" prop="orderStatus">
        <template slot-scope="scope">
          <el-tag>{{ scope.row.orderStatus | orderStatusFilter }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" min-width="80px" label="订单类型" prop="freightType">
        <template slot-scope="scope">
          <span>{{ scope.row.freightType | freightTypeFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" min-width="100px" label="订单金额" prop="orderPrice"/>

      <el-table-column align="center" min-width="100px" label="支付金额" prop="actualPrice"/>

      <el-table-column align="center" min-width="150px" label="支付时间" prop="payTime"/>

      <el-table-column align="center" min-width="120px" label="配送状态" prop="deliveryStatus"/>

      <!--      <el-table-column align="center" min-width="100px" label="物流渠道" prop="shipChannel"/>-->

      <el-table-column align="center" min-width="150px" label="创建时间" prop="addTime"/>

      <el-table-column align="center" label="操作" min-width="350px" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-permission="['GET /admin/order/detail']" type="primary" size="mini" @click="handleDetail(scope.row)">详情</el-button>
          <el-button v-permission="['GET /admin/order/manualUpdateStatus']" type="primary" size="mini" @click="manualUpdateStatus(scope.row)">状态</el-button>
          <el-button v-permission="['POST /admin/order/ship']" v-if="scope.row.orderStatus==201" type="primary" size="mini" @click="handleShip(scope.row)">配送</el-button>
          <el-button v-permission="['POST /admin/order/refund']" v-if="scope.row.orderStatus==202" type="primary" size="mini" @click="handleRefund(scope.row)">退款</el-button>
          <el-button v-permission="['POST /admin/order/prepare']" v-if="[1,2].includes(scope.row.freightType)" type="primary" size="mini" @click="handleFinish(scope.row)">出餐</el-button>
          <el-button type="primary" size="mini" @click="handlePrint(scope.row)">打印</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList"/>

    <!-- 订单详情对话框 -->
    <el-dialog :visible.sync="orderDialogVisible" title="订单详情" width="1000">
      <el-form :data="orderDetail" label-position="left">
        <el-form-item label="订单编号">
          <span>{{ orderDetail.order.orderSn }}</span>
        </el-form-item>
        <el-form-item label="订单状态">
          <template slot-scope="scope">
            <el-tag>{{ scope.order.orderStatus | orderStatusFilter }}</el-tag>
          </template>
        </el-form-item>
        <el-form-item label="订单用户">
          <span>{{ orderDetail.user.nickname }}</span>
        </el-form-item>
        <el-form-item label="用户留言">
          <span>{{ orderDetail.order.message }}</span>
        </el-form-item>
        <el-form-item label="收货信息">
          <span>（收货人）{{ orderDetail.order.consignee }}</span>
          <span>（手机号）{{ orderDetail.order.mobile }}</span>
          <span>（地址）{{ orderDetail.order.address }}</span>
        </el-form-item>
        <el-form-item label="商品信息">
          <el-table :data="orderDetail.orderGoods" size="small" border fit highlight-current-row>
            <el-table-column align="center" label="商品名称" prop="goodsName"/>
            <el-table-column align="center" label="商品编号" prop="goodsSn"/>
            <el-table-column align="center" label="货品规格" prop="specificationJoin"/>
            <el-table-column align="center" label="货品价格" prop="price"/>
            <el-table-column align="center" label="货品数量" prop="number"/>
            <el-table-column align="center" label="货品图片" prop="picUrl">
              <template slot-scope="scope">
                <img :src="scope.row.picUrl" width="40">
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item label="费用信息">
          <span>
            (实际费用){{ orderDetail.order.actualPrice }}元 =
            (商品总价){{ orderDetail.order.goodsPrice }}元 +
            (快递费用){{ orderDetail.order.freightPrice }}元 -
            (优惠减免){{ orderDetail.order.couponPrice }}元 -
            (积分减免){{ orderDetail.order.integralPrice }}元
          </span>
        </el-form-item>
        <el-form-item label="支付信息">
          <span>（支付渠道）{{ orderDetail.order.payType | orderPayTypeFilter }}</span>
          <span>（支付时间）{{ orderDetail.order.payTime }}</span>
        </el-form-item>
        <el-form-item label="快递信息">
          <span>（快递公司）{{ orderDetail.order.shipChannel }}</span>
          <span>（快递单号）{{ orderDetail.order.shipSn }}</span>
          <span>（发货时间）{{ orderDetail.order.shipTime }}</span>
        </el-form-item>
        <el-form-item label="收货信息">
          <span>（确认收货时间）{{ orderDetail.order.confirmTime }}</span>
        </el-form-item>
        <el-form-item label="配送状态轨迹">
          <el-table :data="orderDetail.delivery" size="small" border fit highlight-current-row>
            <el-table-column align="center" label="配送平台" prop="platform"/>
            <el-table-column align="center" label="配送状态" prop="deliveryStatus"/>
            <el-table-column align="center" label="实际扣款的小费(分)" prop="tipFee"/>
            <el-table-column align="center" label="实际扣款的运费(分)" prop="freightFee"/>
            <el-table-column align="center" label="接单员名字" prop="courierName"/>
            <el-table-column align="center" label="接单员电话" prop="courierPhone"/>
            <el-table-column align="center" label="接单品牌" prop="deliveryBrand"/>
            <el-table-column align="center" label="接单实际距离" prop="deliveryDistance"/>
            <el-table-column align="center" label="原因" prop="reason"/>
          </el-table>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 发货对话框 -->
    <el-dialog :visible.sync="shipDialogVisible" title="配送">
      <el-form ref="shipForm" :model="shipForm" status-icon label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <span style="font-weight: bold;">此订单确认配送吗？</span>
        <!--        <el-form-item label="快递公司" prop="shipChannel">-->
        <!--          <el-select v-model="shipForm.shipChannel">-->
        <!--            <el-option v-for="item in shipChannelList" :key="item.value" :label="item.label" :value="item.value"/>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="快递编号" prop="shipSn">-->
        <!--          <el-input v-model="shipForm.shipSn"/>-->
        <!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="shipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmShip">确定</el-button>
      </div>
    </el-dialog>

    <!-- 订单状态对话框 -->
    <el-dialog :visible.sync="orderStatusVisible" title="订单状态修改">
      <el-form ref="manualUpdateStatusForm" :model="manualUpdateStatusForm" status-icon label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="订单状态" prop="orderStatus">
          <el-select v-model="manualUpdateStatusForm.orderStatus">
            <el-option
              v-for="(value, key) in statusMap"
              :key="key"
              :label="value"
              :value="key"
            />
            <!--            <el-option v-for="item in shipChannelList" :key="item.value" :label="item.label" :value="item.value"/>-->
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderStatusVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmOrderStatus">确定</el-button>
      </div>
    </el-dialog>

    <!-- 退款对话框 -->
    <el-dialog :visible.sync="refundDialogVisible" title="退款">
      <el-form ref="refundForm" :model="refundForm" status-icon label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="退款金额" prop="refundMoney">
          <el-input v-model="refundForm.refundMoney" :disabled="true"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="refundDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmRefund">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style>

</style>

<script>
import {
  listOrder,
  shipOrder,
  refundOrder,
  detailOrder,
  listShipChannel,
  manualUpdateStatus,
  finishOrder,
  printOrder
} from '@/api/business/order'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import checkPermission from '@/utils/permission' // 权限判断函数
import { listUserShopInfo } from '@/api/business/shopInfo'

const statusMap = {
  101: '未付款',
  102: '用户取消',
  103: '系统取消',
  201: '已付款',
  202: '申请退款',
  203: '已退款',
  301: '配送中',
  302: '已出餐',
  401: '已收货',
  402: '已收货(系统)'
}

const orderPayTypeMap = {
  BALANCE: '余额支付',
  WECHAT: '微信支付',
  ALIPAY: '支付宝支付'
}

const freightTypeMap = {
  0: '外送',
  1: '自提',
  2: '堂食'
}

export default {
  name: 'Order',
  components: { Pagination },
  filters: {
    orderStatusFilter(status) {
      return statusMap[status]
    },
    freightTypeFilter(status) {
      return freightTypeMap[status]
    },
    orderPayTypeFilter(status) {
      return orderPayTypeMap[status]
    }
  },
  data() {
    return {
      list: undefined,
      total: 0,
      listLoading: true,
      shipChannelList: [],
      listQuery: {
        page: 1,
        limit: 20,
        id: undefined,
        name: undefined,
        orderStatusArray: [],
        shopId: undefined,
        sort: 'add_time',
        order: 'desc'
      },
      useShopInfoList: {},
      statusMap,
      orderDialogVisible: false,
      orderDetail: {
        order: {},
        user: {},
        orderGoods: [],
        delivery: []
      },
      shipForm: {
        orderId: undefined,
        shipChannel: undefined,
        shipSn: undefined
      },
      manualUpdateStatusForm: {
        orderId: undefined,
        orderStatus: undefined
      },
      orderStatusVisible: false,
      shipDialogVisible: false,
      refundForm: {
        orderId: undefined,
        refundMoney: undefined
      },
      refundDialogVisible: false,
      downloadLoading: false
    }
  },
  created() {
    this.getList()
    this.getUserShopInfoList()
    this.getListShipChannel()
  },
  methods: {
    checkPermission,
    getUserShopInfoList() {
      listUserShopInfo().then(response => {
        this.useShopInfoList = response.data.data
        console.log(this.useShopInfoList)
      })
    },
    getList() {
      this.listLoading = true
      listOrder(this.listQuery).then(response => {
        this.list = response.data.data.items
        this.total = response.data.data.total
        this.listLoading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    getListShipChannel() {
      listShipChannel().then(response => {
        this.shipChannelList = response.data.data.shipChannelList
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleDetail(row) {
      detailOrder(row.id).then(response => {
        this.orderDetail = response.data.data
      })
      this.orderDialogVisible = true
    },
    handleShip(row) {
      this.shipForm.orderId = row.id
      this.shipForm.shipChannel = row.shipChannel
      this.shipForm.shipSn = row.shipSn

      this.shipDialogVisible = true
      this.$nextTick(() => {
        this.$refs['shipForm'].clearValidate()
      })
    },
    handleFinish(row) {
      finishOrder({ orderId: row.id }).then(res => {
        this.$notify.success({
          title: '成功',
          message: '出餐成功'
        })
        this.getList()
      }).catch(response => {
        this.$notify.error({
          title: '失败',
          message: response.data.errmsg
        })
      })
    },
    handlePrint(row) {
      printOrder({ orderId: row.id }).then(res => {
        this.$notify.success({
          title: '成功',
          message: '操作成功'
        })
        this.getList()
      }).catch(response => {
        this.$notify.error({
          title: '失败',
          message: response.data.errmsg
        })
      })
    },
    confirmShip() {
      this.$refs['shipForm'].validate((valid) => {
        if (valid) {
          shipOrder(this.shipForm).then(response => {
            this.shipDialogVisible = false
            this.$notify.success({
              title: '成功',
              message: '确认配送成功'
            })
            this.getList()
          }).catch(response => {
            this.$notify.error({
              title: '失败',
              message: response.data.errmsg
            })
          })
        }
      })
    },
    manualUpdateStatus(row) {
      this.manualUpdateStatusForm.orderId = row.id
      this.orderStatusVisible = true
      this.$nextTick(() => {
        this.$refs['manualUpdateStatusForm'].clearValidate()
      })
    },
    confirmOrderStatus() {
      this.$refs['manualUpdateStatusForm'].validate((valid) => {
        if (valid) {
          manualUpdateStatus(this.manualUpdateStatusForm).then(response => {
            this.orderStatusVisible = false
            this.$notify.success({
              title: '成功',
              message: '状态修改成功'
            })
            this.getList()
          }).catch(response => {
            this.$notify.error({
              title: '失败',
              message: response.data.errmsg
            })
          })
        }
      })
    },
    handleRefund(row) {
      this.refundForm.orderId = row.id
      this.refundForm.refundMoney = row.actualPrice

      this.refundDialogVisible = true
      this.$nextTick(() => {
        this.$refs['refundForm'].clearValidate()
      })
    },
    confirmRefund() {
      this.$refs['refundForm'].validate((valid) => {
        if (valid) {
          refundOrder(this.refundForm).then(response => {
            this.refundDialogVisible = false
            this.$notify.success({
              title: '成功',
              message: '确认退款成功'
            })
            this.getList()
          }).catch(response => {
            this.$notify.error({
              title: '失败',
              message: response.data.errmsg
            })
          })
        }
      })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['订单ID', '订单编号', '用户ID', '订单状态', '是否删除', '收货人', '收货联系电话', '收货地址']
        const filterVal = ['id', 'orderSn', 'userId', 'orderStatus', 'isDelete', 'consignee', 'mobile', 'address']
        excel.export_json_to_excel2(tHeader, this.list, filterVal, '订单信息')
        this.downloadLoading = false
      })
    }
  }
}
</script>
