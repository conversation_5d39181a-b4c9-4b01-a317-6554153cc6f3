<template>
  <div class="app-container">

    <!-- 查询和其他操作 -->
    <div class="filter-container">
      <el-select v-model="listQuery.shopId" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入店铺名称">
        <el-option v-for="item in useShopInfoList" :key="item.id" :label="item.shopName" :value="item.id"/>
      </el-select>
      <el-button v-permission="['GET /admin/freight/list']" size="mini" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查找</el-button>
      <el-button v-permission="['POST /admin/freight/create']" size="mini" class="filter-item" type="primary" icon="el-icon-edit" @click="handleCreate">添加</el-button>
    </div>

    <!-- 查询结果 -->
    <el-table v-loading="listLoading" :data="list" size="small" element-loading-text="正在查询中。。。" border fit highlight-current-row>

      <el-table-column align="center" label="ID" width="60" prop="id" sortable/>
      <el-table-column align="center" label="店铺名称" prop="shopName"/>
      <!--      <el-table-column align="center" label="店铺编码" prop="shopCode"/>-->
      <!--      <el-table-column align="center" label="店铺经度" prop="longitude"/>-->
      <!--      <el-table-column align="center" label="店铺纬度" prop="latitude"/>-->
      <el-table-column align="center" min-width="120px" label="配送范围开始(米)" prop="rangeStart"/>
      <el-table-column align="center" min-width="120px" label="配送范围结束(米)" prop="rangeEnd"/>
      <el-table-column align="center" label="价格(元)" prop="price"/>
      <el-table-column align="center" min-width="100px" label="配送类型" prop="type">
        <template slot-scope="scope">
          <el-tag>{{ scope.row.type | typeFilter }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排序" prop="sort"/>
      <el-table-column align="center" label="创建时间" min-width="100" prop="addTime"/>
      <el-table-column align="center" min-width="120px" label="操作" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-permission="['POST /admin/freight/update']" type="primary" size="mini" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button v-permission="['POST /admin/freight/delete']" type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList"/>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="dataForm" status-icon label-position="right" label-width="150px" style="width: 400px; margin-left:80px;">
        <!--        <el-form-item label="店铺名称" prop="shopName">-->
        <!--          <el-input v-model="dataForm.shopName"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="店铺编码" prop="shopCode">-->
        <!--          <el-select v-model="dataForm.shopCode" placeholder="请选择">-->
        <!--            <el-option v-for="(key, value) in shipCodeMap" :key="key" :label="key" :value="value"/>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="店铺" prop="shopId">
          <el-select v-model="dataForm.shopId" placeholder="请选择店铺">
            <el-option v-for="item in useShopInfoList" :key="item.id" :label="item.shopName" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="配送范围开始(米)" prop="rangeStart">
          <el-input v-model="dataForm.rangeStart"/>
        </el-form-item>
        <el-form-item label="配送范围结束(米)" prop="rangeEnd">
          <el-input v-model="dataForm.rangeEnd"/>
        </el-form-item>
        <el-form-item label="价格(元)" prop="price">
          <el-input v-model="dataForm.price"/>
        </el-form-item>
        <el-form-item label="配送类型" prop="type">
          <el-select v-model="dataForm.type" placeholder="请选择">
            <el-option :value="0" label="聚单客"/>
            <!--            <el-option v-for="(key, value) in typeMap" :key="String(key)" :label="String(key)" :value="value"/>-->
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="dataForm.sort"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus=='create'" type="primary" @click="createData">确定</el-button>
        <el-button v-else type="primary" @click="updateData">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #20a0ff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar {
  width: 145px;
  height: 145px;
  display: block;
}
</style>

<script>
import { createFreight, deleteFreight, listFreight, updateFreight } from '@/api/business/freight'
import { uploadPath } from '@/api/business/storage'
import { getToken } from '@/utils/auth'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import { listUserShopInfo } from '@/api/business/shopInfo'

const typeMap = {
  '0': '聚单客'
}

const shipCodeMap = {
  BINJIANG: 'BINJIANG'
}
export default {
  name: 'Freight',
  components: { Pagination },
  filters: {
    typeFilter(status) {
      return typeMap[status]
    }
  },
  data() {
    return {
      uploadPath,
      list: undefined,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        name: undefined,
        shopId: undefined,
        content: undefined,
        sort: 'sort',
        order: 'desc'
      },
      useShopInfoList: {},
      dataForm: {
        id: undefined,
        shopCode: undefined,
        shopName: undefined,
        longitude: undefined,
        latitude: undefined,
        rangeStart: undefined,
        rangeEnd: undefined,
        price: undefined,
        type: undefined,
        sort: undefined
      },
      typeMap,
      shipCodeMap,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '创建'
      },
      rules: {
        shopName: [
          { required: true, message: '店铺名称不能为空', trigger: 'blur' }
        ],
        shopCode: [
          { required: true, message: '店铺编码不能为空', trigger: 'blur' }
        ],
        rangeStart: [
          { required: true, message: '配送开始范围不能为空', trigger: 'blur' }
        ],
        rangeEnd: [
          { required: true, message: '配送结束范围不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '配送类型不能为空', trigger: 'blur' }
        ]
      },
      downloadLoading: false
    }
  },
  computed: {
    headers() {
      return {
        'X-Dts-Admin-Token': getToken()
      }
    }
  },
  created() {
    this.getList()
    this.getUserShopInfoList()
  },
  methods: {
    getUserShopInfoList() {
      listUserShopInfo().then(response => {
        this.useShopInfoList = response.data.data
      })
    },
    getList() {
      this.listLoading = true
      listFreight(this.listQuery)
        .then(response => {
          this.list = response.data.data.items
          this.total = response.data.data.total
          this.listLoading = false
        })
        .catch(() => {
          this.list = []
          this.total = 0
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetForm() {
      this.dataForm = {
        id: undefined,
        name: undefined,
        level: undefined,
        points: undefined,
        picUrl: undefined,
        discount: undefined,
        enable: undefined
      }
    },
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    uploadUrl: function(response) {
      this.dataForm.picUrl = response.data.url
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          createFreight(this.dataForm)
            .then(response => {
              // this.list.unshift(response.data.data)
              this.dialogFormVisible = false
              this.$notify.success({
                title: '成功',
                message: '创建成功'
              })
              // 刷新下页面
              this.getList()
            })
            .catch(response => {
              this.$notify.error({
                title: '失败',
                message: response.data.errmsg
              })
            })
        }
      })
    },
    handleUpdate(row) {
      this.dataForm = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          updateFreight(this.dataForm)
            .then(() => {
              for (const v of this.list) {
                if (v.id === this.dataForm.id) {
                  const index = this.list.indexOf(v)
                  this.list.splice(index, 1, this.dataForm)
                  break
                }
              }
              this.dialogFormVisible = false
              this.$notify.success({
                title: '成功',
                message: '更新成功'
              })
              // 刷新下页面
              this.getList()
            })
            .catch(response => {
              this.$notify.error({
                title: '失败',
                message: response.data.errmsg
              })
            })
        }
      })
    },
    handleDelete(row) {
      deleteFreight(row)
        .then(response => {
          this.$notify.success({
            title: '成功',
            message: '删除成功'
          })
          // 刷新页面
          this.getList()
          // const index = this.list.indexOf(row)
          // this.list.splice(index, 1)
        })
        .catch(response => {
          this.$notify.error({
            title: '失败',
            message: response.data.errmsg
          })
        })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = [
          '广告ID',
          '广告标题',
          '广告内容',
          '广告图片',
          '广告位置',
          '活动链接',
          '是否启用'
        ]
        const filterVal = [
          'id',
          'name',
          'content',
          'url',
          'postion',
          'link',
          'enabled'
        ]
        excel.export_json_to_excel2(tHeader, this.list, filterVal, '广告信息')
        this.downloadLoading = false
      })
    }
  }
}
</script>
