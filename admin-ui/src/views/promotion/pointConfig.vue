<template>
  <div class="app-container">

    <!-- 查询和其他操作 -->
    <div class="filter-container">
      <el-input v-model="listQuery.name" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入标题"/>
      <!--      <el-input v-model="listQuery.content" clearable size="mini" class="filter-item" style="width: 200px;" placeholder="请输入内容"/>-->
      <el-button v-permission="['GET /admin/point/config/list']" size="mini" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查找</el-button>
      <el-button v-permission="['POST /admin/point/config/create']" size="mini" class="filter-item" type="primary" icon="el-icon-edit" @click="handleCreate">添加</el-button>
      <!--      <el-button :loading="downloadLoading" size="mini" class="filter-item" type="warning" icon="el-icon-download" @click="handleDownload">导出</el-button>-->
    </div>

    <!-- 查询结果 -->
    <el-table v-loading="listLoading" :data="list" size="small" element-loading-text="正在查询中。。。" border fit highlight-current-row>

      <el-table-column align="center" label="ID" width="60" prop="id" sortable/>

      <el-table-column align="center" label="序号" prop="sortOrder"/>

      <el-table-column align="center" label="标题" prop="name"/>

      <el-table-column align="center" label="兑换积分数量" prop="amount"/>

      <!--      <el-table-column align="center" label="兑换优惠券ID" prop="couponId"/>-->

      <el-table-column align="center" label="兑换优惠券名称" prop="couponName"/>

      <el-table-column align="center" label="已兑换数量" prop="exchangeNum"/>

      <el-table-column align="center" label="描述" prop="description"/>

      <el-table-column align="center" label="是否启用" prop="enable">
        <template slot-scope="scope">
          {{ scope.row.enable ? '启用' : '禁用' }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="创建时间" width="150" prop="createTime"/>

      <el-table-column align="center" label="操作" min-width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-permission="['POST /admin/point/config/update']" type="primary" size="mini" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button v-permission="['POST /admin/point/config/delete']" type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList"/>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="dataForm" status-icon label-position="right" label-width="120px" style="width: 400px; margin-left:50px;">
        <el-form-item label="标题" prop="name">
          <el-input v-model="dataForm.name"/>
        </el-form-item>
        <el-form-item label="序号" prop="sortOrder">
          <el-input v-model="dataForm.sortOrder"/>
        </el-form-item>
        <el-form-item label="兑换积分数量" prop="amount">
          <el-input v-model="dataForm.amount"/>
        </el-form-item>
        <!--        <el-form-item label="兑换优惠券ID" prop="couponId">-->
        <!--          <el-input v-model="dataForm.couponId"/>-->
        <!--        </el-form-item>-->
        <el-form-item label="兑换优惠券">
          <el-select v-model="dataForm.couponId">
            <el-option v-for="item in couponList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="dataForm.description" type="textarea"/>
        </el-form-item>
        <el-form-item label="是否启用" prop="enable">
          <el-select v-model="dataForm.enable" placeholder="请选择">
            <el-option :value="true" label="启用"/>
            <el-option :value="false" label="禁用"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus=='create'" type="primary" @click="createData">确定</el-button>
        <el-button v-else type="primary" @click="updateData">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #20a0ff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar {
  width: 145px;
  height: 145px;
  display: block;
}
</style>

<script>
import { listAllCoupon } from '@/api/business/coupon'
import { listPointConfig, createPointConfig, updatePointConfig, deletePointConfig } from '@/api/business/pointConfig'
import { uploadPath } from '@/api/business/storage'
import { getToken } from '@/utils/auth'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination

export default {
  name: 'PointConfig',
  components: { Pagination },
  data() {
    return {
      uploadPath,
      list: undefined,
      total: 0,
      couponList: [],
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        name: undefined,
        content: undefined,
        sort: 'sort_order',
        order: 'desc'
      },
      dataForm: {
        id: undefined,
        sortOrder: undefined,
        name: undefined,
        amount: undefined,
        couponId: undefined,
        couponName: undefined,
        exchangeNum: undefined,
        description: undefined,
        enable: undefined,
        createTime: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '创建'
      },
      rules: {
        name: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '兑换积分数量不能为空', trigger: 'blur' }
        ],
        couponId: [
          { required: true, message: '兑换优惠券ID不能为空', trigger: 'blur' }
        ],
        description: [{ required: true, message: '描述不能为空', trigger: 'blur' }]
      },
      downloadLoading: false
    }
  },
  computed: {
    headers() {
      return {
        'X-Dts-Admin-Token': getToken()
      }
    }
  },
  created() {
    this.getList()
    this.getCouponList()
  },
  methods: {
    getList() {
      this.listLoading = true
      listPointConfig(this.listQuery)
        .then(response => {
          this.list = response.data.data.items
          this.total = response.data.data.total
          this.listLoading = false
        })
        .catch(() => {
          this.list = []
          this.total = 0
          this.listLoading = false
        })
    },
    getCouponList() {
      listAllCoupon().then(response => {
        this.couponList = response.data.data.couponList
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetForm() {
      this.dataForm = {
        id: undefined,
        name: undefined,
        amount: undefined,
        couponId: undefined,
        couponName: undefined,
        exchangeNum: undefined,
        description: undefined,
        enable: undefined,
        createTime: undefined
      }
    },
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    uploadUrl: function(response) {
      this.dataForm.url = response.data.url
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          createPointConfig(this.dataForm)
            .then(response => {
              // this.list.unshift(response.data.data)
              this.dialogFormVisible = false
              this.$notify.success({
                title: '成功',
                message: '创建成功'
              })
              // 刷新下页面
              this.getList()
            })
            .catch(response => {
              this.$notify.error({
                title: '失败',
                message: response.data.errmsg
              })
            })
        }
      })
    },
    handleUpdate(row) {
      this.dataForm = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          updatePointConfig(this.dataForm)
            .then(() => {
              for (const v of this.list) {
                if (v.id === this.dataForm.id) {
                  const index = this.list.indexOf(v)
                  this.list.splice(index, 1, this.dataForm)
                  break
                }
              }
              this.dialogFormVisible = false
              this.$notify.success({
                title: '成功',
                message: '更新成功'
              })
              // 刷新下页面
              this.getList()
            })
            .catch(response => {
              this.$notify.error({
                title: '失败',
                message: response.data.errmsg
              })
            })
        }
      })
    },
    handleDelete(row) {
      deletePointConfig(row)
        .then(response => {
          this.$notify.success({
            title: '成功',
            message: '删除成功'
          })
          // 刷新页面
          this.getList()
          // const index = this.list.indexOf(row)
          // this.list.splice(index, 1)
        })
        .catch(response => {
          this.$notify.error({
            title: '失败',
            message: response.data.errmsg
          })
        })
    }
  }
}
</script>
