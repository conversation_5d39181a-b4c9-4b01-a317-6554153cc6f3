# 地图圈定区域组件使用说明

## 概述

`map-select.vue` 是一个基于腾讯地图API的地图绘制组件，支持动态加载地图SDK，提供多种图形绘制功能和区域坐标获取功能。

## 功能特性

- ✅ 动态加载腾讯地图SDK（包含tools库）
- ✅ 多种绘制工具：点标记、折线、多边形、圆形、矩形、椭圆
- ✅ 圈定区域坐标获取
- ✅ 区域信息展示（类型、坐标点数量）
- ✅ 坐标数据导出（JSON格式）
- ✅ 坐标复制到剪贴板
- ✅ 清除绘制功能
- ✅ 加载状态提示
- ✅ 错误处理
- ✅ 事件回调

## 使用方法

### 1. 基本使用

```vue
<template>
  <div>
    <map-select
      @location-selected="handleLocationSelected"
      @area-selected="handleAreaSelected" />
  </div>
</template>

<script>
import MapSelect from './map-select.vue'

export default {
  components: {
    MapSelect
  },
  methods: {
    handleLocationSelected(location) {
      console.log('选择的位置:', location)
      // location 包含: { lat, lng, address }
    },

    handleAreaSelected(area) {
      console.log('圈定的区域:', area)
      // area 包含: { type, coordinates, pointCount }
    }
  }
}
</script>
```

### 2. 事件说明

#### location-selected
当用户选择位置时触发，返回位置信息对象：

```javascript
{
  lat: 39.908823,        // 纬度
  lng: 116.397470,       // 经度
  address: "位置描述"     // 地址描述
}
```

#### area-selected
当用户圈定区域并点击"获取区域坐标"按钮时触发，返回区域信息对象：

```javascript
{
  type: "多边形",         // 区域类型：多边形、矩形、圆形、椭圆
  coordinates: [         // 坐标数组
    { lat: 39.908823, lng: 116.397470 },
    { lat: 39.909823, lng: 116.398470 },
    // ... 更多坐标点
  ],
  pointCount: 36         // 坐标点数量
}
```

### 3. 绘制工具说明

组件提供6种绘制工具：

- **点标记**: 在地图上添加标记点
- **折线**: 绘制折线路径
- **多边形**: 绘制多边形区域（推荐用于圈定区域）
- **圆形**: 绘制圆形区域
- **矩形**: 绘制矩形区域
- **椭圆**: 绘制椭圆区域

### 4. 操作说明

- **绘制**: 鼠标左键点击及移动即可绘制图形
- **结束绘制**: 鼠标左键双击即可结束绘制折线、多边形会自动闭合；圆形、矩形、椭圆单击即可结束
- **中断**: 绘制过程中按下esc键可中断该过程
- **撤销**: 使用ctrl+z或鼠标右键可以在绘制线和多边形时撤销上个绘制点

### 5. 组件方法

组件内部提供以下方法：

- `getAreaCoordinates()`: 获取当前圈定区域的所有坐标点
- `clearCurrentArea()`: 清除当前绘制的区域
- `exportCoordinates()`: 导出坐标数据为JSON文件
- `copyCoordinates()`: 复制坐标到剪贴板
- `loadTencentMapSDK()`: 动态加载腾讯地图SDK
- `initMap()`: 初始化地图实例

### 6. 配置说明

#### 腾讯地图API Key
当前使用的API Key: `OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77`

如需更换，请修改 `loadTencentMapSDK()` 方法中的URL：

```javascript
script.src = 'https://map.qq.com/api/gljs?libraries=tools&v=1.exp&key=YOUR_API_KEY'
```

**注意**: 必须包含 `libraries=tools` 参数以支持绘制功能。

#### 默认地图中心
默认地图中心设置为北京（39.984104, 116.307503），可在 `initMap()` 方法中修改：

```javascript
this.map = new TMap.Map('container', {
  zoom: 12,
  center: new TMap.LatLng(39.984104, 116.307503), // 修改默认中心点
});
```

#### 圆形和椭圆坐标点数量
默认生成36个坐标点，可在相关方法中修改 `pointCount` 参数：

```javascript
generateCircleCoordinates(center, radius, pointCount = 36)  // 修改为需要的点数
generateEllipseCoordinates(ellipseData, pointCount = 36)   // 修改为需要的点数
```

## 样式自定义

组件提供了基本样式，可以通过以下CSS类进行自定义：

```css
.app-container {
  /* 容器样式 */
}

.loading-container {
  /* 加载状态样式 */
}

.map-container {
  /* 地图容器样式 */
  width: 100%;
  height: 400px;
}

.map-controls {
  /* 控制按钮样式 */
}
```

## 注意事项

1. **网络依赖**: 组件需要网络连接来加载腾讯地图SDK
2. **API Key**: 请确保使用有效的腾讯地图API Key
3. **浏览器兼容性**: 支持现代浏览器，IE需要polyfill
4. **内存管理**: 组件在销毁时会自动清理地图实例

## 错误处理

组件内置了以下错误处理：

- SDK加载失败提示
- 地图初始化失败处理
- 网络连接异常处理

## 示例文件

参考 `map-select-example.vue` 文件查看完整的使用示例。
