<template>
  <div class="test-container">
    <h2>ActiveType 测试页面</h2>

    <div class="test-info">
      <h3>当前状态：</h3>
      <p><strong>当前激活工具：</strong>{{ activeType }}</p>
      <p><strong>地图是否加载：</strong>{{ mapLoaded ? '是' : '否' }}</p>
      <p><strong>编辑器是否初始化：</strong>{{ editor ? '是' : '否' }}</p>
    </div>

    <div class="test-buttons">
      <h3>测试按钮：</h3>
      <el-button @click="testActiveType">测试 ActiveType</el-button>
      <el-button @click="testMapLayers">测试 MapLayers</el-button>
      <el-button @click="testEditor">测试 Editor</el-button>
      <el-button @click="clearAll">清除所有</el-button>
    </div>

    <div class="test-results">
      <h3>测试结果：</h3>
      <div v-for="(result, index) in testResults" :key="index" class="test-result">
        <span :class="result.success ? 'success' : 'error'">
          {{ result.message }}
        </span>
      </div>
    </div>

    <!-- 地图组件 -->
    <map-select
      ref="mapSelect"
      @area-selected="handleAreaSelected"
      @location-selected="handleLocationSelected" />
  </div>
</template>

<script>
import MapSelect from './map-select.vue'

export default {
  name: 'ActiveTypeTest',
  components: {
    MapSelect
  },
  data() {
    return {
      testResults: []
    }
  },
  computed: {
    activeType() {
      return this.$refs.mapSelect ? this.$refs.mapSelect.activeType : 'unknown'
    },
    mapLoaded() {
      return this.$refs.mapSelect ? this.$refs.mapSelect.mapLoaded : false
    },
    editor() {
      return this.$refs.mapSelect ? this.$refs.mapSelect.editor : null
    }
  },
  methods: {
    addTestResult(message, success = true) {
      this.testResults.push({
        message: `[${new Date().toLocaleTimeString()}] ${message}`,
        success
      })
    },

    testActiveType() {
      const mapSelect = this.$refs.mapSelect
      if (!mapSelect) {
        this.addTestResult('MapSelect 组件未找到', false)
        return
      }

      this.addTestResult(`当前 activeType: ${mapSelect.activeType}`)

      // 测试修改 activeType
      const originalType = mapSelect.activeType
      mapSelect.activeType = 'polygon'

      if (mapSelect.activeType === 'polygon') {
        this.addTestResult('ActiveType 修改成功: polygon')
        mapSelect.activeType = originalType
        this.addTestResult(`ActiveType 恢复为: ${originalType}`)
      } else {
        this.addTestResult('ActiveType 修改失败', false)
      }
    },

    testMapLayers() {
      const mapSelect = this.$refs.mapSelect
      if (!mapSelect) {
        this.addTestResult('MapSelect 组件未找到', false)
        return
      }

      if (mapSelect.mapLayers) {
        const layers = Object.keys(mapSelect.mapLayers)
        this.addTestResult(`MapLayers 存在，包含: ${layers.join(', ')}`)

        // 检查每个图层是否已初始化
        layers.forEach(layer => {
          if (mapSelect.mapLayers[layer]) {
            this.addTestResult(`图层 ${layer} 已初始化`)
          } else {
            this.addTestResult(`图层 ${layer} 未初始化`, false)
          }
        })
      } else {
        this.addTestResult('MapLayers 不存在', false)
      }
    },

    testEditor() {
      const mapSelect = this.$refs.mapSelect
      if (!mapSelect) {
        this.addTestResult('MapSelect 组件未找到', false)
        return
      }

      if (mapSelect.editor) {
        this.addTestResult('Editor 已初始化')

        try {
          const activeOverlay = mapSelect.editor.getActiveOverlay()
          if (activeOverlay) {
            this.addTestResult(`当前激活图层: ${activeOverlay.id}`)
          } else {
            this.addTestResult('无激活图层', false)
          }
        } catch (error) {
          this.addTestResult(`Editor 测试出错: ${error.message}`, false)
        }
      } else {
        this.addTestResult('Editor 未初始化', false)
      }
    },

    clearAll() {
      this.testResults = []
      this.addTestResult('测试结果已清除')
    },

    handleAreaSelected(area) {
      this.addTestResult(`区域选择事件触发: ${area.type}, ${area.pointCount} 个点`)
    },

    handleLocationSelected(location) {
      this.addTestResult(`位置选择事件触发: ${location.lat}, ${location.lng}`)
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.test-info, .test-buttons, .test-results {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.test-info h3, .test-buttons h3, .test-results h3 {
  margin-top: 0;
  color: #303133;
}

.test-buttons .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.test-result {
  margin: 5px 0;
  padding: 5px;
  border-radius: 3px;
}

.success {
  color: #67c23a;
}

.error {
  color: #f56c6c;
}
</style>
