<template>
  <div class="app-container">
    <h2>地图圈定区域组件使用示例</h2>

    <!-- 显示选中的位置信息 -->
    <div v-if="selectedLocation" class="location-info">
      <h3>已选择的位置：</h3>
      <p><strong>纬度：</strong>{{ selectedLocation.lat }}</p>
      <p><strong>经度：</strong>{{ selectedLocation.lng }}</p>
      <p><strong>地址：</strong>{{ selectedLocation.address }}</p>
    </div>

    <!-- 显示圈定区域信息 -->
    <div v-if="selectedArea" class="area-result">
      <h3>圈定区域信息：</h3>
      <p><strong>区域类型：</strong>{{ selectedArea.type }}</p>
      <p><strong>坐标点数量：</strong>{{ selectedArea.pointCount }}</p>
      <div class="coordinates-preview">
        <h4>坐标预览（前5个点）：</h4>
        <div v-for="(coord, index) in selectedArea.coordinates.slice(0, 5)" :key="index">
          点{{ index + 1 }}: {{ coord.lat.toFixed(6) }}, {{ coord.lng.toFixed(6) }}
        </div>
        <p v-if="selectedArea.coordinates.length > 5">
          ... 还有 {{ selectedArea.coordinates.length - 5 }} 个坐标点
        </p>
      </div>
    </div>

    <!-- 地图选择组件 -->
    <map-select
      @location-selected="handleLocationSelected"
      @area-selected="handleAreaSelected" />

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button :disabled="!selectedLocation && !selectedArea" type="success" @click="saveLocation">
        保存数据
      </el-button>
      <el-button @click="clearAll">
        清除所有
      </el-button>
      <el-button type="info" @click="showDataModal">
        查看完整数据
      </el-button>
    </div>

    <!-- 数据展示弹窗 -->
    <el-dialog :visible.sync="dataModalVisible" title="完整数据" width="60%">
      <div class="data-display">
        <h4>位置数据：</h4>
        <pre>{{ JSON.stringify(selectedLocation, null, 2) }}</pre>

        <h4>区域数据：</h4>
        <pre>{{ JSON.stringify(selectedArea, null, 2) }}</pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dataModalVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadData">下载数据</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import MapSelect from './map-select.vue'

export default {
  name: 'MapSelectExample',
  components: {
    MapSelect
  },
  data() {
    return {
      selectedLocation: null,
      selectedArea: null,
      dataModalVisible: false
    }
  },
  methods: {
    // 处理位置选择事件
    handleLocationSelected(location) {
      this.selectedLocation = location
      console.log('选择的位置:', location)
    },

    // 处理区域选择事件
    handleAreaSelected(area) {
      this.selectedArea = area
      console.log('圈定的区域:', area)
      this.$message.success(`已圈定${area.type}区域，包含${area.pointCount}个坐标点`)
    },

    // 保存数据
    saveLocation() {
      const dataToSave = {
        location: this.selectedLocation,
        area: this.selectedArea,
        timestamp: new Date().toISOString()
      }

      // 这里可以调用API保存数据
      this.$message.success('数据保存成功！')
      console.log('保存的数据:', dataToSave)
    },

    // 清除所有选择
    clearAll() {
      this.selectedLocation = null
      this.selectedArea = null
      this.$message.info('已清除所有选择')
    },

    // 显示数据弹窗
    showDataModal() {
      if (!this.selectedLocation && !this.selectedArea) {
        this.$message.warning('暂无数据可查看')
        return
      }
      this.dataModalVisible = true
    },

    // 下载数据
    downloadData() {
      const data = {
        location: this.selectedLocation,
        area: this.selectedArea,
        exportTime: new Date().toISOString()
      }

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `map-data-${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      this.$message.success('数据已下载')
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.location-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.location-info h3 {
  margin-top: 0;
  color: #333;
}

.location-info p {
  margin: 5px 0;
  color: #666;
}

.actions {
  margin-top: 20px;
  text-align: center;
}

.actions .el-button {
  margin: 0 10px;
}

.area-result {
  background: #e8f4fd;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border-left: 4px solid #409eff;
}

.area-result h3 {
  margin-top: 0;
  color: #409eff;
}

.area-result p {
  margin: 5px 0;
  color: #666;
}

.coordinates-preview {
  margin-top: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.coordinates-preview h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
}

.coordinates-preview div {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #606266;
  margin: 3px 0;
}

.data-display pre {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  color: #303133;
}
</style>
