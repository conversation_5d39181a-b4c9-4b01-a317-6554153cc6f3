<template>
  <div class="app-container">
    <div id="container"/>
    <div id="toolControl">
      <div id="marker" class="toolItem active" title="点标记"/>
      <div id="polyline" class="toolItem" title="折线"/>
      <div id="polygon" class="toolItem" title="多边形"/>
      <div id="circle" class="toolItem" title="圆形"/>
      <div id="rectangle" class="toolItem" title="矩形"/>
      <div id="ellipse" class="toolItem" title="椭圆"/>
    </div>

    <!-- 预设数据控制 -->
    <div class="preset-data-control">
      <h4>预设坐标点展示：</h4>
      <div class="action-buttons">
        <el-button type="success" @click="loadPresetData">
          <i class="el-icon-location" /> 加载预设坐标点
        </el-button>
        <el-button @click="clearPresetData">
          <i class="el-icon-close" /> 清除预设数据
        </el-button>
        <el-button type="info" @click="showPresetInfo">
          <i class="el-icon-info" />
          {{ showPresetCoordinates ? "隐藏" : "显示" }}坐标信息
        </el-button>
        <el-button type="primary" @click="connectPoints">
          <i class="el-icon-connection" /> 连接坐标点
        </el-button>
        <el-button type="warning" @click="createPolygon">
          <i class="el-icon-s-grid" /> 创建多边形区域
        </el-button>
        <el-button v-if="presetPolygon" type="info" @click="showPolygonInfo">
          <i class="el-icon-view" /> 查看多边形信息
        </el-button>
      </div>

      <!-- 预设坐标信息显示 -->
      <div v-if="showPresetCoordinates" class="preset-coordinates">
        <h5>预设坐标点列表（共{{ presetCoordinates.length }}个点）：</h5>
        <div class="coordinates-list">
          <div
            v-for="(coord, index) in presetCoordinates"
            :key="index"
            class="coordinate-item"
          >
            <span>点{{ index + 1 }}: </span>
            <span>{{ coord.lat }}, {{ coord.lng }}</span>
            <span v-if="coord.address" class="address-info">
              - {{ coord.address }}</span
              >
          </div>
        </div>
        <el-button
          size="small"
          @click="copyPresetCoordinates"
        >复制所有坐标</el-button
        >
      </div>
    </div>

    <div class="instructions">
      <h4>操作说明：</h4>
      <p>1. 点击"加载预设坐标点"按钮在地图上显示预设的7个坐标点</p>
      <p>2. 点击"连接坐标点"按钮将坐标点用线段连接起来</p>
      <p>3. 点击"创建多边形区域"按钮将坐标点构建成多边形区域</p>
      <p>4. 点击"显示坐标信息"查看详细的坐标数据和地址信息</p>
      <p>5. 可以使用绘制工具在地图上添加其他图形</p>
    </div>

    <!-- 区域信息显示 -->
    <div v-if="currentPolygonData" class="area-info">
      <h4>圈定区域信息：</h4>
      <p><strong>区域类型：</strong>{{ currentPolygonData.type }}</p>
      <p>
        <strong>坐标点数量：</strong>{{ currentPolygonData.coordinates.length }}
      </p>

      <div class="action-buttons">
        <el-button type="primary" @click="getAreaCoordinates">
          <i class="el-icon-download" /> 获取区域坐标
        </el-button>
        <el-button @click="clearCurrentArea">
          <i class="el-icon-delete" /> 清除当前区域
        </el-button>
        <el-button type="warning" @click="clearAllShapes">
          <i class="el-icon-refresh-left" /> 清除所有图形
        </el-button>
      </div>

      <!-- 坐标列表 -->
      <div v-if="showCoordinates" class="coordinates-display">
        <h5>区域边界坐标：</h5>
        <div class="coordinates-list">
          <div
            v-for="(coord, index) in currentPolygonData.coordinates"
            :key="index"
            class="coordinate-item"
          >
            <span>点{{ index + 1 }}: </span>
            <span>{{ coord.lat.toFixed(6) }}, {{ coord.lng.toFixed(6) }}</span>
          </div>
        </div>
        <el-button size="small" @click="copyCoordinates">复制坐标</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
html,
body {
  height: 100%;
  margin: 0px;
  padding: 0px;
}

#container {
  width: 80%;
  height: 50vh;
  margin: auto;
}

#toolControl {
  position: absolute;
  top: 10px;
  left: 0px;
  right: 0px;
  margin: auto;
  width: 252px;
  z-index: 1001;
}

.toolItem {
  width: 30px;
  height: 30px;
  float: left;
  margin: 1px;
  padding: 4px;
  border-radius: 3px;
  background-size: 30px 30px;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 1px 2px 0 #e4e7ef;
  background-color: #ffffff;
  border: 1px solid #ffffff;
}

.toolItem:hover {
  border-color: #789cff;
}

.active {
  border-color: #d5dff2;
  background-color: #d5dff2;
}

#marker {
  background-image: url("https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/marker_editor.png");
}

#polyline {
  background-image: url("https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/polyline.png");
}

#polygon {
  background-image: url("https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/polygon.png");
}

#circle {
  background-image: url("https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/circle.png");
}

#rectangle {
  background-image: url("https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/rectangle.png");
}

#ellipse {
  background-image: url("https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/ellipse.png");
}

/* 预设数据样式 */
.preset-data-control {
  margin: 20px 0;
  padding: 20px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.preset-data-control h4 {
  margin-top: 0;
  color: #1f2937;
  border-bottom: 1px solid #b3d8ff;
  padding-bottom: 10px;
}

.preset-coordinates {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #b3d8ff;
}

.preset-coordinates h5 {
  margin-bottom: 10px;
  color: #1f2937;
}

.instructions {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.instructions h4 {
  margin-top: 0;
  color: #303133;
}

.instructions p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.area-info {
  margin: 20px 0;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.area-info h4 {
  margin-top: 0;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.area-info p {
  margin: 10px 0;
  color: #606266;
}

.action-buttons {
  margin: 15px 0;
}

.action-buttons .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.coordinates-display {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.coordinates-display h5 {
  margin-bottom: 10px;
  color: #303133;
}

.coordinates-list {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.coordinate-item {
  padding: 5px 0;
  border-bottom: 1px solid #e4e7ed;
  font-family: "Courier New", monospace;
  font-size: 13px;
}

.coordinate-item:last-child {
  border-bottom: none;
}

.coordinate-item span:first-child {
  color: #909399;
  width: 60px;
  display: inline-block;
}

.coordinate-item span:nth-child(2) {
  color: #303133;
  font-weight: 500;
}

.address-info {
  color: #6b7280 !important;
  font-size: 12px;
  font-weight: normal !important;
}
</style>

<script>
export default {
  name: 'MapSelect',
  data() {
    return {
      mapLoaded: false,
      map: null,
      marker: null,
      editor: null,
      selectedLocation: {
        lat: 39.908823,
        lng: 116.39747,
        address: ''
      },
      // 圈定区域相关数据
      currentPolygonData: null,
      showCoordinates: false,
      drawnShapes: [], // 存储所有绘制的图形
      activeType: 'marker', // 当前激活的绘制工具

      // 地图图层对象
      mapLayers: {
        marker: null,
        polyline: null,
        polygon: null,
        circle: null,
        rectangle: null,
        ellipse: null
      },

      // 预设坐标数据
      presetCoordinates: [
        { lat: 39.994244, lng: 116.356582, address: '' },
        { lat: 39.986619, lng: 116.346887, address: '' },
        { lat: 39.981623, lng: 116.360528, address: '' },
        { lat: 39.988263, lng: 116.369965, address: '' },
        { lat: 39.994835, lng: 116.367049, address: '' },
        { lat: 39.994244, lng: 116.356582, address: '' },
        { lat: 39.994244, lng: 116.356582, address: '' }
      ],
      showPresetCoordinates: false,
      presetMarkers: [], // 存储预设标记点
      presetPolyline: null, // 存储连接线
      presetPolygon: null // 存储预设多边形
    }
  },
  mounted() {
    this.loadTencentMapSDK()
  },
  beforeDestroy() {
    // 清理地图实例
    if (this.map) {
      this.map.destroy()
    }
  },
  methods: {
    // 动态加载腾讯地图SDK
    loadTencentMapSDK() {
      return new Promise((resolve, reject) => {
        // 检查是否已经加载过
        if (window.TMap) {
          this.mapLoaded = true
          this.$nextTick(() => {
            this.initMap()
          })
          resolve()
          return
        }

        // 检查是否已经有相同的script标签在加载
        const existingScript = document.querySelector(
          'script[src*="map.qq.com/api/gljs"]'
        )
        if (existingScript) {
          // 如果已经在加载，等待加载完成
          existingScript.addEventListener('load', () => {
            this.mapLoaded = true
            this.$nextTick(() => {
              this.initMap()
            })
            resolve()
          })
          return
        }

        // 创建script标签
        const script = document.createElement('script')
        script.type = 'text/javascript'
        script.src =
          'https://map.qq.com/api/gljs?libraries=tools&v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77'

        // 加载成功回调
        script.onload = () => {
          // 等待一小段时间确保TMap对象完全初始化
          setTimeout(() => {
            this.mapLoaded = true
            this.$nextTick(() => {
              this.initMap()
            })
            resolve()
          }, 100)
        }

        // 加载失败回调
        script.onerror = () => {
          this.$message.error('地图SDK加载失败，请检查网络连接')
          reject(new Error('地图SDK加载失败'))
        }

        // 添加到页面
        document.head.appendChild(script)
      })
    },

    // 初始化地图
    initMap() {
      // 使用类型断言来避免TypeScript警告
      const TMap = window.TMap
      if (!TMap) {
        this.$message.error('地图SDK未正确加载')
        return
      }

      // 初始化地图，使用预设坐标的中心点
      const centerLat =
        this.presetCoordinates.length > 0
          ? this.presetCoordinates[0].lat
          : 39.984104
      const centerLng =
        this.presetCoordinates.length > 0
          ? this.presetCoordinates[0].lng
          : 116.307503

      this.map = new TMap.Map('container', {
        zoom: 13, // 设置地图缩放级别
        center: new TMap.LatLng(centerLat, centerLng) // 设置地图中心点坐标
      })

      document.getElementById('toolControl').addEventListener('click', e => {
        var id = e.target.id
        if (id !== 'toolControl') {
          document.getElementById(this.activeType).className = 'toolItem'
          document.getElementById(id).className = 'toolItem active'
          this.activeType = id
          console.log('id:', id)
          this.editor.setActiveOverlay(id)
        }
      })

      // 初始化几何图形及编辑器
      this.mapLayers.marker = new TMap.MultiMarker({
        map: this.map
      })
      this.mapLayers.polyline = new TMap.MultiPolyline({
        map: this.map
      })
      this.mapLayers.polygon = new TMap.MultiPolygon({
        map: this.map
      })
      this.mapLayers.circle = new TMap.MultiCircle({
        map: this.map
      })
      this.mapLayers.rectangle = new TMap.MultiRectangle({
        map: this.map
      })
      this.mapLayers.ellipse = new TMap.MultiEllipse({
        map: this.map
      })

      this.editor = new TMap.tools.GeometryEditor({
        map: this.map, // 编辑器绑定的地图对象
        overlayList: [
          {
            overlay: this.mapLayers.marker,
            id: 'marker'
          },
          {
            overlay: this.mapLayers.polyline,
            id: 'polyline'
          },
          {
            overlay: this.mapLayers.polygon,
            id: 'polygon'
          },
          {
            overlay: this.mapLayers.circle,
            id: 'circle'
          },
          {
            overlay: this.mapLayers.rectangle,
            id: 'rectangle'
          },
          {
            overlay: this.mapLayers.ellipse,
            id: 'ellipse'
          }
        ],
        actionMode: TMap.tools.constants.EDITOR_ACTION.DRAW, // 编辑器的工作模式
        activeOverlayId: 'marker', // 激活图层
        snappable: true // 开启吸附
      })

      // 监听绘制结束事件，获取绘制几何图形
      this.editor.on('draw_complete', geometry => {
        var id = geometry.id
        var activeOverlayId = this.editor.getActiveOverlay().id

        // 处理不同类型的图形
        this.handleDrawnGeometry(activeOverlayId, id)
      })

      // 地图初始化完成后，自动加载预设数据
      this.$nextTick(() => {
        setTimeout(() => {
          this.loadPresetData()
        }, 500)
      })
    },

    // 处理绘制完成的图形
    handleDrawnGeometry(activeOverlayId, id) {
      let geometryData = null

      if (activeOverlayId === 'rectangle') {
        const geo = this.mapLayers.rectangle.geometries.filter(
          item => item.id === id
        )
        if (geo.length > 0) {
          geometryData = {
            type: '矩形',
            id: id,
            coordinates: this.convertPathsToCoordinates(geo[0].paths),
            rawData: geo[0]
          }
        }
      } else if (activeOverlayId === 'polygon') {
        const geo = this.mapLayers.polygon.geometries.filter(
          item => item.id === id
        )
        if (geo.length > 0) {
          geometryData = {
            type: '多边形',
            id: id,
            coordinates: this.convertPathsToCoordinates(geo[0].paths),
            rawData: geo[0]
          }
        }
      } else if (activeOverlayId === 'circle') {
        const geo = this.mapLayers.circle.geometries.filter(
          item => item.id === id
        )
        if (geo.length > 0) {
          const circleCoords = this.generateCircleCoordinates(
            geo[0].center,
            geo[0].radius
          )
          geometryData = {
            type: '圆形',
            id: id,
            coordinates: circleCoords,
            rawData: geo[0]
          }
        }
      }

      if (geometryData) {
        this.currentPolygonData = geometryData
        this.drawnShapes.push(geometryData)
      }
    },

    // 加载预设坐标数据
    async loadPresetData() {
      if (!this.map || !window.TMap) {
        this.$message.error('地图未初始化完成')
        return
      }

      try {
        // 清除之前的预设标记
        this.clearPresetData()

        // 为每个坐标点获取地址信息并创建标记
        const markersData = []

        for (let i = 0; i < this.presetCoordinates.length; i++) {
          const coord = this.presetCoordinates[i]

          // 调用腾讯地图逆地理编码API获取地址
          const address = await this.getAddressByCoordinate(
            coord.lat,
            coord.lng
          )

          // 更新坐标点的地址信息
          this.presetCoordinates[i].address = address

          // 创建标记数据
          markersData.push({
            id: `preset-marker-${i}`,
            position: new window.TMap.LatLng(coord.lat, coord.lng),
            properties: {
              title: `预设点${i + 1}`,
              address: address
            }
          })
        }

        // 在地图上添加标记点
        this.mapLayers.marker.add(markersData)

        // 设置地图中心为第一个坐标点
        if (this.presetCoordinates.length > 0) {
          const firstCoord = this.presetCoordinates[0]
          this.map.setCenter(
            new window.TMap.LatLng(firstCoord.lat, firstCoord.lng)
          )
          this.map.setZoom(13)
        }

        this.presetMarkers = markersData
        this.$message.success(
          `已加载 ${this.presetCoordinates.length} 个预设坐标点`
        )
      } catch (error) {
        console.error('加载预设数据失败:', error)
        this.$message.error('加载预设数据失败，请重试')
      }
    },

    // 连接坐标点
    connectPoints() {
      if (!this.map || !window.TMap || this.presetCoordinates.length < 2) {
        this.$message.warning('需要至少2个坐标点才能连接')
        return
      }

      try {
        // 清除之前的连接线
        if (this.presetPolyline) {
          this.mapLayers.polyline.remove([this.presetPolyline.id])
        }

        // 创建路径点
        const path = this.presetCoordinates.map(
          coord => new window.TMap.LatLng(coord.lat, coord.lng)
        )

        // 创建折线
        const polylineData = {
          id: 'preset-polyline',
          paths: path,
          properties: {
            color: '#3777FF',
            width: 3,
            borderWidth: 1,
            borderColor: '#FFF'
          }
        }

        this.mapLayers.polyline.add([polylineData])
        this.presetPolyline = polylineData

        this.$message.success('已连接所有坐标点')
      } catch (error) {
        console.error('连接坐标点失败:', error)
        this.$message.error('连接坐标点失败')
      }
    },

    // 创建多边形区域
    createPolygon() {
      if (!this.map || !window.TMap || this.presetCoordinates.length < 3) {
        this.$message.warning('需要至少3个坐标点才能创建多边形')
        return
      }

      try {
        // 清除之前的多边形
        if (this.presetPolygon) {
          this.mapLayers.polygon.remove([this.presetPolygon.id])
        }

        // 去除重复的坐标点，创建有效的多边形路径
        const uniqueCoords = []
        const seen = new Set()

        for (const coord of this.presetCoordinates) {
          const key = `${coord.lat},${coord.lng}`
          if (!seen.has(key)) {
            seen.add(key)
            uniqueCoords.push(coord)
          }
        }

        if (uniqueCoords.length < 3) {
          this.$message.warning('去除重复点后，坐标点不足3个，无法创建多边形')
          return
        }

        // 创建多边形路径点
        const polygonPath = uniqueCoords.map(
          coord => new window.TMap.LatLng(coord.lat, coord.lng)
        )

        // 创建多边形数据
        const polygonData = {
          id: 'preset-polygon',
          paths: polygonPath,
          properties: {
            color: 'rgba(55, 119, 255, 0.3)', // 半透明蓝色填充
            strokeColor: '#3777FF', // 边框颜色
            strokeWidth: 2, // 边框宽度
            strokeDashArray: [5, 5] // 虚线边框
          }
        }

        // 添加多边形到地图
        this.mapLayers.polygon.add([polygonData])
        this.presetPolygon = polygonData

        // 设置当前多边形数据用于显示信息
        this.currentPolygonData = {
          type: '预设多边形',
          id: 'preset-polygon',
          coordinates: uniqueCoords,
          rawData: polygonData
        }

        // 计算多边形面积（简单估算）
        const area = this.calculatePolygonArea(uniqueCoords)

        this.$message.success(
          `已创建多边形区域，包含${
            uniqueCoords.length
          }个顶点，估算面积约${area.toFixed(2)}平方米`
        )
      } catch (error) {
        console.error('创建多边形失败:', error)
        this.$message.error('创建多边形失败')
      }
    },

    // 计算多边形面积（使用鞋带公式的简化版本）
    calculatePolygonArea(coordinates) {
      if (coordinates.length < 3) return 0

      let area = 0
      const earthRadius = 6371000 // 地球半径（米）

      // 将经纬度转换为弧度并计算面积
      for (let i = 0; i < coordinates.length; i++) {
        const j = (i + 1) % coordinates.length
        const lat1 = (coordinates[i].lat * Math.PI) / 180
        const lng1 = (coordinates[i].lng * Math.PI) / 180
        const lat2 = (coordinates[j].lat * Math.PI) / 180
        const lng2 = (coordinates[j].lng * Math.PI) / 180

        area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2))
      }

      area = (Math.abs(area) * earthRadius * earthRadius) / 2
      return area
    },

    // 显示多边形信息
    showPolygonInfo() {
      if (
        this.currentPolygonData &&
        this.currentPolygonData.type === '预设多边形'
      ) {
        this.showCoordinates = !this.showCoordinates

        if (this.showCoordinates) {
          const area = this.calculatePolygonArea(
            this.currentPolygonData.coordinates
          )
          this.$message.info(
            `多边形包含${
              this.currentPolygonData.coordinates.length
            }个顶点，估算面积约${area.toFixed(2)}平方米`
          )
        }
      } else {
        this.$message.warning('请先创建多边形区域')
      }
    },

    // 清除预设数据
    clearPresetData() {
      if (this.presetMarkers.length > 0 && this.mapLayers.marker) {
        // 从地图上移除预设标记
        const markerIds = this.presetMarkers.map(marker => marker.id)
        this.mapLayers.marker.remove(markerIds)
        this.presetMarkers = []
      }

      // 清除连接线
      if (this.presetPolyline && this.mapLayers.polyline) {
        this.mapLayers.polyline.remove([this.presetPolyline.id])
        this.presetPolyline = null
      }

      // 清除多边形
      if (this.presetPolygon && this.mapLayers.polygon) {
        this.mapLayers.polygon.remove([this.presetPolygon.id])
        this.presetPolygon = null
      }

      // 如果当前显示的是预设多边形，也清除
      if (
        this.currentPolygonData &&
        this.currentPolygonData.id === 'preset-polygon'
      ) {
        this.currentPolygonData = null
        this.showCoordinates = false
      }

      this.showPresetCoordinates = false
      this.$message.info('已清除预设数据')
    },

    // 显示预设坐标信息
    showPresetInfo() {
      this.showPresetCoordinates = !this.showPresetCoordinates

      if (
        this.showPresetCoordinates &&
        this.presetCoordinates.some(coord => !coord.address)
      ) {
        this.$message.info('部分地址信息可能需要先加载预设数据才能显示')
      }
    },

    // 根据坐标获取地址（腾讯地图逆地理编码）
    async getAddressByCoordinate(lat, lng) {
      try {
        // 使用腾讯地图的逆地理编码服务
        const response = await fetch(
          `https://apis.map.qq.com/ws/geocoder/v1/?location=${lat},${lng}&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&get_poi=1`
        )

        if (!response.ok) {
          throw new Error('网络请求失败')
        }

        const data = await response.json()

        if (data.status === 0 && data.result) {
          return (
            data.result.formatted_addresses.recommend ||
            data.result.address ||
            '地址解析失败'
          )
        } else {
          return '地址解析失败'
        }
      } catch (error) {
        console.error('地址解析错误:', error)
        return `坐标: ${lat.toFixed(6)}, ${lng.toFixed(6)}`
      }
    },

    // 复制预设坐标
    copyPresetCoordinates() {
      const coordsText = this.presetCoordinates
        .map(
          (coord, index) =>
            `点${index + 1}: ${coord.lat}, ${coord.lng}${
              coord.address ? ' - ' + coord.address : ''
            }`
        )
        .join('\n')

      this.copyToClipboard(coordsText)
    },

    // 转换路径数据为坐标数组
    convertPathsToCoordinates(paths) {
      if (!paths || !Array.isArray(paths)) return []

      return paths.map(path => ({
        lat: path.lat,
        lng: path.lng
      }))
    },

    // 生成圆形周边坐标点
    generateCircleCoordinates(center, radius, pointCount = 36) {
      const coordinates = []
      const angleStep = (2 * Math.PI) / pointCount
      const earthRadius = 6371000

      for (let i = 0; i < pointCount; i++) {
        const angle = i * angleStep
        const deltaLat =
          ((radius * Math.cos(angle)) / earthRadius) * (180 / Math.PI)
        const deltaLng =
          ((radius * Math.sin(angle)) /
            (earthRadius * Math.cos((center.lat * Math.PI) / 180))) *
          (180 / Math.PI)

        coordinates.push({
          lat: center.lat + deltaLat,
          lng: center.lng + deltaLng
        })
      }

      return coordinates
    },

    // 获取区域坐标
    getAreaCoordinates() {
      if (!this.currentPolygonData) {
        this.$message.warning('请先圈定一个区域')
        return
      }

      this.showCoordinates = true
      this.$emit('area-selected', {
        type: this.currentPolygonData.type,
        coordinates: this.currentPolygonData.coordinates,
        pointCount: this.currentPolygonData.coordinates.length
      })

      this.$message.success(
        `已获取${this.currentPolygonData.type}区域的${
          this.currentPolygonData.coordinates.length
        }个坐标点`
      )
    },

    // 清除当前区域
    clearCurrentArea() {
      if (this.currentPolygonData) {
        this.removeShapeFromMap(
          this.currentPolygonData.id,
          this.currentPolygonData.type
        )
        this.currentPolygonData = null
        this.showCoordinates = false
        this.$message.info('已清除当前区域')
      }
    },

    // 从地图上移除指定图形
    removeShapeFromMap(shapeId, shapeType) {
      if (!this.mapLayers || !shapeId) return

      const layerMap = {
        矩形: 'rectangle',
        多边形: 'polygon',
        圆形: 'circle',
        椭圆: 'ellipse',
        折线: 'polyline',
        点标记: 'marker'
      }

      const layerKey = layerMap[shapeType]
      if (layerKey && this.mapLayers[layerKey]) {
        const layer = this.mapLayers[layerKey]
        if (layer.geometries) {
          layer.geometries = layer.geometries.filter(
            item => item.id !== shapeId
          )
          layer.updateGeometries(layer.geometries)
        }
      }
    },

    // 清除所有绘制的图形
    clearAllShapes() {
      if (this.mapLayers) {
        Object.keys(this.mapLayers).forEach(key => {
          if (this.mapLayers[key] && this.mapLayers[key].geometries) {
            this.mapLayers[key].geometries = []
            this.mapLayers[key].updateGeometries([])
          }
        })
      }

      this.currentPolygonData = null
      this.showCoordinates = false
      this.drawnShapes = []
      this.$message.info('已清除所有绘制图形')
    },

    // 复制坐标到剪贴板
    copyCoordinates() {
      if (!this.currentPolygonData) return

      const coordsText = this.currentPolygonData.coordinates
        .map(
          (coord, index) =>
            `点${index + 1}: ${coord.lat.toFixed(6)}, ${coord.lng.toFixed(6)}`
        )
        .join('\n')

      this.copyToClipboard(coordsText)
    },

    // 通用复制到剪贴板方法
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            this.$message.success('坐标已复制到剪贴板')
          })
          .catch(() => {
            this.fallbackCopyToClipboard(text)
          })
      } else {
        this.fallbackCopyToClipboard(text)
      }
    },

    // 兼容性复制方法
    fallbackCopyToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('坐标已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    }
  }
}
</script>
