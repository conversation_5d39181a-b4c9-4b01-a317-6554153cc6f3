<template>
  <div class="app-container">
    <!-- 店铺选择Tab -->
    <div class="shop-tabs">
      <el-tabs v-model="activeShopId" @tab-click="handleShopChange">
        <el-tab-pane
          v-for="shop in shopList"
          :key="shop.id"
          :label="shop.shopName"
          :name="shop.id.toString()"/>
      </el-tabs>
    </div>

    <!-- 当前店铺设置内容 -->
    <div v-if="currentShop" class="shop-content">
      <div class="shop-status">
        <label>店铺营业状态：</label>
        <el-radio v-model="isOpen" label="true">营业</el-radio>
        <el-radio v-model="isOpen" label="false">休息</el-radio>
        <div v-if="isOpen==='false'" class="shop-status-mask">
          <div style="margin-right:'20px;width:120px">休业描述</div>
          <el-input v-model="statusDes" style="max-width:70%"/>
        </div>
        <br >
        <el-button class="shop-status-btn" @click="setShopStatus">保存</el-button>
      </div>
      <div class="shop-week-status">
        <div v-for="(row,index) in dates" :key="index" class="shop-week-day">
          <label style="margin-right:10px">{{ dayIn[row.dayOfWeek] }}</label>
          <el-time-select
            v-model="row.openingTimeDesc"
            :picker-options="{
              start: '08:30',
              step: '00:15',
              end: '23:30'
            }"
            placeholder="起始时间"/>
          <el-time-select
            v-model="row.closingTimeDesc"
            :picker-options="{
              start: '08:30',
              step: '00:15',
              end: '23:30',
              minTime: row.startTime
            }"
            style="margin-right:20px"
            placeholder="结束时间"/>
          <el-switch
            v-model="row.isOpen"
            active-color="#13ce66"
            active-text="开业"
            inactive-text="休息"
            inactive-color="#ff4949"/>
        </div>
        <el-button style="margin:auto;display:block;margin-top:20px" type="primary" @click="setShopTimes">保存</el-button>
      </div>
    </div>

    <!-- 无店铺时的提示 -->
    <div v-else class="no-shop-tip">
      <el-alert
        title="暂无店铺信息"
        type="info"
        description="请先添加店铺信息后再进行设置"
        show-icon/>
    </div>
  </div>
</template>
<style scoped>
   .app-container{
    .shop-tabs{
    }
    .shop-content{
    }
    .no-shop-tip{
        padding: 20px 20px;
        text-align: center;
    }
    .shop-status{
        position: relative;
        padding: 15px 10px 15px 10px;
       .shop-status-mask{
        display: flex;
        flex-direction: row;
        align-items: center;
        margin: 10px 0;
       }
       .shop-status-btn{
        position: absolute;
        right: 20px;
        top: 30%;
       }
    }
    .shop-week-status{
        border-top: 1px solid #ccc;
        padding-top: 15px;
        .shop-week-day{
            padding: 5px 10px;

        }
    }
   }
</style>

<script>
import { setShopStatus, setShopTimes, getshopDetail, getShopList } from '@/api/shop/index'
//   import VeLine from 'v-charts/lib/line'

export default {
  components: { },
  data() {
    return {
      // 店铺列表和当前选中的店铺
      shopList: [],
      activeShopId: '',
      currentShop: null,

      // 当前店铺的设置数据
      isOpen: 'true',
      statusDes: '',
      dayIn: {
        1: '星期一',
        2: '星期二',
        3: '星期三',
        4: '星期四',
        5: '星期五',
        6: '星期六',
        7: '星期日'
      },
      dates: []
    }
  },
  created() {
    this.getShopListData()
  },
  methods: {
    // 获取店铺列表
    async getShopListData() {
      try {
        const { data } = await getShopList()
        if (data.errno === 0) {
          this.shopList = data.data || []
          if (this.shopList.length > 0) {
            this.activeShopId = this.shopList[0].id.toString()
            this.currentShop = this.shopList[0]
            this.getDetail()
          }
        } else {
          this.$message.error(data.errMsg || '获取店铺列表失败')
        }
      } catch (error) {
        this.$message.error('获取店铺列表失败')
        console.error('获取店铺列表错误:', error)
      }
    },

    // Tab切换处理
    handleShopChange(tab) {
      const shopId = parseInt(tab.name)
      this.currentShop = this.shopList.find(shop => shop.id === shopId)
      if (this.currentShop) {
        this.getDetail()
      }
    },

    // 初始化当前店铺的时间设置数据
    initDatesForCurrentShop() {
      if (!this.currentShop) return

      this.dates = []
      for (let i = 1; i <= 7; i++) {
        this.dates.push({
          shopId: this.currentShop.id,
          dayOfWeek: i,
          openingTimeDesc: '',
          closingTimeDesc: '',
          isOpen: true
        })
      }
    },

    setShopStatus() {
      if (!this.currentShop) {
        this.$message.error('请先选择店铺')
        return
      }

      setShopStatus({
        shopId: this.currentShop.id,
        isOpen: this.isOpen === 'true',
        statusDes: this.statusDes,
        dayOfWeek: 0
      }).then(res => {
        if (res.data.errno === 0) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.errMsg)
        }
      }).catch(error => {
        this.$message.error('保存失败')
        console.error('保存店铺状态错误:', error)
      })
    },
    setShopTimes() {
      if (!this.currentShop) {
        this.$message.error('请先选择店铺')
        return
      }

      setShopTimes({
        shopId: this.currentShop.id,
        reqVOList: this.dates
      }).then(res => {
        if (res.data.errno === 0) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.errMsg)
        }
      }).catch(error => {
        this.$message.error('保存失败')
        console.error('保存店铺时间错误:', error)
      })
    },
    getDetail() {
      if (!this.currentShop) {
        this.initDatesForCurrentShop()
        return
      }

      getshopDetail({
        shopId: this.currentShop.id
      }).then(({ data }) => {
        if (data.errno === 0) {
          if (data.data.shopHourSetting && data.data.shopHourSetting.length > 0) {
            data.data.shopHourSetting.map((item) => {
              if (item.isOpen) {
                const str = item.closingTime.split(':')
                str.splice(2, 1)
                item.closingTimeDesc = str.join(':')

                const str1 = item.openingTime.split(':')
                str1.splice(2, 1)
                item.openingTimeDesc = str1.join(':')
              }
            })
            this.dates = data.data.shopHourSetting
          } else {
            // 如果没有设置数据，初始化默认数据
            this.initDatesForCurrentShop()
          }

          if (data.data.shopInfo) {
            this.isOpen = data.data.shopInfo.isOpen ? 'true' : 'false'
            this.statusDes = data.data.shopInfo.statusDesc || ''
          }
        } else {
          this.$message.error(data.errMsg || '获取店铺详情失败')
          this.initDatesForCurrentShop()
        }
      }).catch(error => {
        this.$message.error('获取店铺详情失败')
        console.error('获取店铺详情错误:', error)
        this.initDatesForCurrentShop()
      })
    }
  }

}
</script>
