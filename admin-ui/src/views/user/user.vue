<template>
  <div class="app-container">

    <!-- 查询和其他操作 -->
    <div class="filter-container">
      <el-input v-model="listQuery.username" clearable size="small" class="filter-item" style="width: 200px;" placeholder="请输入用户名"/>
      <el-input v-model="listQuery.mobile" clearable size="small" class="filter-item" style="width: 200px;" placeholder="请输入手机号"/>
      <el-button class="filter-item" type="primary" size="mini" icon="el-icon-search" @click="handleFilter">查找</el-button>
      <el-button :loading="downloadLoading" size="mini" class="filter-item" type="warning" icon="el-icon-download" @click="handleDownload">导出</el-button>
    </div>

    <!-- 查询结果 -->
    <el-table v-loading="listLoading" :data="list" size="small" element-loading-text="正在查询中..." border fit highlight-current-row>
      <el-table-column align="center" width="100px" label="用户ID" prop="id" sortable/>

      <el-table-column align="center" label="用户名" prop="nickname"/>

      <el-table-column align="center" label="手机号码" prop="mobile"/>

      <el-table-column align="center" label="性别" prop="gender">
        <template slot-scope="scope">
          {{ genderDic[scope.row.gender] }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="生日" prop="birthday"/>

      <el-table-column align="center" label="用户等级" prop="userLevel">
        <template slot-scope="scope">
          {{ levelDic[scope.row.userLevel] }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="积分" prop="points"/>
      <el-table-column align="center" label="余额" prop="balance"/>

      <el-table-column align="center" label="状态" prop="status">
        <template slot-scope="scope">
          {{ statusDic[scope.row.status] }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" class-name="small-padding fixed-width" title="无">
        <el-row slot-scope="scope">
          <el-button type="primary" @click="handlePoints(scope.row)">修改积分</el-button>
          <el-button type="primary" @click="handleBalance(scope.row)">修改余额</el-button>
        </el-row>
      </el-table-column>

    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList"/>

    <!-- 详情对话框 -->
    <el-dialog :visible.sync="detailDialogVisible" title="代理详情" width="700">
      <el-form :data="agencyDetail" label-position="left">
        <el-form-item label="佣金比例(%)">
          <span>{{ agencyDetail.settlementRate }}</span>
        </el-form-item>
        <el-form-item label="推广二维码">
          <img :src="agencyDetail.shareUrl" width="300">
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 代理审批 -->
    <el-dialog :visible.sync="approveDialogVisible" title="代理审批">
      <el-form ref="approveForm" :model="approveForm" status-icon label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="佣金比例(%)" prop="settlementRate">
          <el-input v-model="approveForm.settlementRate"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmApprove">确定</el-button>
      </div>
    </el-dialog>
    <!-- 修改积分 -->
    <el-dialog :visible.sync="pointDialogVisible" title="修改积分">
      <el-form ref="pointForm" :model="pointForm" label-width="120px" status-icon label-position="left" style="width: 400px; margin-left:50px;">
        <el-form-item label="id" prop="settlementRate">
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ pointForm.id }}
        </el-form-item>
        <el-form-item label="昵称" prop="settlementRate">
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ pointForm.nickname }}
        </el-form-item>
        <el-form-item label="手机号" prop="settlementRate">
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ pointForm.mobile }}
        </el-form-item>
        <el-form-item label="积分" >
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ pointForm.points }}
        </el-form-item>
        <el-form-item label="添加/扣减积分" prop="settlementRate">
          <el-input v-model="pointForm.changeAmount"/>
        </el-form-item>
        <!-- <el-form-item label="是否增加积分" prop="settlementRate">
                <el-input v-model="pointForm.changeAmount"/>
                </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pointDialogVisible = false;pointForm.changeAmount = 0">取消</el-button>
        <el-button type="primary" @click="updatePoints">确定</el-button>
      </div>
    </el-dialog>
    <!-- 修改余额 -->
    <el-dialog :visible.sync="balanceDialogVisible" title="修改余额">
      <el-form ref="balanceForm" :model="balanceForm" label-width="120px" status-icon label-position="left" style="width: 400px; margin-left:50px;">
        <el-form-item label="id" >
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ balanceForm.id }}
        </el-form-item>
        <el-form-item label="昵称" >
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ balanceForm.nickname }}
        </el-form-item>
        <el-form-item label="手机号" >
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ balanceForm.mobile }}
        </el-form-item>
        <el-form-item label="余额" >
          <!-- <el-input v-model="pointForm.points"/> -->
          {{ balanceForm.balance }}
        </el-form-item>
        <el-form-item label="添加/扣减余额" prop="settlementRate">
          <el-input v-model="balanceForm.changeAmount"/>
        </el-form-item>
        <!-- <el-form-item label="是否同步积分" >
         <el-radio-group v-model="isAddPoint">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="balanceDialogVisible = false;balanceForm.changeAmount =0;isAddPoint = true;">取消</el-button>
        <el-button type="primary" @click="updateBalance">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchList, approveAgency, detailApprove, manualChangeBalance, manualChangePoint } from '@/api/business/user'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination

export default {
  name: 'User',
  components: { Pagination },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        username: undefined,
        mobile: undefined,
        sort: 'add_time',
        order: 'desc'
      },
      downloadLoading: false,
      genderDic: ['未知', '男', '女'],
      levelDic: ['普通用户', 'VIP用户', '代理'],
      statusDic: ['可用', '禁用', '注销', '代理申请'],
      detailDialogVisible: false,
      agencyDetail: {},
      approveDialogVisible: false,
      approveForm: {
        userId: undefined,
        settlementRate: undefined
      },
      balanceDialogVisible: false,
      balanceForm: {
        balance: 0
      },
      pointDialogVisible: false,
      pointForm: {
        points: 0
      },
      isAddPoint: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        this.list = response.data.data.items
        this.total = response.data.data.total
        this.listLoading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleDetail(row) {
      this.agencyDetail = {
        shareUrl: undefined,
        settlementRate: undefined
      }
      detailApprove(row.id).then(response => {
        this.agencyDetail = response.data.data
      })
      this.detailDialogVisible = true
    },
    handleApproveAgency(row) {
      this.approveForm.userId = row.id

      this.approveDialogVisible = true
      this.$nextTick(() => {
        this.$refs['approveForm'].clearValidate()
      })
    },
    confirmApprove() {
      this.$refs['approveForm'].validate((valid) => {
        if (valid) {
          approveAgency(this.approveForm).then(response => {
            this.approveDialogVisible = false
            this.$notify.success({
              title: '成功',
              message: '审批成功'
            })
            this.getList()
          }).catch(response => {
            this.$notify.error({
              title: '审批失败',
              message: response.data.errmsg
            })
          })
        }
      })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['用户名', '手机号码', '性别', '生日', '状态']
        const filterVal = ['username', 'mobile', 'gender', 'birthday', 'status']
        excel.export_json_to_excel2(tHeader, this.list, filterVal, '用户信息')
        this.downloadLoading = false
      })
    },

    /**
     * 修改积分
    */
    updatePoints(row) {
      this.$refs['pointForm'].validate((valid) => {
        if (valid) {
          manualChangePoint({
            userId: this.pointForm.id,
            changeAmount: this.pointForm.changeAmount
          }).then(response => {
            this.pointDialogVisible = false
            this.pointForm = {
              changeAmount: 0,
              points: 0
            }
            this.$notify.success({
              title: '成功',
              message: '修改成功'
            })
            this.getList()
          }).catch(response => {
            this.$notify.error({
              title: '修改失败',
              message: response.data.errmsg
            })
          })
        }
      })
    },
    handlePoints(row) {
      this.pointDialogVisible = true
      // this.pointForm.points = row.points;
      // this.pointForm.id = row.id
      row.changeAmount = 0
      this.pointForm = { ...row }
    },
    /**
     * 修改余额
    */
    updateBalance(row) {
      this.$refs['balanceForm'].validate((valid) => {
        if (valid) {
          manualChangeBalance({
            userId: this.balanceForm.id,
            changeAmount: this.balanceForm.changeAmount
            // isAddPoint: this.isAddPoint
          }).then(response => {
            this.balanceDialogVisible = false
            this.balanceForm = {
              changeAmount: 0,
              balance: 0
              // isAddPoint:true,
            }
            this.$notify.success({
              title: '成功',
              message: '修改成功'
            })
            this.getList()
          }).catch(response => {
            this.$notify.error({
              title: '修改失败',
              message: response.data.errmsg
            })
          })
        }
      })
    },
    handleBalance(row) {
      this.balanceDialogVisible = true
      // this.balanceForm.balance = row.balance;
      // this.balanceForm.id = row.id
      row.changeAmount = 0
      this.balanceForm = { ...row }
      this.isAddPoint = true
    }
  }
}
</script>
