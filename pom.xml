<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.pioneer</groupId>
    <artifactId>mall</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>mall</name>
    <description>The pioneer</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.5.RELEASE</version>
        <relativePath/>
    </parent>

    <modules>
        <module>tpm-admin-api</module>
        <module>tpm-wx-api</module>
        <module>tpm-core</module>
        <module>tpm-dao</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <maven.test.skip>true</maven.test.skip>
        <fastjson.version>1.2.83</fastjson.version>
        <jackson-databind.version>2.9.6</jackson-databind.version>
        <mybatis-spring-boot-starter.version>1.3.2</mybatis-spring-boot-starter.version>
        <pagehelper-spring-boot-starter.version>1.2.5</pagehelper-spring-boot-starter.version>
        <shiro-spring-boot-web-starter.version>1.4.0</shiro-spring-boot-web-starter.version>
        <hibernate-validator.version>6.0.15.Final</hibernate-validator.version>
        <mysql-connector-java.version>5.1.46</mysql-connector-java.version>
        <druid-spring-boot-starter.version>1.1.10</druid-spring-boot-starter.version>
        <weixin-java-pay.version>4.6.0</weixin-java-pay.version>
        <weixin-java-miniapp.version>4.6.0</weixin-java-miniapp.version>
        <qcloudsms.version>1.0.5</qcloudsms.version>
        <cos_api.version>5.6.8</cos_api.version>
        <aliyun-sdk-oss.version>2.5.0</aliyun-sdk-oss.version>
        <qiniu-java-sdk.version>[7.2.0, 7.2.99]</qiniu-java-sdk.version>
        <spring-boot-starter-json.version>2.0.4.RELEASE</spring-boot-starter-json.version>
        <spring-boot-starter-mail.version>2.0.4.RELEASE</spring-boot-starter-mail.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot Mybatis 依赖 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>

            <!-- Spring Boot pagehelper 依赖 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring-boot-web-starter</artifactId>
                <version>${shiro-spring-boot-web-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <!-- MySQL 连接驱动依赖 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin-java-pay.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin-java-miniapp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.qcloudsms</groupId>
                <artifactId>qcloudsms</artifactId>
                <version>${qcloudsms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos_api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu-java-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${spring-boot-starter-json.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mail</artifactId>
                <version>${spring-boot-starter-mail.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!-- 热部署模块 -->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-devtools</artifactId>-->
        <!--            <optional>true</optional>-->
        <!--        </dependency>-->
        <!-- 测试模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <!-- dev 环境 -->
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.active>dev</profile.active>
                <logback.dir>./logs</logback.dir>
            </properties>
        </profile>
        <profile>
            <!-- test-online 环境 -->
            <id>test-online</id>
            <properties>
                <profile.active>demo</profile.active>
                <logback.dir>./logs</logback.dir>
            </properties>
        </profile>
        <profile>
            <!-- uat 环境 -->
            <id>uat</id>
            <properties>
                <profile.active>uat</profile.active>
                <logback.dir>./logs</logback.dir>
            </properties>
        </profile>
        <profile>
            <!-- prod 环境 -->
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
                <logback.dir>./logs</logback.dir>
            </properties>
        </profile>
    </profiles>

</project>
