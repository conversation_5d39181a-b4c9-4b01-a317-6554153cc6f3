package com.pioneer.mall.admin.api;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 管理后台服务启动类
 */
@SpringBootApplication(scanBasePackages = { "com.pioneer.mall.db", "com.pioneer.mall.core",
		"com.pioneer.mall.admin" })
@MapperScan({ "com.pioneer.mall.db.dao", "com.pioneer.mall.db.dao.ex" })
@EnableTransactionManagement
@EnableScheduling
public class Application {

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}
}