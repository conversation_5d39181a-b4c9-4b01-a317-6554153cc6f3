package com.pioneer.mall.admin.controller;

import com.alibaba.fastjson.JSONArray;
import com.pioneer.mall.admin.vo.req.CityCodePriceReqVo;
import com.pioneer.mall.admin.vo.req.ExpressConfReqVo;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs;
import com.pioneer.mall.db.service.TpmExpressConfService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/tpm/express/conf")
public class AdminExpressConfController {

    @Autowired
    private TpmExpressConfService expressConfService;

    @GetMapping("/list")
    public Object getallExpressConf(@RequestParam Integer expressFeeType) {

        List<TpmExpressConfWithBLOBs> expressConfWithBLOBsList = expressConfService.getExpressConfByExpressFeeType(expressFeeType);
        if (CollectionUtils.isEmpty(expressConfWithBLOBsList)) {
            return ResponseUtil.ok();
        }

        ExpressConfReqVo expressConfReqVo = new ExpressConfReqVo();
        expressConfReqVo.setExpressFeeType(expressFeeType);
        List<CityCodePriceReqVo> cityCodePriceReqVoList = expressConfWithBLOBsList.stream().map(z -> {
            CityCodePriceReqVo cityCodePriceReqVo = new CityCodePriceReqVo();
            cityCodePriceReqVo.setId(z.getId());
            cityCodePriceReqVo.setCityCodeList(JSONArray.parseArray(z.getDeliveryAreaCode(), String.class));
            cityCodePriceReqVo.setCityNameList(JSONArray.parseArray(z.getDeliveryArea(), String.class));
            cityCodePriceReqVo.setPrice(z.getShippingFee().doubleValue());
            return cityCodePriceReqVo;
        }).collect(Collectors.toList());
        expressConfReqVo.setCityCodePriceReqVoList(cityCodePriceReqVoList);
        return ResponseUtil.ok(expressConfReqVo);
    }


    @PostMapping("/upsert")
    public Object upsert(@RequestBody ExpressConfReqVo expressConfReqVo) {

        Integer expressFeeType = expressConfReqVo.getExpressFeeType();

        if (expressFeeType == null) {
            return ResponseUtil.fail("快递类型不能为空");
        }

        List<CityCodePriceReqVo> cityCodePriceReqVoList = expressConfReqVo.getCityCodePriceReqVoList();
        for (CityCodePriceReqVo z : cityCodePriceReqVoList) {
            if (CollectionUtils.isEmpty(z.getCityCodeList())) {
                return ResponseUtil.fail("城市编码不能为空");
            }
            if (CollectionUtils.isEmpty(z.getCityNameList())) {
                return ResponseUtil.fail("城市名称不能为空");
            }
            if (z.getPrice() == null) {
                return ResponseUtil.fail("价格不能为空");
            }
        }

        List<TpmExpressConfWithBLOBs> expressConfWithBLOBsList = expressConfReqVo.getCityCodePriceReqVoList().stream().map(z -> {
            TpmExpressConfWithBLOBs tpmExpressConfWithBLOBs = new TpmExpressConfWithBLOBs();
            tpmExpressConfWithBLOBs.setDeliveryAreaCode(JSONArray.toJSONString(z.getCityCodeList()));
            tpmExpressConfWithBLOBs.setDeliveryArea(JSONArray.toJSONString(z.getCityNameList()));
            tpmExpressConfWithBLOBs.setShippingFee(BigDecimal.valueOf(z.getPrice()));
            tpmExpressConfWithBLOBs.setCreateTime(LocalDateTime.now());
            tpmExpressConfWithBLOBs.setUpdateTime(LocalDateTime.now());
            tpmExpressConfWithBLOBs.setExpressFeeType(expressConfReqVo.getExpressFeeType());
            tpmExpressConfWithBLOBs.setDeleted(false);
            return tpmExpressConfWithBLOBs;
        }).collect(Collectors.toList());
        expressConfService.upsert(expressFeeType, expressConfWithBLOBsList);
        return ResponseUtil.ok();
    }
}
