package com.pioneer.mall.admin.controller;

import com.pioneer.mall.admin.service.AdminAreaService;
import com.pioneer.mall.admin.vo.res.AreaResVo;
import com.pioneer.mall.core.util.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/tpm/area")
public class AreaController {

    @Autowired
    private AdminAreaService adminAreaService;


    @RequestMapping("/list")
    public List<AreaResVo> getallArea() {
        return adminAreaService.getallArea();
    }

    /**
     * 省市区缓存刷新
     *
     * @return
     */
    @RequestMapping("/cache/sync")
    public Object cacheSync() {
        adminAreaService.cacheSync();
        return ResponseUtil.ok();
    }


}
