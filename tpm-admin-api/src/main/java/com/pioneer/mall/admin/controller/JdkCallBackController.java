package com.pioneer.mall.admin.controller;

import com.alibaba.fastjson.JSON;
import com.pioneer.mall.admin.vo.req.jdk.JdkDeliveryStatusCallBackReqVo;
import com.pioneer.mall.core.judanke.JdkOrderService;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.WechatNotifyUtils;
import com.pioneer.mall.core.util.WxUploadShippingInfoUtils;
import com.pioneer.mall.db.domain.TpmDeliveryRecord;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.enums.OrderPayTypeEnums;
import com.pioneer.mall.db.service.TpmDeliveryRecordService;
import com.pioneer.mall.db.service.TpmOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/30 21:32
 */
@RestController
@RequestMapping("/tpm")
@Slf4j
public class JdkCallBackController {

    @Autowired
    private TpmOrderService tpmOrderService;
    @Autowired
    private TpmDeliveryRecordService tpmDeliveryRecordService;
    @Autowired
    private WxUploadShippingInfoUtils wxUploadShippingInfoUtils;
    @Autowired
    private JdkOrderService jdkOrderService;

    private final ExecutorService executor = Executors.newFixedThreadPool(5);


    @PostMapping(value = "/delivery/status/callbackV2")
    public String deliveryStatusCallbackV2(@RequestParam(value = "type") Integer type,
                                           @RequestParam(value = "order_id") String orderId,
                                           @RequestParam(value = "tip_fee") Integer tip_fee,
                                           @RequestParam(value = "freight_fee") Integer freight_fee,
                                           @RequestParam(value = "courier_name") String courier_name,
                                           @RequestParam(value = "courier_phone") String courier_phone,
                                           @RequestParam(value = "delivery_brand") String delivery_brand,
                                           @RequestParam(value = "delivery_distance") String delivery_distance,
                                           @RequestParam(value = "delivery_order_no") String delivery_order_no,
                                           @RequestParam(value = "reason") String reason,
                                           @RequestParam(value = "from") Integer from) {
        log.info("deliveryStatusCallbackV2 聚客单运力状态回调参数 type={},order_id={},tip_fee={},freight_fee={},courier_name={},courier_phone={},delivery_brand={},delivery_distance={},delivery_order_no={},reason={},from={}", type, orderId, tip_fee, freight_fee, courier_name, courier_phone, delivery_brand, delivery_distance, delivery_order_no, reason, from);
        JdkDeliveryStatusCallBackReqVo jdkDeliveryStatusCallBackReqVo = new JdkDeliveryStatusCallBackReqVo();
        jdkDeliveryStatusCallBackReqVo.setOrderId(orderId);
        jdkDeliveryStatusCallBackReqVo.setType(type);
        jdkDeliveryStatusCallBackReqVo.setTipFee(tip_fee);
        jdkDeliveryStatusCallBackReqVo.setFreightFee(freight_fee);
        jdkDeliveryStatusCallBackReqVo.setCourierName(courier_name);
        jdkDeliveryStatusCallBackReqVo.setCourierPhone(courier_phone);
        jdkDeliveryStatusCallBackReqVo.setDeliveryBrand(delivery_brand);
        jdkDeliveryStatusCallBackReqVo.setDeliveryDistance(delivery_distance);
        jdkDeliveryStatusCallBackReqVo.setDeliveryOrderNo(delivery_order_no);
        jdkDeliveryStatusCallBackReqVo.setReason(reason);
        jdkDeliveryStatusCallBackReqVo.setFrom(from);
        String deliveryOrderId = jdkDeliveryStatusCallBackReqVo.getOrderId();
        if (StringUtils.isEmpty(deliveryOrderId)) {
            log.info("聚客单运力状态回调参数data中order_id为空");
            return "ERROR";
        }

        TpmOrder tpmOrder = tpmOrderService.getByExternalDeliveryPlatformOrderId(deliveryOrderId);
        if (Objects.isNull(tpmOrder)) {
            log.error("根据orderId:" + deliveryOrderId + "未查询到关联订单");
            return "ERROR";
        }
        TpmDeliveryRecord tpmDeliveryRecord = new TpmDeliveryRecord();
        BeanUtils.copyProperties(jdkDeliveryStatusCallBackReqVo, tpmDeliveryRecord);
        // 设置tpmDeliveryRecord 的所有属性
        tpmDeliveryRecord.setOrderId(jdkDeliveryStatusCallBackReqVo.getOrderId());
        tpmDeliveryRecord.setTpmOrderId(tpmOrder.getId());
        tpmDeliveryRecord.setPlatform("jdk");
        String deliveryStatus = type == null ? null : type.toString();
        tpmDeliveryRecord.setDeliveryStatus(deliveryStatus);
        tpmOrderService.updateDeliveryStatusAndRecord(tpmOrder.getId(), deliveryStatus, tpmDeliveryRecord);
        if (Objects.equals(deliveryStatus, "4")) {
            if (Objects.equals(tpmOrder.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
                log.info("订单:{} 聚单客完成 上传发货信息", tpmOrder.getOrderSn());
                executor.submit(() -> wxUploadShippingInfoUtils.doUploadShippingInfo(tpmOrder.getId()));
            }
        }else if (Objects.equals(deliveryStatus, "7")) {
            log.info("订单:{} 聚单客运力取消 原因:{}", tpmOrder.getOrderSn(), reason);
            executor.submit(() -> {
                WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(tpmOrder.getShopId()), null, "订单:" + tpmOrder.getOrderSn() + "聚单客平台运力取消");
                jdkOrderService.printerPrintNotice(tpmOrder);
            });
        }
        log.info("聚客单运力状态回调处理完成 orderId={}", orderId);
        return "SUCCEED";
    }

    // todo 地图距离 配送回调接口参数还需要确认，配送回调状态如果是类似已完成状态，维护订单的状态应该变成已收货

    /**
     * https://open.judanke.cn/docs/130/39
     *
     * @param jdkCallBackBaseParam
     * @return
     */
    @PostMapping(value = "/delivery/status/callback", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String deliveryStatusCallback(@ModelAttribute("app_id") String appId,
                                         @ModelAttribute("sign") String sign,
                                         @ModelAttribute("ts") String ts,
                                         @ModelAttribute("nonce") String nonce,
                                         @ModelAttribute("data") String data) {
        log.info("聚客单运力状态回调参数 app_id={},sign={},ts={},nonce={},data={}", appId, sign, ts, nonce, data);
        if (StringUtils.isEmpty(data)) {
            return "ERROR";
        }
//        log.info("聚客单运力状态回调参数 jdkCallBackBaseParam:" + jdkCallBackBaseParam);
//        String data = jdkCallBackBaseParam.getData();
        JdkDeliveryStatusCallBackReqVo jdkDeliveryStatusCallBackReqVo = JSON.parseObject(data, JdkDeliveryStatusCallBackReqVo.class);
        String deliveryOrderId = jdkDeliveryStatusCallBackReqVo.getOrderId();
        if (StringUtils.isEmpty(deliveryOrderId)) {
            log.info("聚客单运力状态回调参数data中order_id为空");
            return "ERROR";
        }

        TpmOrder tpmOrder = tpmOrderService.getByExternalDeliveryPlatformOrderId(deliveryOrderId);
        if (Objects.isNull(tpmOrder)) {
            log.error("根据orderId:" + deliveryOrderId + "未查询到关联订单");
            return "ERROR";
        }
        TpmDeliveryRecord tpmDeliveryRecord = new TpmDeliveryRecord();
        BeanUtils.copyProperties(jdkDeliveryStatusCallBackReqVo, tpmDeliveryRecord);
        // 设置tpmDeliveryRecord 的所有属性
        tpmDeliveryRecord.setOrderId(jdkDeliveryStatusCallBackReqVo.getOrderId());
        tpmDeliveryRecord.setTpmOrderId(tpmOrder.getId());
        tpmDeliveryRecord.setPlatform("jdk");
        Integer type = jdkDeliveryStatusCallBackReqVo.getType();
        String deliveryStatus = type == null ? null : type.toString();
        tpmOrderService.updateDeliveryStatusAndRecord(tpmOrder.getId(), deliveryStatus, tpmDeliveryRecord);
        log.info("聚客单运力状态回调处理完成");
        return "SUCCEED";
    }


}
