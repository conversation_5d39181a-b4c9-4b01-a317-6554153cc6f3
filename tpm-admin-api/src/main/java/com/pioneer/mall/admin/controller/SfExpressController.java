package com.pioneer.mall.admin.controller;

import com.alibaba.fastjson.JSON;
import com.pioneer.mall.admin.dao.SfRoutePushRequest;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmWaybillRoute;
import com.pioneer.mall.db.service.TpmOrderService;
import com.pioneer.mall.db.service.TpmWaybillRouteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/17 21:28
 */
@RestController
@RequestMapping("/tpm/sf")
@Slf4j
public class SfExpressController {


    @Autowired
    private TpmOrderService tpmOrderService;
    @Autowired
    private TpmWaybillRouteService tpmWaybillRouteService;

    @PostMapping(value = "/route/push", consumes = "application/json")
    public SfRoutePushRequest.ResponseMessage routePush(@RequestBody SfRoutePushRequest sfRoutePushRequest) {
        log.info("routePush reqVo={}", JSON.toJSONString(sfRoutePushRequest));
        SfRoutePushRequest.Body body = sfRoutePushRequest.getBody();
        List<SfRoutePushRequest.WaybillRoute> waybillRouteList = body.getWaybillRoute();
        for (SfRoutePushRequest.WaybillRoute waybillRoute : waybillRouteList) {
            TpmOrder tpmOrder = tpmOrderService.findByWayBillNo(waybillRoute.getMailno());
            if (Objects.isNull(tpmOrder)) {
                SfRoutePushRequest.ResponseMessage responseMessage = new SfRoutePushRequest.ResponseMessage();
                responseMessage.setReturn_code("1000");
                responseMessage.setReturn_msg("系统异常");
                continue;
            }
            TpmWaybillRoute tpmWaybillRoute = new TpmWaybillRoute();
            tpmWaybillRoute.setTpmOrderId(tpmOrder.getId());
            tpmWaybillRoute.setMailNo(waybillRoute.getMailno());
            tpmWaybillRoute.setReasonName(waybillRoute.getReasonName());
            tpmWaybillRoute.setOrderId(waybillRoute.getOrderid());
            tpmWaybillRoute.setAcceptAddress(waybillRoute.getAcceptAddress());
            tpmWaybillRoute.setAcceptTime(waybillRoute.getAcceptTime());
            tpmWaybillRoute.setRemark(waybillRoute.getRemark());
            tpmWaybillRoute.setOpCode(waybillRoute.getOpCode());
            tpmWaybillRoute.setReasonCode(waybillRoute.getReasonCode());
            tpmWaybillRoute.setFirstStatusCode(waybillRoute.getFirstStatusCode());
            tpmWaybillRoute.setFirstStatusName(waybillRoute.getFirstStatusName());
            tpmWaybillRoute.setSecondaryStatusCode(waybillRoute.getSecondaryStatusCode());
            tpmWaybillRoute.setSecondaryStatusName(waybillRoute.getSecondaryStatusName());
            tpmWaybillRoute.setAddTime(LocalDateTime.now());
            tpmWaybillRoute.setUpdateTime(LocalDateTime.now());
            tpmWaybillRoute.setDeleted(false);
            tpmWaybillRouteService.add(tpmWaybillRoute);
        }


        SfRoutePushRequest.ResponseMessage responseMessage = new SfRoutePushRequest.ResponseMessage();
        responseMessage.setReturn_code("0000");
        responseMessage.setReturn_msg("成功");
        return responseMessage;
    }
}
