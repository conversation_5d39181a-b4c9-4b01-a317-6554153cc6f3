package com.pioneer.mall.admin.dao;

import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.domain.TpmGoodsAttribute;
import com.pioneer.mall.db.domain.TpmGoodsProduct;
import com.pioneer.mall.db.domain.TpmGoodsSpecification;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.vo.TpmGoodsVo;

import java.util.List;

public class GoodsAllinone {
	TpmGoodsVo goods;
	TpmGoodsSpecification[] specifications;
	TpmGoodsAttribute[] attributes;
	// 这里采用 Product 再转换到 TpmGoodsProduct
	TpmGoodsProduct[] products;
	List<TpmGoodsAttributesDto> goodsAttributes;

	public List<TpmGoodsAttributesDto> getGoodsAttributes() {
		return goodsAttributes;
	}

	public GoodsAllinone setGoodsAttributes(List<TpmGoodsAttributesDto> goodsAttributes) {
		this.goodsAttributes = goodsAttributes;
		return this;
	}

	public TpmGoodsVo getGoods() {
		return goods;
	}

	public void setGoods(TpmGoodsVo goods) {
		this.goods = goods;
	}

	public TpmGoodsProduct[] getProducts() {
		return products;
	}

	public void setProducts(TpmGoodsProduct[] products) {
		this.products = products;
	}

	public TpmGoodsSpecification[] getSpecifications() {
		return specifications;
	}

	public void setSpecifications(TpmGoodsSpecification[] specifications) {
		this.specifications = specifications;
	}

	public TpmGoodsAttribute[] getAttributes() {
		return attributes;
	}

	public void setAttributes(TpmGoodsAttribute[] attributes) {
		this.attributes = attributes;
	}

}
