package com.pioneer.mall.admin.dao;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/19 20:41
 */
@Data
public class SfRoutePushRequest {
    @JsonProperty("Body")
    private Body body;

    @Data
    public static class Body {
        @JsonProperty("WaybillRoute")
        private List<WaybillRoute> waybillRoute;
    }

    @Data
    public static class WaybillRoute {
        private String mailno;
        private String acceptAddress;
        private String reasonName;
        private String orderid;
        private String acceptTime;
        private String remark;
        private String opCode;
        private String id;
        private String reasonCode;
        private String firstStatusCode;
        private String firstStatusName;
        private String secondaryStatusCode;
        private String secondaryStatusName;
    }

    @Data
// 响应实体类
    public static class ResponseMessage {
        private String return_code;
        private String return_msg;
    }

}