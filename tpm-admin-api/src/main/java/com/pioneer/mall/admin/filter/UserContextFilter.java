package com.pioneer.mall.admin.filter;
import com.pioneer.mall.db.util.ThreadContextUtil;

import javax.servlet.*;
import java.io.IOException;

public class UserContextFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化逻辑（如果有）
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        try {
            // 继续执行过滤器链
            chain.doFilter(request, response);
        } finally {
            // 确保在请求处理完成后清理ThreadLocal
            ThreadContextUtil.clear();
        }
    }

    @Override
    public void destroy() {
        // 销毁逻辑（如果有）
    }
}
