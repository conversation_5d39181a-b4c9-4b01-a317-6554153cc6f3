package com.pioneer.mall.admin.job;

import java.util.List;

import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCouponUser;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.pioneer.mall.db.service.TpmCouponService;
import com.pioneer.mall.db.service.TpmCouponUserService;
import com.pioneer.mall.db.util.CouponConstant;
import com.pioneer.mall.db.util.CouponUserConstant;

/**
 * 检测优惠券过期情况
 */
@Component
public class CouponJob {
	private final Log logger = LogFactory.getLog(CouponJob.class);

	@Autowired
	private TpmCouponService couponService;
	@Autowired
	private TpmCouponUserService couponUserService;

	/**
	 * 每隔一个小时检查
	 */
	@Scheduled(fixedDelay = 60 * 60 * 1000)
	public void checkCouponExpired() {
		logger.info("系统开启任务检查优惠券是否已经过期");

		List<TpmCoupon> couponList = couponService.queryExpired();
		for (TpmCoupon coupon : couponList) {
			coupon.setStatus(CouponConstant.STATUS_EXPIRED);
			couponService.updateById(coupon);
		}

		List<TpmCouponUser> couponUserList = couponUserService.queryExpired();
		for (TpmCouponUser couponUser : couponUserList) {
			couponUser.setStatus(CouponUserConstant.STATUS_EXPIRED);
			couponUserService.update(couponUser);
		}
	}

}
