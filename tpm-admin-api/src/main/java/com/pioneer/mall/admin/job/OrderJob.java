package com.pioneer.mall.admin.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pioneer.mall.core.judanke.JdkOrderService;
import com.pioneer.mall.core.judanke.dto.JdkCommonResDto;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.type.OrderFreightTypeEnum;
import com.pioneer.mall.core.util.WechatNotifyUtils;
import com.pioneer.mall.core.util.WxUploadShippingInfoUtils;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.enums.OrderPayTypeEnums;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * 检测订单状态
 */
@Slf4j
@Component
public class OrderJob {
    private final Log logger = LogFactory.getLog(OrderJob.class);

    @Autowired
    private TpmOrderGoodsService orderGoodsService;
    @Autowired
    private TpmOrderService orderService;
    //	@Autowired
//	private TpmGoodsProductService productService;
    @Autowired
    private TpmPointService tpmPointService;
    @Autowired
    private TpmGoodsService tpmGoodsService;
    @Autowired
    private TpmCouponUserService tpmCouponUserService;
    @Autowired
    private WxUploadShippingInfoUtils wxUploadShippingInfoUtils;
    @Autowired
    private JdkOrderService jdkOrderService;

    /**
     * 自动取消订单
     * <p>
     * 定时检查订单未付款情况，如果超时半个小时则自动取消订单 定时时间是每次相隔半个小时。
     * <p>
     * 注意，因为是相隔半小时检查，因此导致有订单是超时一个小时以后才设置取消状态。 TODO
     * 这里可以进一步地配合用户订单查询时订单未付款检查，如果订单超时半小时则取消。
     */
    @Scheduled(fixedDelay = 30 * 60 * 1000)
    @Transactional
    public void checkOrderUnpaid() {
        logger.info("系统开启任务检查订单是否已经超期自动取消订单");

        List<TpmOrder> orderList = orderService.queryUnpaid();
        for (TpmOrder order : orderList) {
            LocalDateTime add = order.getAddTime();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expired = add.plusMinutes(30);
            if (expired.isAfter(now)) {
                continue;
            }

            // 设置订单已取消状态
            order.setOrderStatus(OrderUtil.STATUS_AUTO_CANCEL);
            order.setEndTime(LocalDateTime.now());
            if (orderService.updateWithOptimisticLocker(order) == 0) {
                throw new RuntimeException("更新数据已失效");
            }

            // 商品货品数量增加
            Integer orderId = order.getId();
            List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(orderId);
            for (TpmOrderGoods orderGoods : orderGoodsList) {
                Integer goodsId = orderGoods.getGoodsId();
                Short number = orderGoods.getNumber();
                if (tpmGoodsService.addInventory(goodsId, number) == 0) {
                    throw new RuntimeException("商品货品库存增加失败");
                }
            }
            tpmCouponUserService.giveBackCoupon(order.getOrderSn());
            logger.info("订单 ID=" + order.getId() + " 已经超期自动取消订单");
        }
    }

    private final ExecutorService executor = Executors.newFixedThreadPool(5);


    /**
     * 自动确认订单
     * <p>
     * 定时检查订单未确认情况，如果超时七天则自动确认订单 定时时间是每天凌晨3点。
     * <p>
     * 注意，因为是相隔一天检查，因此导致有订单是超时八天以后才设置自动确认。 这里可以进一步地配合用户订单查询时订单未确认检查，如果订单超时7天则自动确认。
     * 但是，这里可能不是非常必要。相比订单未付款检查中存在商品资源有限所以应该 早点清理未付款情况，这里八天再确认是可以的。。
     */
    @Scheduled(cron = "0 0 9-23 * * ? ")
    public void checkPickUpAndDeliveryUnconfirmed() {
        logger.info("系统开启任务检查订单是否已经超期自动确认收货");

        List<TpmOrder> orderList = orderService.queryPickUpAndDeliveryUnconfirmed();
        for (TpmOrder order : orderList) {
            LocalDateTime oldConfirmTime = order.getConfirmTime();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expired;
            if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.Distribution.getValue())) {
                LocalDateTime ship = order.getShipTime();
                if (Objects.isNull(ship)) {
                    ship = order.getPayTime();
                }
                expired = ship.plusHours(2);
            } else {
                LocalDateTime payTime = order.getPayTime();
                expired = payTime.plusHours(1);
            }
            if (expired.isAfter(now)) {
                continue;
            }
            // 设置订单已取消状态
            order.setOrderStatus(OrderUtil.STATUS_AUTO_CONFIRM);
            order.setConfirmTime(now);
            if (orderService.updateWithOptimisticLocker(order) == 0) {
                logger.info("订单 ID=" + order.getId() + " 数据已经更新，放弃自动确认收货");
            } else if (Objects.isNull(oldConfirmTime)) {
                TpmPointDto tpmPointDto = new TpmPointDto();
                tpmPointDto.setUserId(order.getUserId());
                tpmPointDto.setOrderSn(order.getOrderSn());
                tpmPointDto.setAmount(order.getActualPrice());
                tpmPointDto.setDescription("订单:" + order.getOrderSn() + "确认收货添加积分:" + order.getActualPrice());
                tpmPointService.addPoint(tpmPointDto);
                logger.info("订单 ID=" + order.getId() + " 已经超期自动确认收货");
                //上传发货信息
                if (Objects.equals(order.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
                    executor.submit(() -> wxUploadShippingInfoUtils.doUploadShippingInfo(order.getId()));
                }
            }
        }
    }

    @Scheduled(cron = "0 0 9-23 * * ? ")
    public void checkExpressUnconfirmed() {
        logger.info("系统开启任务检查商城订单是否已经超期自动确认收货");
        List<TpmOrder> orderList = orderService.queryExpressUnconfirmed();
        for (TpmOrder order : orderList) {
            LocalDateTime oldConfirmTime = order.getConfirmTime();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime payTime = order.getPayTime();
            LocalDateTime expired = payTime.plusDays(7);
            if (expired.isAfter(now)) {
                continue;
            }
            // 设置订单已取消状态
            order.setOrderStatus(OrderUtil.STATUS_AUTO_CONFIRM);
            order.setConfirmTime(now);
            if (orderService.updateWithOptimisticLocker(order) == 0) {
                logger.info("订单 ID=" + order.getId() + " 数据已经更新，放弃自动确认收货");
            } else if (Objects.isNull(oldConfirmTime)) {
                TpmPointDto tpmPointDto = new TpmPointDto();
                tpmPointDto.setUserId(order.getUserId());
                tpmPointDto.setOrderSn(order.getOrderSn());
                tpmPointDto.setAmount(order.getActualPrice());
                tpmPointDto.setDescription("订单:" + order.getOrderSn() + "确认收货添加积分:" + order.getActualPrice());
                tpmPointService.addPoint(tpmPointDto);
                logger.info("订单 ID=" + order.getId() + " 已经超期自动确认收货");
                //上传发货信息
//                if (Objects.equals(order.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
//                    executor.submit(() -> wxUploadShippingInfoUtils.doUploadExpressShippingInfo(order.getId()));
//                }
            }
        }
    }

    /**
     * 可评价订单商品超期
     * <p>
     * 定时检查订单商品评价情况，如果确认商品超时七天则取消可评价状态 定时时间是每天凌晨4点。
     */
    /**
     * 每小时自动查询聚单客账户余额
     * 定时查询所有配置了聚单客的店铺账户余额，并发送微信通知
     * 定时时间是每小时整点执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkJdkBalance() {
        logger.info("系统开启任务查询聚单客账户余额");

        try {
            // 构建通知消息
            StringBuilder message = new StringBuilder("聚单客账户余额查询结果：\n");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            message.append("查询时间：").append(LocalDateTime.now().format(formatter)).append("\n\n");

            // 处理每个店铺的余额查询结果
            try {
                String result = jdkOrderService.queryBalance();
                JdkCommonResDto resDto = JSON.parseObject(result, JdkCommonResDto.class);
                // 解析余额查询结果
                if (resDto != null && resDto.getCode() == 0 && !StringUtils.isEmpty(resDto.getData())) {
                    JSONObject balanceData = JSON.parseObject(resDto.getData());

                    // 提取余额信息
                    if (balanceData.containsKey("total_avail")) {
                        // 假设余额是以分为单位的整数
                        Integer balanceInCents = balanceData.getInteger("total_avail");
                        BigDecimal balance = new BigDecimal(balanceInCents).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

                        message.append("余额: ").append(balance).append("元\n");

                        // 如果余额低于阈值，添加警告信息
                        BigDecimal threshold = new BigDecimal(100); // 设置100元为警戒线
                        if (balance.compareTo(threshold) < 0) {
                            message.append(" 余额不足，请及时充值！\n");
                        }
                    } else {
                        message.append("余额查询成功，但返回数据格式异常\n");
                    }
                } else {
                    message.append("查询失败: ").append(resDto != null ? resDto.getMsg() : "未知错误").append("\n");
                }
            } catch (Exception e) {
                log.error("余额查询结果异常:{}",e.getMessage());
                message.append("解析结果异常: ").append(e.getMessage()).append("\n");
            }
            // 发送微信通知
            WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(1), null, message.toString());
            log.info("聚单客账户余额查询完成，已发送通知");
        } catch (Exception e) {
            log.error("查询聚单客账户余额异常: {}", e.getMessage(), e);

            // 发送异常通知
            String errorMessage = "聚单客账户余额查询异常：" + e.getMessage();
            WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(1), null, errorMessage);
        }
    }


    @Scheduled(cron = "0 0 4 * * ?")
    public void checkOrderComment() {
        logger.info("系统开启任务检查订单是否已经超期未评价");

        LocalDateTime now = LocalDateTime.now();
        List<TpmOrder> orderList = orderService.queryComment();
        for (TpmOrder order : orderList) {
            LocalDateTime confirm = order.getConfirmTime();
            LocalDateTime expired = confirm.plusDays(7);
            if (expired.isAfter(now)) {
                continue;
            }

            order.setComments((short) 0);
            orderService.updateWithOptimisticLocker(order);

            List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(order.getId());
            for (TpmOrderGoods orderGoods : orderGoodsList) {
                orderGoods.setComment(-1);
                orderGoodsService.updateById(orderGoods);
            }
        }
    }
}

