package com.pioneer.mall.admin.service;

import com.pioneer.mall.admin.vo.res.AreaResVo;
import com.pioneer.mall.db.domain.TpmArea;
import com.pioneer.mall.db.service.TpmAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AdminAreaService {

    @Autowired
    private TpmAreaService tpmAreaService;
    private static final Object syncLock = new Object();
    /**
     * 省市区缓存
     */
    private static volatile List<AreaResVo> AreaResVoCachelist = new ArrayList<>();

    public void cacheSync(){
        AreaResVoCachelist.clear();
        getallArea();
    }

    public List<AreaResVo> getallArea() {

        if (CollectionUtils.isEmpty(AreaResVoCachelist)) {
            synchronized (syncLock) {
                if (CollectionUtils.isEmpty(AreaResVoCachelist)) {
                    List<TpmArea> areasList = tpmAreaService.getallArea();
                    //获取省
                    List<TpmArea> finalAreasList = areasList;
                    List<AreaResVo> areaResVoList = areasList.stream().filter(tpmArea -> tpmArea.getLevel() == 1)
                            .map(tpmArea -> {
                                AreaResVo areaResVo = new AreaResVo();
                                areaResVo.setId(tpmArea.getId());
                                areaResVo.setValue(tpmArea.getAreaCode());
                                areaResVo.setLabel(tpmArea.getAreaName());
                                List<AreaResVo> cityList = finalAreasList.stream().filter(z -> z.getParentId().equals(tpmArea.getId()))
                                        .map(z -> {
                                            AreaResVo areaVo = new AreaResVo();
                                            areaVo.setId(z.getId());
                                            areaVo.setValue(z.getAreaCode());
                                            areaVo.setLabel(z.getAreaName());
                                            return areaVo;
                                        }).collect(Collectors.toList());

                                areaResVo.setChildren(cityList);
                                return areaResVo;
                            }).collect(Collectors.toList());

                    AreaResVoCachelist = areaResVoList;
                }
            }
        }
        return AreaResVoCachelist;
    }
}
