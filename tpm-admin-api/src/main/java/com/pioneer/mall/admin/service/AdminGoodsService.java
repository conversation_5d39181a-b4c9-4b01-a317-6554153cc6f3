package com.pioneer.mall.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.dao.GoodsAllinone;
import com.pioneer.mall.admin.util.AdminResponseUtil;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.admin.util.CatVo;
import com.pioneer.mall.admin.vo.res.TpmCategoryResVo;
import com.pioneer.mall.db.util.SaleRangeEnum;
import com.pioneer.mall.core.qcode.QCodeService;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.dto.TpmGoodsSpecificationsDto;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.vo.TpmGoodsVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.pioneer.mall.admin.util.AdminResponseCode.GOODS_NAME_EXIST;

@Service
public class AdminGoodsService {
    private static final Logger logger = LoggerFactory.getLogger(AdminGoodsService.class);

    @Autowired
    private TpmGoodsService goodsService;
    @Autowired
    private TpmGoodsSpecificationService specificationService;
    @Autowired
    private TpmGoodsAttributeService attributeService;
    @Autowired
    private TpmGoodsProductService productService;
    @Autowired
    private TpmCategoryService categoryService;
    @Autowired
    private TpmBrandService brandService;
    @Autowired
    private TpmCartService cartService;
    @Autowired
    private TpmOrderGoodsService orderGoodsService;

    @Autowired
    private QCodeService qCodeService;

    @Autowired
    private AdminDataAuthService adminDataAuthService;

    @Autowired
    private TpmAttributeSpecificationService attributeSpecificationService;

    @Autowired
    private TpmShopInfoService tpmShopInfoService;

    public Object list(String goodsSn, String name, Integer categoryId, Integer businessType, Integer page, Integer limit, String sort, String order, List<Integer> brandIds, Integer shopId) {
        List<TpmGoods> goodsList = goodsService.querySelective(goodsSn, name, categoryId, businessType, page, limit, sort, order, brandIds, shopId);
        long total = PageInfo.of(goodsList).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);

        List<Integer> shopIdList = goodsList.stream().map(TpmGoods::getShopId).distinct().collect(Collectors.toList());
        Map<Integer, String> shopInfoMap = tpmShopInfoService.selectShopInfo(shopIdList);
        // 转换下销售范围
        List<TpmGoodsVo> goodsVoList = new ArrayList<>();
        for (TpmGoods tpmGoods : goodsList) {
            TpmGoodsVo goodsVo = new TpmGoodsVo();
            BeanUtils.copyProperties(tpmGoods, goodsVo);
            // 销售范围
            goodsVo.setSaleRangeList(SaleRangeEnum.changeToPageSaleRangeByDb(tpmGoods.getSaleRange()));
            goodsVo.setShopName(shopInfoMap.get(tpmGoods.getShopId()));
            goodsVoList.add(goodsVo);
        }
        data.put("items", goodsVoList);

        logger.info("【请求结束】商品管理->商品管理->查询,响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    private Object validate(GoodsAllinone goodsAllinone) {
        TpmGoods goods = goodsAllinone.getGoods();
        if (StringUtils.isEmpty(goods.getBusinessType())) {
            return ResponseUtil.fail("商品业务类型为空");
        }
        if (goods.getShopId() == null) {
            return ResponseUtil.fail("店铺ID不能为空");
        }
        String name = goods.getName();
        if (StringUtils.isEmpty(name)) {
            return ResponseUtil.badArgument();
        }
        String goodsSn = goods.getGoodsSn();
        if (StringUtils.isEmpty(goodsSn)) {
            return ResponseUtil.badArgument();
        }
        // 品牌商可以不设置，如果设置则需要验证品牌商存在
        Integer brandId = goods.getBrandId();
        if (brandId != null && brandId != 0) {
            if (brandService.findById(brandId) == null) {
                return ResponseUtil.badArgumentValue();
            }
        }
        // 分类可以不设置，如果设置则需要验证分类存在
        Integer categoryId = goods.getCategoryId();
        if (categoryId != null && categoryId != 0) {
            TpmCategory category = categoryService.findById(categoryId);
            if (category == null) {
                return ResponseUtil.badArgumentValue();
            }
            // 类目的店铺id和商品的店铺id一致
            if (!category.getShopId().equals(goods.getShopId())) {
                return ResponseUtil.fail("商品和类目不属于同一店铺");
            }
        }

        TpmGoodsAttribute[] attributes = goodsAllinone.getAttributes();
        for (TpmGoodsAttribute attribute : attributes) {
            String attr = attribute.getAttribute();
            if (StringUtils.isEmpty(attr)) {
                return ResponseUtil.badArgument();
            }
            String value = attribute.getValue();
            if (StringUtils.isEmpty(value)) {
                return ResponseUtil.badArgument();
            }
        }

        TpmGoodsSpecification[] specifications = goodsAllinone.getSpecifications();
        for (TpmGoodsSpecification specification : specifications) {
            String spec = specification.getSpecification();
            if (StringUtils.isEmpty(spec)) {
                return ResponseUtil.badArgument();
            }
            String value = specification.getValue();
            if (StringUtils.isEmpty(value)) {
                return ResponseUtil.badArgument();
            }
        }

        TpmGoodsProduct[] products = goodsAllinone.getProducts();
        for (TpmGoodsProduct product : products) {
            Integer number = product.getNumber();
            if (number == null || number < 0) {
                return ResponseUtil.badArgument();
            }

            BigDecimal price = product.getPrice();
            if (price == null) {
                return ResponseUtil.badArgument();
            }

            String[] productSpecifications = product.getSpecifications();
            if (productSpecifications.length == 0) {
                return ResponseUtil.badArgument();
            }
        }

        return null;
    }

    /**
     * 编辑商品
     * <p>
     * TODO 目前商品修改的逻辑是 1. 更新Dts_goods表 2.
     * 逻辑删除Dts_goods_specification、Dts_goods_attribute、Dts_goods_product 3.
     * 添加Dts_goods_specification、Dts_goods_attribute、Dts_goods_product
     * <p>
     * 这里商品三个表的数据采用删除再添加的策略是因为 商品编辑页面，支持管理员添加删除商品规格、添加删除商品属性，因此这里仅仅更新是不可能的，
     * 只能删除三个表旧的数据，然后添加新的数据。 但是这里又会引入新的问题，就是存在订单商品货品ID指向了失效的商品货品表。
     * 因此这里会拒绝管理员编辑商品，如果订单或购物车中存在商品。 所以这里可能需要重新设计。
     */
    @Transactional
    public Object update(GoodsAllinone goodsAllinone) {
        Object error = validate(goodsAllinone);
        if (error != null) {
            return error;
        }
        TpmGoods goods = this.changeToTpmGoods(goodsAllinone.getGoods());
//        TpmGoodsAttribute[] attributes = goodsAllinone.getAttributes();
//        TpmGoodsSpecification[] specifications = goodsAllinone.getSpecifications();
        TpmGoodsProduct[] products = goodsAllinone.getProducts();

        Integer id = goods.getId();
        // 检查是否存在购物车商品或者订单商品
        // 如果存在则拒绝修改商品。
//		if (orderGoodsService.checkExist(id) || cartService.checkExist(id)) {
//			logger.error("商品管理->商品管理->编辑错误:{}", GOODS_UPDATE_NOT_ALLOWED.desc());
//			return AdminResponseUtil.fail(GOODS_UPDATE_NOT_ALLOWED);
//		}

        List<TpmGoodsAttributesDto> goodsAttributes = goodsAllinone.getGoodsAttributes();
        Object result = this.goodsAttributesValidate(goodsAttributes);
        if (result != null) {
            return result;
        }
        //库存小于0自动变为已售罄
        Integer inventoryNum = goods.getInventoryNum();
        if (Objects.nonNull(inventoryNum)) {
            if (inventoryNum <= 0) {
                goods.setIsSellOut(true);
            } else {
                goods.setIsSellOut(false);
            }
        }
        // 将生成的分享图片地址写入数据库
        String url = qCodeService.createGoodShareImage(null, goods.getId().toString(), goods.getPicUrl(), goods.getName(), goods.getCounterPrice(), goods.getRetailPrice());
        goods.setShareUrl(url);

        // 商品基本信息表Dts_goods
        if (goodsService.updateById(goods) == 0) {
            logger.error("商品管理->商品管理->编辑错误:{}", "更新数据失败");
            throw new RuntimeException("更新数据失败");
        }

        Integer gid = goods.getId();
        specificationService.deleteByGid(gid);
        attributeService.deleteByGid(gid);
        productService.deleteByGid(gid);


        saveGoodsAttributesAndSpecifications(goodsAttributes, goods.getId());

//        // 商品规格表Dts_goods_specification
//        for (TpmGoodsSpecification specification : specifications) {
//            specification.setGoodsId(goods.getId());
//            specificationService.add(specification);
//        }
//
//        // 商品参数表Dts_goods_attribute
//        for (TpmGoodsAttribute attribute : attributes) {
//            attribute.setGoodsId(goods.getId());
//            attributeService.add(attribute);
//        }

        // 商品货品表Dts_product
        for (TpmGoodsProduct product : products) {
            product.setGoodsId(goods.getId());
            productService.add(product);
        }
        //qCodeService.createGoodShareImage(goods.getId().toString(), goods.getPicUrl(), goods.getName());

        logger.info("【请求结束】商品管理->商品管理->编辑,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    @Transactional
    public Object delete(TpmGoods goods) {
        Integer id = goods.getId();
        if (id == null) {
            return ResponseUtil.badArgument();
        }

        Integer gid = goods.getId();
        goodsService.deleteById(gid);
        specificationService.deleteByGid(gid);
        attributeService.deleteByGid(gid);
        productService.deleteByGid(gid);

        logger.info("【请求结束】商品管理->商品管理->删除,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    @Transactional
    public Object create(GoodsAllinone goodsAllinone) {
        Object error = validate(goodsAllinone);
        if (error != null) {
            return error;
        }

        TpmGoods goods = this.changeToTpmGoods(goodsAllinone.getGoods());
        TpmGoodsAttribute[] attributes = goodsAllinone.getAttributes();
        TpmGoodsSpecification[] specifications = goodsAllinone.getSpecifications();
        TpmGoodsProduct[] products = goodsAllinone.getProducts();

        List<TpmGoodsAttributesDto> goodsAttributes = goodsAllinone.getGoodsAttributes();
        Object result = this.goodsAttributesValidate(goodsAttributes);
        if (result != null) {
            return result;
        }
        String name = goods.getName();
        if (goodsService.checkExistByName(name, goods.getBusinessType())) {
            logger.error("商品管理->商品管理->上架错误:{}", GOODS_NAME_EXIST.desc());
            return AdminResponseUtil.fail(GOODS_NAME_EXIST);
        }

        // 商品基本信息表Dts_goods
        goodsService.add(goods);

        // 将生成的分享图片地址写入数据库
        String url = qCodeService.createGoodShareImage(null, goods.getId().toString(), goods.getPicUrl(), goods.getName(), goods.getCounterPrice(), goods.getRetailPrice());
        if (!StringUtils.isEmpty(url)) {
            goods.setShareUrl(url);
            if (goodsService.updateById(goods) == 0) {
                logger.error("商品管理->商品管理->上架错误:{}", "更新数据失败");
                throw new RuntimeException("更新数据失败");
            }
        }

        saveGoodsAttributesAndSpecifications(goodsAttributes, goods.getId());

        // 商品货品表Dts_product
        for (TpmGoodsProduct product : products) {
            product.setGoodsId(goods.getId());
            productService.add(product);
        }

        logger.info("【请求结束】商品管理->商品管理->上架,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    private TpmGoods changeToTpmGoods(TpmGoodsVo tpmGoodsVo) {
        TpmGoods tpmGoods = new TpmGoods();
        BeanUtils.copyProperties(tpmGoodsVo, tpmGoods);
        tpmGoods.setSaleRange(SaleRangeEnum.changeToDbSaleRangeByPage(tpmGoodsVo.getSaleRangeList()));
        return tpmGoods;
    }

    public void saveGoodsAttributesAndSpecifications(List<TpmGoodsAttributesDto> goodsAttributes, Integer goodsId) {

        if (CollectionUtils.isEmpty(goodsAttributes)) {
            logger.info("商品属性为空，不做新增操作");
            return;
        }

        // 插入商品属性 商品参数表Dts_goods_attribute
        for (TpmGoodsAttributesDto goodsAttribute : goodsAttributes) {
            TpmGoodsAttribute attribute = new TpmGoodsAttribute();
            attribute.setGoodsId(goodsId);
            attribute.setAttribute(goodsAttribute.getValue());
            attribute.setValue(goodsAttribute.getValue());
            attribute.setSelectNum(goodsAttribute.getSelectNumber());
            attribute.setRequired(goodsAttribute.getRequired());
            attributeService.add(attribute);
            Integer attributeId = attribute.getId();
            for (TpmGoodsSpecificationsDto goodsSpecification : goodsAttribute.getGoodsSpecifications()) {
                TpmGoodsSpecification specification = new TpmGoodsSpecification();
                specification.setGoodsId(goodsId);
                specification.setSpecification(goodsSpecification.getValue());
                specification.setValue(goodsSpecification.getValue());
                specification.setPrice(new BigDecimal(goodsSpecification.getPrice()));
                specification.setPicUrl(goodsSpecification.getPicUrl());
                specification.setEnable(goodsSpecification.getEnable());
                specification.setAttributeId(attributeId);
                specificationService.add(specification);
            }
        }
    }

    private Object goodsAttributesValidate(List<TpmGoodsAttributesDto> goodsAttributes) {

        if (CollectionUtils.isEmpty(goodsAttributes)) {
            return null;
        }
        //todo 任选和属性互斥

        // 属性名称不能为空，不能重复
        Set<String> attributeNames = new HashSet<>();
        for (TpmGoodsAttributesDto goodsAttribute : goodsAttributes) {

            // 兼容下任选多件按钮和数字关系
            if (goodsAttribute.getSelectRequired() == null || goodsAttribute.getSelectRequired() == false) {
                goodsAttribute.setSelectNumber(null);
            }
            String attributeName = goodsAttribute.getValue();
            Boolean required = goodsAttribute.getRequired();
            if (StringUtils.isEmpty(required)) {
                String msg = String.format("商品属性：%s，是否必选不能为空", attributeName);
                return AdminResponseUtil.fail(msg);
            }
            if (StringUtils.isEmpty(attributeName)) {
                logger.error("商品管理->商品管理->上架错误:{}", "商品属性名称不能为空");
                return AdminResponseUtil.fail("商品属性名称不能为空");
            }
            if (attributeNames.contains(attributeName)) {
                logger.error("商品管理->商品管理->上架错误:{}", "商品属性名称不能重复");
                return AdminResponseUtil.fail("商品属性名称不能重复");
            }
            attributeNames.add(attributeName);

            // 每个属性中的规格验证
            List<TpmGoodsSpecificationsDto> goodsSpecifications = goodsAttribute.getGoodsSpecifications();
            if (CollectionUtils.isEmpty(goodsSpecifications)) {
                String msg = String.format("商品属性：%s,规格不能为空", attributeName);
                return AdminResponseUtil.fail(msg);
            }

            Set<String> specificationNames = new HashSet<>();
            for (TpmGoodsSpecificationsDto goodsSpecification : goodsSpecifications) {
                String specificationName = goodsSpecification.getValue();
                if (StringUtils.isEmpty(specificationName)) {
                    String msg = String.format("商品属性：%s,规格名称不能为空", attributeName);
                    return AdminResponseUtil.fail(msg);
                }
                String price = goodsSpecification.getPrice();
                if (StringUtils.isEmpty(price)) {
                    String msg = String.format("商品属性：%s,价格不能为空", attributeName);
                    return AdminResponseUtil.fail(msg);
                }
                boolean isTwoDecimal = Pattern.matches("^-?\\d+\\.\\d{2}$", price);
                if (!isTwoDecimal) {
                    String msg = String.format("商品属性：%s,价格应该保留两位小数", attributeName);
                    return AdminResponseUtil.fail(msg);
                }
                Boolean enable = goodsSpecification.getEnable();
                if (StringUtils.isEmpty(enable)) {
                    String msg = String.format("商品属性：%s，是否启用不能为空", attributeName);
                    return AdminResponseUtil.fail(msg);
                }

                if (specificationNames.contains(specificationName)) {
                    String msg = String.format("商品属性：%s，规格名称重复", attributeName);
                    return AdminResponseUtil.fail(msg);
                }
                specificationNames.add(specificationName);
            }
        }
        return null;
    }

    public Object catAndBrand(Integer businessType) {
        // http://element-cn.eleme.io/#/zh-CN/component/cascader
        // 管理员设置“所属分类”
        List<TpmCategory> l1CatList = categoryService.queryL1(businessType);
        List<CatVo> categoryList = new ArrayList<>(l1CatList.size());

        for (TpmCategory l1 : l1CatList) {
            CatVo l1CatVo = new CatVo();
            l1CatVo.setValue(l1.getId());
            l1CatVo.setLabel(l1.getName());

            List<TpmCategory> l2CatList = categoryService.queryByPid(l1.getId(), businessType);
            List<CatVo> children = new ArrayList<>(l2CatList.size());
            for (TpmCategory l2 : l2CatList) {
                CatVo l2CatVo = new CatVo();
                l2CatVo.setValue(l2.getId());
                l2CatVo.setLabel(l2.getName());
                children.add(l2CatVo);
            }
            l1CatVo.setChildren(children);

            categoryList.add(l1CatVo);
        }

        //品牌商获取需要控制数据权限，如果是店铺管理员下拉的品牌商只能选择当前用户可管理的品牌商
        List<TpmBrand> list = new ArrayList<>();
        List<Map<String, Object>> brandList = new ArrayList<>();
        List<Integer> brandIds = null;
        if (adminDataAuthService.isBrandManager()) {
            list = brandService.getAdminBrands(AuthSupport.adminId());
            logger.info("运营商管理角色操作，需控制数据权限，brandIds:{}", JSONObject.toJSONString(brandIds));
        } else {
            list = brandService.all();
            brandList = new ArrayList<>(list.size());
        }

        for (TpmBrand brand : list) {
            Map<String, Object> b = new HashMap<>(2);
            b.put("value", brand.getId());
            b.put("label", brand.getName());
            brandList.add(b);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("categoryList", categoryList);
        data.put("brandList", brandList);
        return ResponseUtil.ok(data);
    }

    public Object detail(Integer id) {
        TpmGoods _goods = goodsService.findById(id);
        TpmGoodsVo goods = new TpmGoodsVo();
        BeanUtils.copyProperties(_goods, goods);
        goods.setSaleRangeList(SaleRangeEnum.changeToPageSaleRangeByDb(_goods.getSaleRange()));

        List<TpmGoodsProduct> products = productService.queryByGid(id);
        List<TpmGoodsSpecification> specifications = specificationService.queryByGid(id);
        List<TpmGoodsAttribute> attributes = attributeService.queryByGid(id);

        //用于展示商品归属的类目（页面级联下拉控件数据展示）
        Integer categoryId = goods.getCategoryId();
        TpmCategory category = categoryService.findById(categoryId);
        Integer[] categoryIds = new Integer[]{};
        if (category != null) {
            Integer parentCategoryId = category.getPid();
            categoryIds = new Integer[]{parentCategoryId, categoryId};
        }

        List<TpmGoodsAttributesDto> attributesDtoList = attributeSpecificationService.queryByGid(id);
        Map<String, Object> data = new HashMap<>();
        data.put("goods", goods);
        data.put("specifications", specifications);
        data.put("products", products);
        data.put("attributes", attributes);
        data.put("categoryIds", categoryIds);
        data.put("goodsAttributes", attributesDtoList);

        logger.info("【请求结束】商品管理->商品管理->详情,响应结果:{}；响应数据：{}", "成功!", com.alibaba.fastjson.JSON.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object batchSaleOut(List<Integer> goodsIdList, boolean isSellOut) {
        List<TpmGoods> tpmGoodsList = goodsService.findById(goodsIdList);
        tpmGoodsList.forEach(t -> {
            TpmGoods tpmGoods = new TpmGoods();
            tpmGoods.setId(t.getId());
            tpmGoods.setIsSellOut(isSellOut);
            goodsService.updateById(tpmGoods);
            logger.info("【请求结束】商品管理->商品管理->更新是否售罄,id:{} 品名:{} {} ", t.getId(), t.getName(), isSellOut ? "售罄" : "取消售罄");
        });
        return ResponseUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    public Object batchOnSale(List<Integer> goodsIdList, boolean isOnSale) {
        List<TpmGoods> tpmGoodsList = goodsService.findById(goodsIdList);
        tpmGoodsList.forEach(t -> {
            TpmGoods tpmGoods = new TpmGoods();
            tpmGoods.setId(t.getId());
            tpmGoods.setIsOnSale(isOnSale);
            goodsService.updateById(tpmGoods);
            logger.info("【请求结束】商品管理->商品管理->更新是否在售,id:{} 品名:{} {} ", t.getId(), t.getName(), isOnSale ? "在售" : "下架");
        });
        return ResponseUtil.ok();
    }

    public Object updateInventoryNum(Integer id, Integer inventoryNum) {
        TpmGoods tpmGoods = new TpmGoods();
        tpmGoods.setId(id);
        tpmGoods.setInventoryNum(inventoryNum);
        if (inventoryNum <= 0) {
            tpmGoods.setIsSellOut(true);
        } else {
            tpmGoods.setIsSellOut(false);
        }
        goodsService.updateById(tpmGoods);
        return ResponseUtil.ok();
    }
}
