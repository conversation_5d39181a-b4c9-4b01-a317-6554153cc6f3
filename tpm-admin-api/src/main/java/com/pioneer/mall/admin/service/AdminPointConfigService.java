package com.pioneer.mall.admin.service;

import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.admin.vo.req.TpmPointExchangeConfigReqVo;
import com.pioneer.mall.admin.vo.res.TpmPointExchangeConfigResVo;
import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmPointExchangeConfig;
import com.pioneer.mall.db.service.TpmCouponService;
import com.pioneer.mall.db.service.TpmPointExchangeConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AdminPointConfigService {

    @Resource
    private TpmPointExchangeConfigService tpmPointExchangeConfigService;

    @Resource
    private TpmCouponService tpmCouponService;

    public List<TpmPointExchangeConfigResVo> pointConfigList(Integer page, Integer limit, String name, String sort, String order) {

        List<TpmPointExchangeConfig> tpmPointExchangeConfigList = tpmPointExchangeConfigService.querySelective(null, name, page, limit, sort, order);
        if (CollectionUtils.isEmpty(tpmPointExchangeConfigList)) {
            return Collections.emptyList();
        }
        return tpmPointExchangeConfigList.stream()
                .map(z -> {
                    TpmCoupon tpmCoupon = tpmCouponService.findById(z.getCouponId());
                    TpmPointExchangeConfigResVo tpmPointExchangeConfigResVo = new TpmPointExchangeConfigResVo();
                    // 设置参数
                    BeanUtils.copyProperties(z, tpmPointExchangeConfigResVo);
                    String couponName = tpmCoupon == null ? "" : tpmCoupon.getName();
                    tpmPointExchangeConfigResVo.setCouponName(couponName);
                    return tpmPointExchangeConfigResVo;
                }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void pointConfigAdd(TpmPointExchangeConfigReqVo data) {

        TpmPointExchangeConfig tpmPointExchangeConfig = new TpmPointExchangeConfig();
        tpmPointExchangeConfig.setName(data.getName());
        tpmPointExchangeConfig.setAmount(data.getAmount());
        tpmPointExchangeConfig.setCouponId(data.getCouponId());
        tpmPointExchangeConfig.setDescription(data.getDescription());
        tpmPointExchangeConfig.setSortOrder(data.getSortOrder());
        tpmPointExchangeConfig.setCreateTime(LocalDateTime.now());
        tpmPointExchangeConfig.setUpdateTime(LocalDateTime.now());
        tpmPointExchangeConfig.setOperator(AuthSupport.userName());
        tpmPointExchangeConfig.setDeleted(false);
        tpmPointExchangeConfig.setEnable(data.getEnable());
        tpmPointExchangeConfigService.add(tpmPointExchangeConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateByPrimaryKeySelective(TpmPointExchangeConfigReqVo data) {

        TpmPointExchangeConfig tpmPointExchangeConfig = new TpmPointExchangeConfig();
        tpmPointExchangeConfig.setId(data.getId());
        tpmPointExchangeConfig.setSortOrder(data.getSortOrder());
        tpmPointExchangeConfig.setName(data.getName());
        tpmPointExchangeConfig.setAmount(data.getAmount());
        tpmPointExchangeConfig.setCouponId(data.getCouponId());
        tpmPointExchangeConfig.setDescription(data.getDescription());
        tpmPointExchangeConfig.setEnable(data.getEnable());
        tpmPointExchangeConfig.setUpdateTime(LocalDateTime.now());
        tpmPointExchangeConfig.setOperator(AuthSupport.userName());
        tpmPointExchangeConfigService.updateByPrimaryKeySelective(tpmPointExchangeConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        TpmPointExchangeConfig tpmPointExchangeConfig = tpmPointExchangeConfigService.findById(id);
        if (tpmPointExchangeConfig == null) {
            throw new RuntimeException("数据不存在");
        }
        tpmPointExchangeConfigService.deleteById(id);
    }


}
