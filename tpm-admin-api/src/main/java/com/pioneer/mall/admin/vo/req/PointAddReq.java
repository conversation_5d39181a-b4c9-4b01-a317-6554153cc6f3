package com.pioneer.mall.admin.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class PointAddReq {

    /**
     * 会员ID
     */
    @NotNull(message = "会员id不能为空")
    private Integer userId;

    /**
     * 积分变化数量
     */
    @NotNull(message = "积分变化数量不能为空")
    private BigDecimal amount;

    /**
     * 备注说明
     */
    @NotNull(message = "备注说明不能为空")
    private String description;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

}
