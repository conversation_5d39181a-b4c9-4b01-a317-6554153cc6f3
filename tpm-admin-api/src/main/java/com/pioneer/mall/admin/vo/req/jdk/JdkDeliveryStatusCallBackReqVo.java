package com.pioneer.mall.admin.vo.req.jdk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/30 21:55
 */
@Data
public class JdkDeliveryStatusCallBackReqVo {
    //订单id
    @JsonProperty("order_id")
    private String orderId;
    //运力状态 1:接单,2:到店,3:配送,4:完成,5:异常，6=发单失败，7=运力取消
    private Integer type;
    //实际扣款的小费
    @JsonProperty("tip_fee")
    private Integer tipFee;
    //实际扣款的运费
    @JsonProperty("freight_fee")
    private Integer freightFee;
    //接单快递员名字
    @JsonProperty("courier_name")
    private String courierName;
    //接单快递员电话
    @JsonProperty("courier_phone")
    private String courierPhone;
    //接单的品牌
    @JsonProperty("delivery_brand")
    private String deliveryBrand;
    //接单实际距离
    @JsonProperty("delivery_distance")
    private String deliveryDistance;
    //运力订单
    @JsonProperty("delivery_order_no")
    private String deliveryOrderNo;
    //取消原因（6，7）才有
    private String reason;
    //取消，异常来源 1=用户，2=运力平台，3=运力用户，4=运力快递员，5=外卖平台，6=外卖商户， 7=外卖顾客，8=运力异常，10=发单后48小时内无人接单超时系统取消
    private Integer from;
}
