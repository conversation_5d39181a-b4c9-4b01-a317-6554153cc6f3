package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain.TpmAddress;
import com.pioneer.mall.db.domain.TpmRegion;
import com.pioneer.mall.db.service.TpmAddressService;
import com.pioneer.mall.db.service.TpmRegionService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/admin/address")
@Validated
public class AdminAddressController {
	private static final Logger logger = LoggerFactory.getLogger(AdminAddressController.class);

	@Autowired
	private TpmAddressService addressService;

	@Autowired
	private TpmRegionService regionService;

	private Map<String, Object> toVo(TpmAddress address) {
        if (address == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> addressVo = new HashMap<>();
        addressVo.put("id", address.getId());
        addressVo.put("userId", address.getUserId());
        addressVo.put("name", address.getName());
        addressVo.put("mobile", address.getMobile());
        addressVo.put("isDefault", address.getIsDefault());
        addressVo.put("provinceId", address.getProvinceId());
        addressVo.put("cityId", address.getCityId());
        addressVo.put("areaId", address.getAreaId());
		addressVo.put("address", address.getAddressLocation() + address.getAddressDetail());
        TpmRegion provinceRegion = regionService.findById(address.getProvinceId());
        String province = provinceRegion == null ? "" : provinceRegion.getName();
        TpmRegion cityRegion = regionService.findById(address.getCityId());
        String city = cityRegion == null ? "" : cityRegion.getName();
        TpmRegion areaRegion = regionService.findById(address.getAreaId());
        String area = areaRegion == null ? "" : areaRegion.getName();
        addressVo.put("province", province);
        addressVo.put("city", city);
        addressVo.put("area", area);
		return addressVo;
	}

	@RequiresPermissions("admin:address:list")
	@RequiresPermissionsDesc(menu = { "用户管理", "收货地址" }, button = "查询")
	@GetMapping("/list")
	public Object list(Integer userId, String name, @RequestParam(defaultValue = "1") Integer page,
			@RequestParam(defaultValue = "10") Integer limit,
			@Sort @RequestParam(defaultValue = "add_time") String sort,
			@Order @RequestParam(defaultValue = "desc") String order) {
		logger.info("【请求开始】操作人:[" + AuthSupport.userName()+ "] 用户管理->收货地址->查询,请求参数:name:{},userId:{},page:{}", name, userId, page);

		List<TpmAddress> addressList = addressService.querySelective(userId, name, page, limit, sort, order);
		long total = PageInfo.of(addressList).getTotal();

		List<Map<String, Object>> addressVoList = new ArrayList<>(addressList.size());
		for (TpmAddress address : addressList) {
			Map<String, Object> addressVo = toVo(address);
			if (!CollectionUtils.isEmpty(addressVo)) {
				addressVoList.add(addressVo);
			}
		}

		Map<String, Object> data = new HashMap<>();
		data.put("total", total);
		data.put("items", addressVoList);

		logger.info("【请求结束】用户管理->收货地址->查询,响应结果:{}", JSONObject.toJSONString(data));
		return ResponseUtil.ok(data);
	}
}
