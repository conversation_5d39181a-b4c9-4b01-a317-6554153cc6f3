package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.NumberUtils;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.db.bean.search.TpmBalanceSearch;
import com.pioneer.mall.db.domain.TpmBalance;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.service.TpmBalanceService;
import com.pioneer.mall.db.service.TpmUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 余额管理controller
 */
@RestController
@RequestMapping("/admin/balance")
@Validated
public class AdminBalanceController {

    private static final Logger logger = LoggerFactory.getLogger(AdminAdController.class);

    /**
     * 余额服务接口
     * */
    @Autowired
    private TpmBalanceService tpmBalanceService;

    /**
     * 会员服务接口
     * */
    @Autowired
    private TpmUserService userService;


    /**
     * 余额明细列表查询
     * @return 余额明细列表
     */
    @GetMapping( "/list")
    @CrossOrigin
    public Object list(@RequestBody TpmBalanceSearch tpmBalanceSearch) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] 余额管理->查询,请求参数:page:{}", JSONObject.toJSONString(tpmBalanceSearch));

//        Integer storeId = accountInfo.getStoreId();
//        if (storeId != null && storeId > 0) {
//            searchParams.put("storeId", storeId);
//        }
//        if (accountInfo.getMerchantId() != null && accountInfo.getMerchantId() > 0) {
//            searchParams.put("merchantId", accountInfo.getMerchantId());
//        }
//
//        PaginationRequest paginationRequest = new PaginationRequest();
//        paginationRequest.setCurrentPage(page);
//        paginationRequest.setPageSize(pageSize);
//        paginationRequest.setSearchParams(searchParams);

        List<TpmBalance> tpmBalanceList = tpmBalanceService.querySelective(tpmBalanceSearch);
        Map<String, Object> result = new HashMap<>();
        result.put("paginationResponse", tpmBalanceList);
        logger.info("【请求结束】 余额管理->查询,响应结果:{}", JSONObject.toJSONString(result));
        return ResponseUtil.ok(result);
    }

    /**
     * 提交充值（单个会员）
     *
//     * @param request HttpServletRequest对象
     * @return
     */
//    @ApiOperation(value = "提交充值")
    @RequestMapping(value = "/doRecharge", method = RequestMethod.POST)
    @CrossOrigin
//    @PreAuthorize("@pms.hasPermission('balance:modify')")
    public Object doRecharge(@RequestBody Map<String, Object> param) throws Exception {
        String amount = param.get("amount") == null ? "0" : param.get("amount").toString();
        String remark = param.get("remark") == null ? "后台充值" : param.get("remark").toString();
        Integer userId = param.get("userId") == null ? 0 : Integer.parseInt(param.get("userId").toString());
        Integer type = param.get("type") == null ? 1 : Integer.parseInt(param.get("type").toString());// 1 增加，2 扣减

//        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
//        if (accountInfo == null) {
//            return getFailureResult(1001, "请先登录");
//        }
        if (!NumberUtils.isNumberString(amount)){
            return ResponseUtil.fail(201, "充值金额必须是数字");
        }
        if(Objects.isNull(userId)){
            return ResponseUtil.fail(201, "充值会员信息不能为空");
        }

//        String operator = accountInfo.getAccountName();
        TpmBalance tpmBalance = new TpmBalance();
        TpmUser tpmUser = userService.findById(userId);
        BigDecimal userLeftBalance = BigDecimal.ZERO;
        if (Objects.nonNull(tpmUser.getBalance())){
            userLeftBalance = tpmUser.getBalance();
        }
        // 扣减余额
        if (type == 2) {
            if (tpmUser.getBalance().compareTo(new BigDecimal(amount)) < 0) {
                return  ResponseUtil.fail(201, "操作失败，会员余额不足");
            }
            tpmBalance.setAmount(new BigDecimal(amount).subtract(new BigDecimal(amount).multiply(new BigDecimal("2"))));
            userLeftBalance = userLeftBalance.subtract(new BigDecimal(amount));
        } else {
            tpmBalance.setAmount(new BigDecimal(amount));
            userLeftBalance = userLeftBalance.add(new BigDecimal(amount));
        }
        tpmBalance.setLeftBalance(userLeftBalance);
        tpmBalance.setUserId(userId);
        tpmBalance.setOperator(tpmUser.getNickname());
        tpmBalance.setOrderSn("");
        tpmBalance.setRemark(remark);
        tpmBalanceService.addBalance(tpmBalance, true);
        return ResponseUtil.ok(true);

    }

    /**
     * 发放余额
     *
     * @param request HttpServletRequest对象
     * @return
     */
//    @ApiOperation(value = "发放余额")
    @RequestMapping(value = "/distribute", method = RequestMethod.POST)
    @CrossOrigin
//    @PreAuthorize("@pms.hasPermission('balance:distribute')")
    public Object distribute(HttpServletRequest request, @RequestBody Map<String, Object> param) throws Exception {
        String token = request.getHeader("Access-Token");
        String amount = param.get("amount") == null ? "0" : param.get("amount").toString();
        String remark = param.get("remark") == null ? "后台充值" : param.get("remark").toString();
        String userIds = param.get("userIds") == null ? "" : param.get("userIds").toString();
        String object = param.get("object") == null ? "" : param.get("object").toString();

//        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
//        if (accountInfo == null) {
//            return getFailureResult(1001, "请先登录");
//        }
        if (!NumberUtils.isNumberString(amount)) {
            throw new Exception("充值金额必须是数字");
        }
        try {
            tpmBalanceService.distribute(object, userIds, amount, remark);
            return ResponseUtil.ok(true);
        } catch (Exception e) {
            return ResponseUtil.fail(201,e.getMessage());
        }
    }
//
//    /**
//     * 充值设置详情
//     *
//     * @param request
//     * @return
//     */
////    @ApiOperation(value = "充值设置详情")
//    @RequestMapping(value = "/setting", method = RequestMethod.GET)
//    @CrossOrigin
////    @PreAuthorize("@pms.hasPermission('balance:setting')")
//    public ResponseObject setting(HttpServletRequest request) throws BusinessCheckException {
//        String token = request.getHeader("Access-Token");
//        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
//        if (accountInfo == null) {
//            return getFailureResult(1001, "请先登录");
//        }
//
//        List<MtSetting> settingList = settingService.getSettingList(accountInfo.getMerchantId(), SettingTypeEnum.BALANCE.getKey());
//
//        List<RechargeRuleDto> rechargeRuleList = new ArrayList<>();
//        String remark = "";
//        String status = "";
//        if (settingList.size() > 0) {
//            for (MtSetting setting : settingList) {
//                 if (setting.getName().equals(BalanceSettingEnum.RECHARGE_RULE.getKey())) {
//                     status = setting.getStatus();
//                     String item[] = setting.getValue().split(",");
//                     if (item.length > 0) {
//                         for (String value : item) {
//                              String el[] = value.split("_");
//                              if (el.length == 2) {
//                                  RechargeRuleDto e = new RechargeRuleDto();
//                                  e.setRechargeAmount(el[0]);
//                                  e.setGiveAmount(el[1]);
//                                  rechargeRuleList.add(e);
//                              }
//                         }
//                     }
//                 } else if(setting.getName().equals(BalanceSettingEnum.RECHARGE_REMARK.getKey())) {
//                     remark = setting.getValue();
//                 }
//            }
//        }
//
//        Map<String, Object> result = new HashMap();
//        result.put("rechargeRuleList", rechargeRuleList);
//        result.put("remark", remark);
//        result.put("status", status);
//
//        return getSuccessResult(result);
//    }
//
//    /**
//     * 保存充值设置
//     *
//     * @param request HttpServletRequest对象
//     * @return
//     */
//    @ApiOperation(value = "保存充值设置")
//    @RequestMapping(value = "/saveSetting", method = RequestMethod.POST)
//    @CrossOrigin
//    @PreAuthorize("@pms.hasPermission('balance:setting')")
//    public ResponseObject saveSetting(HttpServletRequest request, @RequestBody Map<String, Object> param) throws BusinessCheckException {
//        String token = request.getHeader("Access-Token");
//        String status = param.get("status") == null ? StatusEnum.ENABLED.getKey() : param.get("status").toString();
//        String remark = param.get("remark") == null ? "" : param.get("remark").toString();
//        List<LinkedHashMap> rechargeItems = (List) param.get("rechargeItem");
//
//        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
//        if (accountInfo == null) {
//            return getFailureResult(1001, "请先登录");
//        }
//
//        if (rechargeItems.size() < 0) {
//            return getFailureResult(201, "充值规则设置不能为空");
//        }
//
//        String rechargeRule = "";
//        List<String> amounts = new ArrayList<>();
//        for (LinkedHashMap item : rechargeItems) {
//             String amount = item.get("rechargeAmount").toString();
//             if (amounts.contains(amount)) {
//                 return getFailureResult(201, "充值金额设置不能有重复");
//             }
//             if (rechargeRule.length() == 0) {
//                 rechargeRule = item.get("rechargeAmount").toString() + '_' + item.get("giveAmount").toString();
//             } else {
//                 rechargeRule = rechargeRule + ',' + item.get("rechargeAmount").toString() + '_' + item.get("giveAmount").toString();
//             }
//             amounts.add(amount);
//        }
//
//        MtSetting setting = new MtSetting();
//        setting.setMerchantId(accountInfo.getMerchantId());
//        setting.setType(SettingTypeEnum.BALANCE.getKey());
//        setting.setName(BalanceSettingEnum.RECHARGE_RULE.getKey());
//        setting.setValue(rechargeRule);
//        setting.setDescription(BalanceSettingEnum.RECHARGE_RULE.getValue());
//        setting.setStatus(status);
//        setting.setOperator(accountInfo.getAccountName());
//        setting.setUpdateTime(new Date());
//        settingService.saveSetting(setting);
//
//        // 保存充值说明
//        MtSetting settingRemark = new MtSetting();
//        settingRemark.setMerchantId(accountInfo.getMerchantId());
//        settingRemark.setType(SettingTypeEnum.BALANCE.getKey());
//        settingRemark.setName(BalanceSettingEnum.RECHARGE_REMARK.getKey());
//        settingRemark.setValue(remark);
//        settingRemark.setDescription("");
//        settingRemark.setStatus(status);
//        settingRemark.setOperator(accountInfo.getAccountName());
//        settingRemark.setUpdateTime(new Date());
//        settingService.saveSetting(settingRemark);
//
//        return getSuccessResult(true);
//    }
}
