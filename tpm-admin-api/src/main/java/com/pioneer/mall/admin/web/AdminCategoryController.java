package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.admin.vo.res.TpmCategoryResVo;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.db.domain.TpmCategory;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.service.TpmCategoryService;
import com.pioneer.mall.db.service.TpmShopInfoService;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin/category")
@Validated
public class AdminCategoryController {
    private static final Logger logger = LoggerFactory.getLogger(AdminCategoryController.class);

    @Autowired
    private TpmCategoryService categoryService;
    @Autowired
    private TpmShopInfoService tpmShopInfoService;

    @RequiresPermissions("admin:category:list")
    @RequiresPermissionsDesc(menu = {"商场管理", "类目管理"}, button = "查询")
    @GetMapping("/list")
    public Object list(String id, String name, @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "1") Integer businessType,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @RequestParam(required = false) Integer shopId,
                       @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {

        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }

        if (shopId != null && !userShopIdList.contains(shopId)) {
            return ResponseUtil.fail("当前用户没有此店铺权限");
        }

        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->类目管理->查询,请求参数:name:{},page:{},业务类型：{}", name, page, businessType);
        List<TpmCategory> collectList = categoryService.querySelective(id, name, businessType, page, limit, sort, order, shopId);
        long total = PageInfo.of(collectList).getTotal();

        List<Integer> shopIdList = collectList.stream().map(TpmCategory::getShopId).distinct().collect(Collectors.toList());
        Map<Integer, String> shopInfoMap = tpmShopInfoService.selectShopInfo(shopIdList);

        List<TpmCategoryResVo> categoryResVoList = collectList.stream()
                .map(category -> {
                    TpmCategoryResVo categoryResVo = new TpmCategoryResVo();
                    BeanUtils.copyProperties(category, categoryResVo);
                    categoryResVo.setShopName(shopInfoMap.get(category.getShopId()));
                    return categoryResVo;
                }).collect(Collectors.toList());

        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", categoryResVoList);

        logger.info("【请求结束】商场管理->类目管理->查询:total:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @GetMapping("/L2")
    @RequiresPermissions("admin:category:categoryL2")
    @RequiresPermissionsDesc(menu = {"商场管理", "类目列表"}, button = "查询")
    public Object categoryL2(@RequestParam(defaultValue = "1") Integer businessType,
                             @RequestParam(required = false) Integer shopId) {

        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        if (shopId != null && !userShopIdList.contains(shopId)) {
            return ResponseUtil.fail("当前用户没有此店铺权限");
        }
        List<TpmCategory> tpmCategories = categoryService.adminQueryL2(businessType, shopId);
        List<Map<String, Object>> data = new ArrayList<>();
        for (TpmCategory category : tpmCategories) {
            Map<String, Object> b = new HashMap<>(2);
            b.put("value", category.getId());
            b.put("label", category.getName());
            data.add(b);
        }
        Map<String, Object> back = new HashMap<>();
        back.put("categoryL2List", data);
        logger.info("【请求结束】商品分类类目 L2 ,响应结果:{}", JSONObject.toJSONString(tpmCategories));
        return ResponseUtil.ok(back);
    }

    private Object validate(TpmCategory category) {
        String name = category.getName();
        if (StringUtils.isEmpty(name)) {
            return ResponseUtil.badArgument();
        }
        // 判断shop Id
        Integer shopId = category.getShopId();
        if (shopId == null) {
            return ResponseUtil.fail("店铺ID不能空");
        }

        Integer businessType = category.getBusinessType();
        if (businessType == null) {
            return ResponseUtil.fail("业务类型不能空");
        }

        String level = category.getLevel();
        if (StringUtils.isEmpty(level)) {
            return ResponseUtil.badArgument();
        }
        if (!level.equals("L1") && !level.equals("L2")) {
            return ResponseUtil.badArgumentValue();
        }

        Integer pid = category.getPid();
        if (level.equals("L2") && (pid == null)) {
            return ResponseUtil.badArgument();
        }

        return null;
    }

    @RequiresPermissions("admin:category:create")
    @RequiresPermissionsDesc(menu = {"商场管理", "类目管理"}, button = "添加")
    @PostMapping("/create")
    public Object create(@RequestBody TpmCategory category) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->类目管理->添加,请求参数:{}", JSONObject.toJSONString(category));

        // 业务类型为空，则设置为自提/配送
        if (category.getBusinessType() == null) {
            category.setBusinessType(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode());
        }

        Object error = validate(category);
        if (error != null) {
            return error;
        }
        categoryService.add(category);

        logger.info("【请求结束】商场管理->类目管理->添加:响应结果:{}", JSONObject.toJSONString(category));
        return ResponseUtil.ok(category);
    }

    @RequiresPermissions("admin:category:read")
    @RequiresPermissionsDesc(menu = {"商场管理", "类目管理"}, button = "详情")
    @GetMapping("/read")
    public Object read(@NotNull Integer id) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->类目管理->详情,请求参数,id:{}", id);
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        TpmCategory category = categoryService.findById(id);

        logger.info("【请求结束】商场管理->类目管理->详情:响应结果:{}", JSONObject.toJSONString(category));
        return ResponseUtil.ok(category);
    }

    @RequiresPermissions("admin:category:update")
    @RequiresPermissionsDesc(menu = {"商场管理", "类目管理"}, button = "编辑")
    @PostMapping("/update")
    public Object update(@RequestBody TpmCategory category) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->类目管理->编辑,请求参数:{}", JSONObject.toJSONString(category));
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        Object error = validate(category);
        if (error != null) {
            return error;
        }

        if (categoryService.updateById(category) == 0) {
            logger.error("商场管理->类目管理->编辑 失败，更新数据失败！");
            return ResponseUtil.updatedDataFailed();
        }

        logger.info("【请求结束】商场管理->类目管理->编辑:响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    @RequiresPermissions("admin:category:delete")
    @RequiresPermissionsDesc(menu = {"商场管理", "类目管理"}, button = "删除")
    @PostMapping("/delete")
    public Object delete(@RequestBody TpmCategory category) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->类目管理->删除,请求参数:{}", JSONObject.toJSONString(category));
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        Integer id = category.getId();
        if (id == null) {
            return ResponseUtil.badArgument();
        }
        categoryService.deleteById(id);

        logger.info("【请求结束】商场管理->类目管理->删除:响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    @RequiresPermissions("admin:category:catL1")
    @GetMapping("/l1")
    public Object catL1(@RequestParam(defaultValue = "1") Integer businessType,
                        @RequestParam(required = false) Integer shopId) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->类目管理->一级分类目录查询");
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        // 所有一级分类目录
        List<TpmCategory> l1CatList = categoryService.adminQueryL1(businessType, shopId);
        List<Map<String, Object>> data = new ArrayList<>(l1CatList.size());
        for (TpmCategory category : l1CatList) {
            Map<String, Object> d = new HashMap<>(2);
            d.put("value", category.getId());
            d.put("label", category.getName());
            data.add(d);
        }

        logger.info("【请求结束】商场管理->类目管理->一级分类目录查询:total:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }
}
