package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.db.domain.TpmCouponCode;
import com.pioneer.mall.db.domain.TpmCouponCodeUseLog;
import com.pioneer.mall.db.service.TpmCouponCodeService;
import com.pioneer.mall.db.service.TpmCouponCodeUseLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 优惠口令
 * @date 2024/12/17 21:31
 */
@Slf4j
@RestController
@RequestMapping("/admin/coupon/code")
public class AdminCouponCodeController {
    @Autowired
    private TpmCouponCodeService tpmCouponCodeService;
    @Autowired
    private TpmCouponCodeUseLogService tpmCouponCodeUseLogService;
    @RequiresPermissions("admin:coupon:code:list")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠口令列表"}, button = "查询")
    @GetMapping("/list")
    public Object list(String code, Integer page, Integer limit, String sort, String order) {
        log.info("【请求结束】推广管理->优惠口令管理->查询 操作者:{}", AuthSupport.userName());
        List<TpmCouponCode> tpmCouponCodes = tpmCouponCodeService.queryList(code, page, limit, sort, order);
        long total = PageInfo.of(tpmCouponCodes).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("codes", tpmCouponCodes);
        log.info("【请求结束】商品管理->商品管理->查询,响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @RequiresPermissions("admin:coupon:code:listUseLog")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠使用记录"}, button = "查询使用记录")
    @GetMapping("/listUseLog")
    public Object listUseLog( Integer codeId,Integer userId,Integer page, Integer limit, String sort, String order) {
        log.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠口令管理->查询使用记录");
        List<TpmCouponCodeUseLog> tpmCouponCodeUseLogs = tpmCouponCodeUseLogService.queryUseList(codeId, userId, page, limit, sort, order);
        long total = PageInfo.of(tpmCouponCodeUseLogs).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("logs", tpmCouponCodeUseLogs);
        log.info("【请求结束】商品管理->商品管理->查询,响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @RequiresPermissions("admin:coupon:code:add")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠口令管理"}, button = "添加")
    @PostMapping("/create")
    public Object create(@RequestBody TpmCouponCode tpmCouponCode) {
        log.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->添加,请求参数:{}", JSONObject.toJSONString(tpmCouponCode));
        try {
            int add = tpmCouponCodeService.add(tpmCouponCode);
            if (add==1){
                log.info("【请求结束】推广管理->优惠券管理->添加成功");
                return ResponseUtil.ok();
            }
        }catch (Exception e){
            log.error("添加优惠口令失败:{}",e.getMessage(),e);
            return ResponseUtil.fail(-1,e.getMessage());
        }
        return ResponseUtil.fail("添加失败，请联系技术处理");
    }

    @RequiresPermissions("admin:coupon:code:update")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券更新"}, button = "添加")
    @PostMapping("/update")
    public Object update(@RequestBody TpmCouponCode tpmCouponCode) {
        log.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->更新,请求参数:{}", JSONObject.toJSONString(tpmCouponCode));
        try {
            int updated = tpmCouponCodeService.updateById(tpmCouponCode);
            if (updated==1){
                log.info("【请求结束】推广管理->优惠券管理->更新成功");
                return ResponseUtil.ok();
            }
        }catch (Exception e){
            log.error("更新优惠口令失败:{}",e.getMessage(),e);
            return ResponseUtil.fail(-1,e.getMessage());
        }
        return ResponseUtil.fail("更新失败，请联系技术处理");
    }


}
