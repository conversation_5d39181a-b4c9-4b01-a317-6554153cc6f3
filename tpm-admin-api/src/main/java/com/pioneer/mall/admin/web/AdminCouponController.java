package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCouponUser;
import com.pioneer.mall.db.service.TpmCouponService;
import com.pioneer.mall.db.service.TpmCouponUserService;
import com.pioneer.mall.db.util.CouponConstant;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin/coupon")
@Validated
public class AdminCouponController {
    private static final Logger logger = LoggerFactory.getLogger(AdminCouponController.class);

    @Autowired
    private TpmCouponService couponService;
    @Autowired
    private TpmCouponUserService couponUserService;

    @RequiresPermissions("admin:coupon:list:all")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券列表"}, button = "查询")
    @GetMapping("/list/all")
    public Object listAll() {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->查询所有");

        List<TpmCoupon> couponList = couponService.queryList(0, 1000);
        List<Map<String, Object>> couponTypeList = new ArrayList<>();
        if (CollectionUtils.isEmpty(couponList)) {
            return couponTypeList;
        }
        couponTypeList = couponList.stream()
                .map(z -> {
                    Map<String, Object> b = new HashMap<>();
                    b.put("value", z.getId());
                    b.put("label", z.getName());
                    return b;
                }).collect(Collectors.toList());

        Map<String, Object> data = new HashMap<>();
        data.put("couponList", couponTypeList);
        logger.info("【请求结束】推广管理->优惠券管理->查询所有:响应结果:{}", "成功!");
        return ResponseUtil.ok(data);
    }

    @RequiresPermissions("admin:coupon:list")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券管理"}, button = "查询")
    @GetMapping("/list")
    public Object list(String name, Short type, Short status, @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @Sort @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->查询,请求参数:name:{},page:{}", name, page);

        List<TpmCoupon> couponList = couponService.querySelective(name, type, status, page, limit, sort, order);
        long total = PageInfo.of(couponList).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        // 给个默认值
        if (!CollectionUtils.isEmpty(couponList)) {
            couponList = couponList.stream()
                    .map(z -> {
                        if (StringUtils.isEmpty(z.getPicUrl())) {
                            z.setPicUrl("空");
                        }
                        return z;
                    }).collect(Collectors.toList());
        }
        data.put("items", couponList);

        logger.info("【请求结束】推广管理->优惠券管理->查询:响应结果:{}", "成功!");
        return ResponseUtil.ok(data);
    }

    @RequiresPermissions("admin:coupon:listuser")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券管理"}, button = "查询用户")
    @GetMapping("/listuser")
    public Object listuser(Integer userId, Integer couponId, Short status,
                           @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer limit,
                           @Sort @RequestParam(defaultValue = "add_time") String sort,
                           @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->查询用户,请求参数:userId:{},couponId:{}", userId, couponId);

        List<TpmCouponUser> couponList = couponUserService.queryList(userId, couponId, status, page, limit, sort,
                order);
        long total = PageInfo.of(couponList).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", couponList);

        logger.info("【请求结束】推广管理->优惠券管理->查询用户:响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    private Object validate(TpmCoupon coupon) {
        String name = coupon.getName();
        if (StringUtils.isEmpty(name)) {
            return ResponseUtil.fail("优惠券名称不能为空");
        }
        if (Objects.equals(coupon.getTimeType(), Integer.valueOf(0).shortValue()) && coupon.getDays() == null) {
            return ResponseUtil.fail("有效期为相对天数：天数不能为空");
        }
        if (Objects.equals(coupon.getTimeType(), Integer.valueOf(1).shortValue())) {
            if (coupon.getStartTime() == null || coupon.getEndTime() == null) {
                return ResponseUtil.fail("有效期为绝对时间：开始时间和结束时间不能为空");
            }
        }
        return null;
    }

    @RequiresPermissions("admin:coupon:create")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券管理"}, button = "添加")
    @PostMapping("/create")
    public Object create(@RequestBody TpmCoupon coupon) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->添加,请求参数:{}", JSONObject.toJSONString(coupon));

        Object error = validate(coupon);
        if (error != null) {
            return error;
        }

        // 如果是兑换码类型，则这里需要生存一个兑换码
        if (coupon.getType().equals(CouponConstant.TYPE_CODE)) {
            String code = couponService.generateCode();
            coupon.setCode(code);
        }

        couponService.add(coupon);

        logger.info("【请求结束】推广管理->优惠券管理->添加,响应结果:{}", JSONObject.toJSONString(coupon));
        return ResponseUtil.ok(coupon);
    }

    @RequiresPermissions("admin:coupon:read")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券管理"}, button = "详情")
    @GetMapping("/read")
    public Object read(@NotNull Integer id) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->详情,请求参数,id:{}", id);

        TpmCoupon coupon = couponService.findById(id);

        logger.info("【请求结束】推广管理->优惠券管理->详情,响应结果:{}", JSONObject.toJSONString(coupon));
        return ResponseUtil.ok(coupon);
    }

    @RequiresPermissions("admin:coupon:update")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券管理"}, button = "编辑")
    @PostMapping("/update")
    public Object update(@RequestBody TpmCoupon coupon) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->编辑,请求参数:{}", JSONObject.toJSONString(coupon));

        Object error = validate(coupon);
        if (error != null) {
            return error;
        }
        if (couponService.updateById(coupon) == 0) {
            return ResponseUtil.updatedDataFailed();
        }

        logger.info("【请求结束】推广管理->优惠券管理->编辑,响应结果:{}", JSONObject.toJSONString(coupon));
        return ResponseUtil.ok(coupon);
    }

    @RequiresPermissions("admin:coupon:delete")
    @RequiresPermissionsDesc(menu = {"推广管理", "优惠券管理"}, button = "删除")
    @PostMapping("/delete")
    public Object delete(@RequestBody TpmCoupon coupon) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 推广管理->优惠券管理->删除,请求参数:{}", JSONObject.toJSONString(coupon));

        couponService.deleteById(coupon.getId());

        logger.info("【请求结束】推广管理->优惠券管理->删除,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

}
