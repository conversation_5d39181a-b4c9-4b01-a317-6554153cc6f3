package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain.TpmFreightStepPrice;
import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.service.TpmFreightStepPriceService;
import com.pioneer.mall.db.service.TpmShopInfoService;
import com.pioneer.mall.db.service.TpmVipService;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/freight/")
public class AdminFreightStepPriceController {

    private static final Logger logger = LoggerFactory.getLogger(AdminFreightStepPriceController.class);


    @Autowired
    private TpmFreightStepPriceService freightStepPriceService;
    @Autowired
    private TpmVipService tpmVipService;
    @Autowired
    private TpmShopInfoService tpmShopInfoService;

    @GetMapping("/list")
    @CrossOrigin
    @RequiresPermissions("admin:freight:list")
    @RequiresPermissionsDesc(menu = {"配置管理", "运费阶梯价"}, button = "查询")
    public Object list(@RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @RequestParam(required = false) String name,
                       @RequestParam(required = false) Integer shopId,
                       @Sort(accepts = {"add_time", "id", "sort"}) @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] 运费阶梯价->查询,请求参数:name:{}", name);
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        if (shopId != null && !userShopIdList.contains(shopId)) {
            return ResponseUtil.fail("当前用户没有此店铺权限");
        }
        TpmFreightStepPrice freightStepPrice = new TpmFreightStepPrice();
        freightStepPrice.setShopName(name);
        List<TpmFreightStepPrice> freightStepPrices = freightStepPriceService.querySelective(freightStepPrice, sort, order, page, limit, shopId);
        long total = PageInfo.of(freightStepPrices).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", freightStepPrices);
        logger.info("【请求结束】运费阶梯价->查询:响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @GetMapping("/detail")
    @CrossOrigin
    public Object detail(@RequestBody TpmFreightStepPrice tpmFreightStepPrice) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] 运费阶梯价->详情,请求参数:page:{}", JSONObject.toJSONString(tpmFreightStepPrice));

        TpmFreightStepPrice freightStepPrice = freightStepPriceService.findById(tpmFreightStepPrice.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("vip", freightStepPrice);
        logger.info("【请求结束】 运费阶梯价->详情,响应结果:{}", JSONObject.toJSONString(result));
        return ResponseUtil.ok(result);
    }

    @PostMapping("/create")
    @CrossOrigin
    @RequiresPermissions("admin:freight:add")
    @RequiresPermissionsDesc(menu = {"配置管理", "运费阶梯价"}, button = "新增")
    public Object add(@RequestBody TpmFreightStepPrice tpmFreightStepPrice) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] 运费阶梯价->添加,请求参数:{}", JSONObject.toJSONString(tpmFreightStepPrice));
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        if (tpmFreightStepPrice.getShopId() == null) {
            return ResponseUtil.fail("请选择店铺");
        }
        if (tpmFreightStepPrice.getShopId() != null && !userShopIdList.contains(tpmFreightStepPrice.getShopId())) {
            return ResponseUtil.fail("当前用户没有此店铺权限");
        }
        TpmShopInfo tpmShopInfo = tpmShopInfoService.findById(tpmFreightStepPrice.getShopId());
        if (tpmShopInfo == null) {
            return ResponseUtil.fail("店铺不存在");
        }
        tpmFreightStepPrice.setShopCode(tpmShopInfo.getShopCode());
        tpmFreightStepPrice.setShopName(tpmShopInfo.getShopName());
        freightStepPriceService.add(tpmFreightStepPrice);
        logger.info("【请求结束】运费阶梯价->添加,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }

    @PostMapping("/update")
    @CrossOrigin
    @RequiresPermissions("admin:freight:update")
    @RequiresPermissionsDesc(menu = {"配置管理", "运费阶梯价"}, button = "更新")
    public Object update(@RequestBody TpmFreightStepPrice tpmFreightStepPrice) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] 运费阶梯价->修改,请求参数:{}", JSONObject.toJSONString(tpmFreightStepPrice));
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        if (tpmFreightStepPrice.getShopId() == null) {
            return ResponseUtil.fail("请选择店铺");
        }
        if (tpmFreightStepPrice.getShopId() != null && !userShopIdList.contains(tpmFreightStepPrice.getShopId())) {
            return ResponseUtil.fail("当前用户没有此店铺权限");
        }
        TpmShopInfo tpmShopInfo = tpmShopInfoService.findById(tpmFreightStepPrice.getShopId());
        if (tpmShopInfo == null) {
            return ResponseUtil.fail("店铺不存在");
        }
        tpmFreightStepPrice.setShopCode(tpmShopInfo.getShopCode());
        tpmFreightStepPrice.setShopName(tpmShopInfo.getShopName());
        freightStepPriceService.updateById(tpmFreightStepPrice);
        logger.info("【请求结束】运费阶梯价->修改,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }

    /**
     * 服务端删除
     *
     * @param tpmFreightStepPrice
     * @return
     */
    @PostMapping("/delete")
    @CrossOrigin
    public Object delete(@RequestBody TpmFreightStepPrice tpmFreightStepPrice) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] 运费阶梯价->删除,请求参数:{}", JSONObject.toJSONString(tpmFreightStepPrice));
        int i = freightStepPriceService.deleteById(tpmFreightStepPrice);
        if (i < 1) {
            return ResponseUtil.fail();
        }
        return ResponseUtil.ok();
    }

}
