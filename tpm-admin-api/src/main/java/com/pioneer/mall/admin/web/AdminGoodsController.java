package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.dao.GoodsAllinone;
import com.pioneer.mall.admin.service.AdminDataAuthService;
import com.pioneer.mall.admin.service.AdminGoodsService;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/admin/goods")
@Validated
public class AdminGoodsController {
    private static final Logger logger = LoggerFactory.getLogger(AdminGoodsController.class);

    @Autowired
    private AdminGoodsService adminGoodsService;

    @Autowired
    private AdminDataAuthService adminDataAuthService;

    /**
     * 查询商品
     *
     * @param goodsSn
     * @param name
     * @param page
     * @param limit
     * @param sort
     * @param order
     * @return
     */
    @RequiresPermissions("admin:goods:list")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "查询")
    @GetMapping("/list")
    public Object list(String goodsSn, String name,
                       @RequestParam(defaultValue = "1") Integer businessType,
                       @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @RequestParam(required = false) Integer shopId,
                       @RequestParam(name = "categoryId", required = false) Integer categoryId,
                       @Sort(accepts = {"sort_order", "add_time", "id"}) @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->查询,请求参数:goodsSn:{},name:{},page:{},businessType={}", goodsSn, name, page, businessType);

        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        if (shopId != null && !userShopIdList.contains(shopId)) {
            return ResponseUtil.fail("当前用户没有此店铺权限");
        }
        //需要区分数据权限，如果属于品牌商管理员，则需要获取当前用户管理品牌店铺
        List<Integer> brandIds = null;
        if (adminDataAuthService.isBrandManager()) {
            brandIds = adminDataAuthService.getBrandIds();
            logger.info("运营商管理角色操作，需控制数据权限，brandIds:{}", JSONObject.toJSONString(brandIds));

            if (brandIds == null || brandIds.size() == 0) {
                Map<String, Object> data = new HashMap<>();
                data.put("total", 0L);
                data.put("items", null);

                logger.info("【请求结束】商品管理->商品管理->查询,响应结果:{}", JSONObject.toJSONString(data));
                return ResponseUtil.ok(data);
            }
        }

        return adminGoodsService.list(goodsSn, name, categoryId, businessType, page, limit, sort, order, brandIds, shopId);
    }

    @GetMapping("/catAndBrand")
    @RequiresPermissions("admin:goods:catAndBrand")
    public Object catAndBrand(@RequestParam(defaultValue = "1") Integer businessType) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->查询,请求参数:businessType={}", businessType);
        return adminGoodsService.catAndBrand(businessType);
    }

    /**
     * 编辑商品
     *
     * @param goodsAllinone
     * @return
     */
    @RequiresPermissions("admin:goods:update")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "编辑")
    @PostMapping("/update")
    public Object update(@RequestBody GoodsAllinone goodsAllinone) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->编辑,请求参数:{}", JSONObject.toJSONString(goodsAllinone));
        // 判断下shopid
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }

        // 如果商品业务类型为空，则默认为1
        if (Objects.isNull(goodsAllinone.getGoods().getBusinessType())) {
            goodsAllinone.getGoods().setBusinessType(1);
        }
        if (goodsAllinone.getGoods().getBusinessType() == null) {
            return ResponseUtil.fail("商品的业务类型为空");
        }
        return adminGoodsService.update(goodsAllinone);
    }

    /**
     * 删除商品
     *
     * @param goods
     * @return
     */
    @RequiresPermissions("admin:goods:delete")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "删除")
    @PostMapping("/delete")
    public Object delete(@RequestBody TpmGoods goods) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->删除,请求参数:{}", JSONObject.toJSONString(goods));

        return adminGoodsService.delete(goods);
    }

    /**
     * 添加商品
     *
     * @param goodsAllinone
     * @return
     */
    @RequiresPermissions("admin:goods:create")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "上架")
    @PostMapping("/create")
    public Object create(@RequestBody GoodsAllinone goodsAllinone) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->上架,请求参数:{}", JSONObject.toJSONString(goodsAllinone));
        // 如果商品业务类型为空，则默认为1
        if (Objects.isNull(goodsAllinone.getGoods().getBusinessType())) {
            goodsAllinone.getGoods().setBusinessType(1);
        }
        return adminGoodsService.create(goodsAllinone);
    }

    /**
     * 商品详情
     *
     * @param id
     * @return
     */
    @RequiresPermissions("admin:goods:detail")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "详情")
    @GetMapping("/detail")
    public Object detail(@NotNull Integer id) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->详情,请求参数,id:{}", id);

        return adminGoodsService.detail(id);
    }

    @RequiresPermissions("admin:goods:batchSaleOut")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "批量售罄")
    @PostMapping("/batchSaleOut")
    public Object batchSaleOut(@RequestBody Map<String, Object> body) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        List<Integer> goodsIdList = (List<Integer>) body.get("goodsIdList");
        Boolean isSaleOut = (Boolean) body.get("isSaleOut");
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return ResponseUtil.fail("数据未选中");
        }
        if (isSaleOut == null) {
            return ResponseUtil.fail("售罄状态未选中");
        }

        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->批量售罄,请求参数,goodsIdList:{} isSaleOut:{}", JSONObject.toJSONString(goodsIdList), isSaleOut);
        return adminGoodsService.batchSaleOut(goodsIdList, isSaleOut);
    }

    @RequiresPermissions("admin:goods:batchOnSale")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "批量上下架")
    @PostMapping("/batchOnSale")
    public Object batchOnSale(@RequestBody Map<String, Object> body) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        List<Integer> goodsIdList = (List<Integer>) body.get("goodsIdList");
        Boolean isOnSale = (Boolean) body.get("isOnSale");
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return ResponseUtil.fail("数据未选中");
        }
        if (isOnSale == null) {
            return ResponseUtil.fail("上下架状态未选中");
        }

        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->批量上下架,请求参数,goodsIdList:{} isOnSale:{}", JSONObject.toJSONString(goodsIdList), isOnSale);
        return adminGoodsService.batchOnSale(goodsIdList, isOnSale);
    }

    @RequiresPermissions("admin:goods:updateInventoryNum")
    @RequiresPermissionsDesc(menu = {"商品管理", "商品管理"}, button = "修改库存数量")
    @PostMapping("/updateInventoryNum")
    public Object updateInventoryNum(@RequestBody TpmGoods tpmGoods) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        Integer id = tpmGoods.getId();
        Integer inventoryNum = tpmGoods.getInventoryNum();
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商品管理->商品管理->修改库存数量,请求参数,id:{} inventoryNum:{}", id, inventoryNum);
        if (Objects.isNull(id)) {
            return ResponseUtil.fail("商品id不能为空");
        }
        if (inventoryNum < 0) {
            return ResponseUtil.fail("库存数量不能小于0");
        }
        return adminGoodsService.updateInventoryNum(id, inventoryNum);
    }


}
