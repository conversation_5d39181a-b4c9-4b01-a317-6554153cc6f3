package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain.TpmMembership;
import com.pioneer.mall.db.dto.TpmMembershipDto;
import com.pioneer.mall.db.service.TpmMembershipService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin/membership")
public class AdminMembershipController {
    private static final Logger logger = LoggerFactory.getLogger(AdminMembershipController.class);

    @Autowired
    private TpmMembershipService tpmMembershipService;

    @Autowired
    private HttpServletRequest request;

    @GetMapping("ui/list")
    @CrossOrigin
    public Object list(@RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @RequestParam(required = false) String name,
                       @Sort(accepts = { "level","add_time", "id" }) @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {

        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->查询,请求参数:name:{}", name);

        TpmMembership tpmMembership = new TpmMembership();
        tpmMembership.setName(name);
        List<TpmMembership> tpmVipList = tpmMembershipService.querySelective(tpmMembership, page, limit, sort, order);
        long total = PageInfo.of(tpmVipList).getTotal();
        // 图片为空显示空
        tpmVipList = tpmVipList.stream().map(z -> {
            if (StringUtils.isEmpty(z.getPicUrl())) {
                z.setPicUrl("");
            }
            return z;
        }).collect(Collectors.toList());
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", tpmVipList);
        logger.info("【请求结束】VIP管理->查询:响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @GetMapping("ui/detail")
    @CrossOrigin
    public Object detail(@RequestBody TpmMembership tpmMembership) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->查询,请求参数:page:{}", JSONObject.toJSONString(tpmMembership));

        TpmMembership tpmMember = tpmMembershipService.findById(tpmMembership.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("vip", tpmMember);
        logger.info("【请求结束】 VIP管理->查询,响应结果:{}", JSONObject.toJSONString(result));
        return ResponseUtil.ok(result);
    }

    @PostMapping("ui/create")
    @CrossOrigin
    public Object create(@RequestBody TpmMembershipDto tpmMembershipDto) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->添加,请求参数:{}", JSONObject.toJSONString(tpmMembershipDto));
        try {
            tpmMembershipService.add(tpmMembershipDto);
        } catch (Exception e) {
            return ResponseUtil.fail(-1, e.getMessage());
        }
        logger.info("【请求结束】VIP管理->添加,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }

    @PostMapping("ui/update")
    @CrossOrigin
    public Object updated(@RequestBody TpmMembershipDto tpmMembershipDto) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->修改,请求参数:{}", JSONObject.toJSONString(tpmMembershipDto));
        if (tpmMembershipDto.getId() == null) {
            return ResponseUtil.fail("数据ID不能为空");
        }
        tpmMembershipService.updateById(tpmMembershipDto);
        logger.info("【请求结束】VIP管理->修改,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }

    @PostMapping("ui/delete")
    @CrossOrigin
    public Object delete(@RequestBody TpmMembershipDto tpmMembershipDto) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->删除,请求参数:{}", JSONObject.toJSONString(tpmMembershipDto));
        int i = tpmMembershipService.deleteById(tpmMembershipDto);
        if (i < 1) {
            return ResponseUtil.fail("没有匹配的数据");
        }
        return ResponseUtil.ok();
    }


    @PostMapping("/list")
    public Object list() {
        List<TpmMembershipDto> list = tpmMembershipService.selectAll();
        return ResponseUtil.ok(list);
    }

    @PostMapping("/add")
    public Object add(@RequestBody TpmMembershipDto tpmMembershipDto) {
        try {
            tpmMembershipService.add(tpmMembershipDto);
            return ResponseUtil.ok();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResponseUtil.fail(-1, e.getMessage());
        }
    }

    @PostMapping("/update")
    public Object update(@RequestBody TpmMembershipDto tpmMembershipDto) {
        if (Objects.isNull(tpmMembershipDto) || Objects.isNull(tpmMembershipDto.getId())) {
            return ResponseUtil.badArgument();
        }
        try {
            tpmMembershipService.update(tpmMembershipDto);
            return ResponseUtil.ok();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResponseUtil.fail(-1, e.getMessage());
        }
    }


    @PostMapping("/delete")
    @CrossOrigin
    public Object deleted(@RequestBody TpmMembershipDto tpmMembershipDto) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->删除,请求参数:{}", JSONObject.toJSONString(tpmMembershipDto));
        try {
            int i = tpmMembershipService.deleteById(tpmMembershipDto);
            if (i < 1) {
                return ResponseUtil.fail();
            }
            return ResponseUtil.ok();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResponseUtil.fail(-1, e.getMessage());
        }
    }

    @GetMapping("/enable")
    @CrossOrigin
    public Object enable(@RequestBody TpmMembershipDto tpmMembershipDto) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->启用,请求参数:{}", JSONObject.toJSONString(tpmMembershipDto));
        tpmMembershipService.updateById(tpmMembershipDto);
        logger.info("【请求结束】VIP管理->启用,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }
}
