package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSON;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.dao.OrderPrintReqVo;
import com.pioneer.mall.admin.dao.OrderShipSnAddReqVo;
import com.pioneer.mall.admin.service.AdminOrderService;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.admin.vo.req.OrderUpdateStatusReqVo;
import com.pioneer.mall.core.judanke.JdkOrderService;
import com.pioneer.mall.core.spPrint.PrintService;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/admin/order")
@Validated
public class AdminOrderController {
    private static final Logger logger = LoggerFactory.getLogger(AdminOrderController.class);

    @Autowired
    private AdminOrderService adminOrderService;
    @Autowired
    private JdkOrderService jdkOrderService;
    @Autowired
    private PrintService printService;

    /**
     * 查询订单
     *
     * @param userId
     * @param orderSn
     * @param orderStatusArray
     * @param page
     * @param limit
     * @param sort
     * @param order
     * @return
     */
    @RequiresPermissions("admin:order:list")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "查询")
    @GetMapping("/list")
    public Object list(Integer userId, String orderSn, @RequestParam(required = false) List<Short> orderStatusArray,
                       @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer limit,
                       @RequestParam(defaultValue = "1") Integer businessType,
                       @RequestParam(required = false) Integer freightType,
                       @RequestParam(required = false) Integer shopId,
                       @Sort @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->查询,请求参数:userId:{},orderSn:{},page:{},businessType:{}", userId, orderSn, page, businessType);
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        if (shopId != null && !userShopIdList.contains(shopId)) {
            return ResponseUtil.fail("当前用户没有此店铺权限");
        }
        return adminOrderService.list(userId, orderSn, orderStatusArray, page, limit, sort, order, businessType,freightType, shopId);
    }

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    @RequiresPermissions("admin:order:read")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "详情")
    @GetMapping("/detail")
    public Object detail(@NotNull Integer id) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->详情,请求参数:id:{}", id);

        return adminOrderService.detail(id);
    }

    /**
     * 订单退款
     *
     * @param body 订单信息，{ orderId：xxx }
     * @return 订单退款操作结果
     */
    @RequiresPermissions("admin:order:refund")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "订单退款")
    @PostMapping("/refund")
    public Object refund(@RequestBody String body) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->订单退款,请求参数,body:{}", body);

        return adminOrderService.refund(body);
    }


    @PostMapping("/wx/refundNotify")
    public String wxRefundNotify(@RequestBody String body, HttpServletRequest request) {
        logger.info("微信退款通知回调 body={}", body);
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setTimeStamp(request.getHeader("Wechatpay-Timestamp"));
        signatureHeader.setNonce(request.getHeader("Wechatpay-Nonce"));
        signatureHeader.setSerial(request.getHeader("Wechatpay-Serial"));
        signatureHeader.setSignature(request.getHeader("Wechatpay-Signature"));
        logger.info("signatureHeader={}",JSON.toJSONString(signatureHeader));
        String result = adminOrderService.wxRefundNotify(body, signatureHeader);
        logger.info("微信退款通知回调 result={}", result);
        return result;
    }

    /**
     * 发货
     *
     * @param body 订单信息，{ orderId：xxx, shipSn: xxx, shipChannel: xxx }
     * @return 订单操作结果
     */
    @RequiresPermissions("admin:order:ship")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "订单发货")
    @PostMapping("/ship")
    public Object ship(@RequestBody String body) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->订单发货,请求参数,body:{}", body);

        try {
            return adminOrderService.ship(body);
        } catch (Exception ex) {
            return ResponseUtil.fail(ex.getMessage());
        }
    }

    @RequiresPermissions("admin:order:mall:print")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "订单打印")
    @PostMapping("/mallPrint")
    public Object mallPrint(@RequestBody OrderPrintReqVo printReqVo) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->订单打印,请求参数,body:{}", JSON.toJSONString(printReqVo));

        try {
            return printService.mallPrint(printReqVo.getOrderIds(), printReqVo.getPrintTypes());
        } catch (Exception ex) {
            logger.error("订单打印失败，exception={}", ex);
            return ResponseUtil.fail(ex.getMessage());
        }
    }

    @RequiresPermissions("admin:order:delivery:add")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "运单号增加")
    @PostMapping("/shipSn/add")
    public Object shipSnAdd(@RequestBody OrderShipSnAddReqVo printReqVo) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->订单打印,请求参数,body:{}", JSON.toJSONString(printReqVo));

        try {
            return adminOrderService.shipSnAdd(printReqVo.getOrderId(), printReqVo.getShipSn());
        } catch (Exception ex) {
            logger.error("订单打印失败，exception={}", ex);
            return ResponseUtil.fail(ex.getMessage());
        }
    }

    /**
     * 回复订单商品
     *
     * @param body 订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    @RequiresPermissions("admin:order:reply")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "订单商品回复")
    @PostMapping("/reply")
    public Object reply(@RequestBody String body) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->订单商品回复,请求参数,body:{}", body);

        return adminOrderService.reply(body);
    }

    /**
     * 回复订单商品
     *
     * @param body 订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    @RequiresPermissions("admin:order:listShip")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "快递加载")
    @GetMapping("/listShipChannel")
    public Object listShipChannel() {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->快递信息加载");

        return adminOrderService.listShipChannel();
    }

    @RequiresPermissions("admin:order:manualUpdateStatus")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "手动修改状态")
    @PostMapping("/manualUpdateStatus")
    public Object manualUpdateStatus(@RequestBody OrderUpdateStatusReqVo req) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 订单管理->手动修改状态");
        if (Objects.isNull(req.getOrderId())) {
            return ResponseUtil.fail("订单id不能为空");
        }
        if (StringUtils.isEmpty(req.getOrderStatus())) {
            return ResponseUtil.fail("订单状态不能为空");
        }

        return adminOrderService.updateStatus(req);
    }

    @RequiresPermissions("admin:order:prepare")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "出餐")
    @PostMapping("/prepare")
    public Object prepare(@RequestBody Map<String, Object> body) {

        Object orderId = body.get("orderId");
        if (orderId == null) {
            return ResponseUtil.fail("订单id不能为空");
        }
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->出餐,orderId=" + orderId);
        return adminOrderService.prepare((Integer) orderId);
    }

    @RequiresPermissions("admin:order:print")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "打印")
    @PostMapping("/print")
    public Object print(@RequestBody Map<String, Object> body) {
        Object orderId = body.get("orderId");
        if (orderId == null) {
            return ResponseUtil.fail("订单id不能为空");
        }
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->出餐,orderId=" + orderId);
        return adminOrderService.print((Integer) orderId);
    }

    @RequiresPermissions("admin:order:delivery")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "配送")
    @PostMapping("/delivery")
    public Object delivery(Integer orderId) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->配送");
        return adminOrderService.delivery(orderId);
    }

    @RequiresPermissions("admin:order:finish")
    @RequiresPermissionsDesc(menu = {"商场管理", "订单管理"}, button = "已送达/已取餐")
    @PostMapping("/finish")
    public Object finish(Integer orderId) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 商场管理->订单管理->已送达/已取餐");
        return adminOrderService.finish(orderId);
    }

    @RequiresPermissions("admin:order:verifyQRCode")
    @PostMapping("/verifyQRCode")
    public Object verifyQRCode(String qrCode) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 核销取餐码 二维码uuid:{}", qrCode);
        if (StringUtils.isEmpty(qrCode)) {
            return ResponseUtil.fail("二维码不能为空");
        }
        return adminOrderService.verifyQRCode(qrCode);
    }
}
