package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.service.AdminPointConfigService;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.admin.vo.req.TpmPointExchangeConfigReqVo;
import com.pioneer.mall.admin.vo.res.TpmPointExchangeConfigResVo;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class AdminPointConfigController {

    @Resource
    private AdminPointConfigService adminPointConfigService;

    private static final Logger logger = LoggerFactory.getLogger(AdminPointConfigController.class);

    @GetMapping("/admin/point/config/list")
    public Object pointConfigList(@RequestParam(defaultValue = "1") Integer page,
                                  @RequestParam(defaultValue = "10") Integer limit,
                                  @RequestParam(required = false) String name,
                                  @Sort @RequestParam(defaultValue = "create_time") String sort,
                                  @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 积分兑换管理");
        List<TpmPointExchangeConfigResVo> exchangeConfigResVoList = adminPointConfigService.pointConfigList(page, limit, name, sort, order);
        long total = PageInfo.of(exchangeConfigResVoList).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", exchangeConfigResVoList);
        logger.info("【请求结束】积分兑换管理:响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @PostMapping("/admin/point/config/create")
    public Object pointConfigAdd(@RequestBody TpmPointExchangeConfigReqVo data) {

        logger.error("操作人:[" + AuthSupport.userName() + "] 积分兑换配置管理:{}", JSONObject.toJSONString(data));
        if (data.getName() == null) {
            return ResponseUtil.fail("名称不能为空");
        }
        if (data.getCouponId() == null) {
            return ResponseUtil.fail("券id不能为空");
        }
        if (data.getAmount() == null) {
            return ResponseUtil.fail("兑换积分不能为空");
        }
        if (data.getDescription() == null) {
            return ResponseUtil.fail("兑换说明不能为空");
        }
        data.setCreateTime(LocalDateTime.now());
        try {
            adminPointConfigService.pointConfigAdd(data);
        } catch (Exception ex) {
            logger.error("【请求异常】操作人:[" + AuthSupport.userName() + "] 积分兑换配置管理:{}", ex.getMessage(), ex);
            return ResponseUtil.fail("操作异常：" + ex.getMessage());
        }
        return ResponseUtil.ok(data);
    }

    @PostMapping("/admin/point/config/update")
    public Object pointConfigUpdate(@RequestBody TpmPointExchangeConfigReqVo data) {

        if (data.getId() == null) {
            return ResponseUtil.fail("数据ID为空");
        }
        if (data.getName() == null) {
            return ResponseUtil.fail("名称不能为空");
        }
        if (data.getDescription() == null) {
            return ResponseUtil.fail("兑换说明不能为空");
        }
        if (data.getCouponId() == null) {
            return ResponseUtil.fail("优惠券不能为空");
        }
        if (data.getEnable() == null) {
            return ResponseUtil.fail("启用标识不能为空");
        }
        adminPointConfigService.updateByPrimaryKeySelective(data);
        return ResponseUtil.ok(data);
    }

    @PostMapping("/admin/point/config/delete")
    public Object pointConfigDelete(@RequestBody TpmPointExchangeConfigReqVo data) {

        if (data.getId() == null) {
            return ResponseUtil.badArgument();
        }
        adminPointConfigService.deleteById(data.getId());
        return ResponseUtil.ok();
    }
}
