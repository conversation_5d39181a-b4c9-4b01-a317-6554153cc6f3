package com.pioneer.mall.admin.web;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.enums.Constants;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.admin.vo.req.PointAddReq;
import com.pioneer.mall.admin.vo.res.TpmPointFlowResVo;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.dto.request.PointPagingReq;
import com.pioneer.mall.db.service.TpmPointService;
import com.pioneer.mall.db.service.TpmUserService;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 积分管理controller
 * <p>
 * 管理端-积分相关接口
 */
@RestController
@RequestMapping(value = "/admin/point")
public class AdminPointController {

    private static final Logger logger = LoggerFactory.getLogger(AdminPointController.class);


    /**
     * 配置服务接口
     * */
//    private SettingService settingService;

    /**
     * 积分服务接口
     */
    @Autowired
    private TpmPointService pointService;

    /**
     * 会员服务接口
     */
    @Autowired
    private TpmUserService userService;

    @Autowired
    private Validator validator;

    /**
     * 积分明细列表查询
     *
     * @param
     * @return 积分明细列表
     */
    @RequiresPermissions("admin:point:list")
    @RequiresPermissionsDesc(menu = {"推广管理", "积分流水"}, button = "查询")
    @GetMapping("/list")
    public Object list(@RequestParam(required = false) Integer userId,
                       @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @Sort @RequestParam(defaultValue = "create_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 积分流水->查询,请求参数:name:{},page:{}", userId, page);

        PointPagingReq pointPagingReq = new PointPagingReq();
        if (userId != null) {
            pointPagingReq.setUserId(Collections.singletonList(userId));
        }
        pointPagingReq.setPageSize(limit);
        pointPagingReq.setPageNum(page);

        List<TpmPointDto> paginationResponse = pointService.paging(pointPagingReq, sort, order);
        long total = PageInfo.of(paginationResponse).getTotal();

        List<TpmPointFlowResVo> pointFlowResVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(paginationResponse)) {
            pointFlowResVoList = paginationResponse.stream()
                    .map(z -> {
                        TpmPointFlowResVo tpmPointFlowResVo = new TpmPointFlowResVo();
                        BeanUtils.copyProperties(z, tpmPointFlowResVo);
                        TpmUser tpmUser = tpmPointFlowResVo.getUserInfo();
                        tpmPointFlowResVo.setUserName(tpmUser == null ? "" : tpmUser.getUsername());
                        return tpmPointFlowResVo;
                    }).collect(Collectors.toList());
        }
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", pointFlowResVoList);
        logger.info("【请求结束】积分流水->查询:响应结果:{}", JSON.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    /**
     * 积分明细列表查询
     *
     * @param request HttpServletRequest对象
     * @return 积分明细列表
     */
    @PostMapping(value = "/list")
    @CrossOrigin
    @RequiresPermissions("admin:point:list")
    public Object list(HttpServletRequest request) {
        String token = request.getHeader("Access-Token");
        Integer page = request.getParameter("page") == null ? Constants.PAGE_NUMBER : Integer.parseInt(request.getParameter("page"));
        Integer pageSize = request.getParameter("pageSize") == null ? Constants.PAGE_SIZE : Integer.parseInt(request.getParameter("pageSize"));
        String mobile = request.getParameter("mobile") == null ? "" : request.getParameter("mobile");
//        String userId = request.getParameter("userId") == null ? "" : request.getParameter("userId");
        String userNo = request.getParameter("userNo") == null ? "" : request.getParameter("userNo");

//        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
//        if (accountInfo == null) {
//            return getFailureResult(1001, "请先登录");
//        }

        PointPagingReq pointPagingReq = new PointPagingReq();
        pointPagingReq.setPageNum(page);
        pointPagingReq.setPageSize(pageSize);

        if (StringUtil.isNotEmpty(mobile)) {
            List<TpmUser> tpmUsers = userService.queryByMobile(mobile);
            if (CollectionUtils.isNotEmpty(tpmUsers)) {
                pointPagingReq.setUserId(tpmUsers.stream()
                        .map(TpmUser::getId).collect(Collectors.toList()));
                pointPagingReq.setUserName(tpmUsers.stream()
                        .map(TpmUser::getNickname)
                        .collect(Collectors.joining(",")));
            }
        }
        pointPagingReq.setPageNum(page);
        pointPagingReq.setPageSize(pageSize);

        List<TpmPointDto> paginationResponse = pointService.paging(pointPagingReq);

        Map<String, Object> result = new HashMap<>();
        result.put("data", paginationResponse);

        return ResponseUtil.ok(result);
    }

    @PostMapping(value = "/manual/add")
    public Object add(HttpServletRequest request, @RequestBody PointAddReq req) {
        if (Objects.isNull(req)) {
            return ResponseUtil.badArgument();
        }
        Set<ConstraintViolation<PointAddReq>> validate = validator.validate(req);
        if (!validate.isEmpty()) {
            String errorMsg = validate.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","));
            return ResponseUtil.fail(401, errorMsg);
        }
        TpmPointDto pointDto = new TpmPointDto();
        pointDto.setDescription(req.getDescription());
        pointDto.setUserId(req.getUserId());
        pointDto.setAmount(req.getAmount());
        pointDto.setOperator(req.getOperator());
        pointService.addPoint(pointDto);
        return ResponseUtil.ok();
    }

}
