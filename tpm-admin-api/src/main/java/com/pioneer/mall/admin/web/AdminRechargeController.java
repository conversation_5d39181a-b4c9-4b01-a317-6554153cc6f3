package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.bean.search.TpmRechargeConfigSearch;
import com.pioneer.mall.db.domain.TpmRechargeConfig;
import com.pioneer.mall.db.service.TpmRechargeConfigService;
import com.pioneer.mall.db.service.TpmRechargeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 充值管理
 * @date 2024/3/6 21:48
 */
@RestController
@RequestMapping("/admin/recharge")
@Validated
public class AdminRechargeController {

    private static final Logger logger = LoggerFactory.getLogger(AdminOrderController.class);


    @Autowired
    private TpmRechargeService tpmRechargeService;

    @Autowired
    private TpmRechargeConfigService tpmRechargeConfigService;


    @GetMapping("/list")
    public Object list(@RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @RequestParam(required = false) String name,
                       @Sort @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 充值管理->查询,请求参数:{}", "");
        TpmRechargeConfigSearch tpmRechargeConfigSearch = new TpmRechargeConfigSearch();
        if (name != null) {
            tpmRechargeConfigSearch.setTitle(name);
        }
        tpmRechargeConfigSearch.setSort(sort);
        tpmRechargeConfigSearch.setOrder(order);
        tpmRechargeConfigSearch.setPageSize(limit);
        tpmRechargeConfigSearch.setCurrentPage(page);
        List<TpmRechargeConfig> list = tpmRechargeConfigService.querySelective(tpmRechargeConfigSearch);
        long total = PageInfo.of(list).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", list);
        logger.info("【请求结束】充值管理->查询:响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

//    /**
//     * 获取充值配置
//     *
//     * @param tpmRechargeConfigSearch
//     * @return
//     */
//    @RequestMapping("/listConfig")
//    @Deprecated
//    public Object listConfig(@RequestBody TpmRechargeConfigSearch tpmRechargeConfigSearch) {
//        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 充值管理->查询,请求参数:{}", "");
//        List<TpmRechargeConfig> list = tpmRechargeConfigService.querySelective(tpmRechargeConfigSearch);
//        return ResponseUtil.ok(list);
//    }

    /**
     * 添加充值配置
     *
     * @param tpmRechargeConfig
     * @return
     */
    @PostMapping("/create")
    public Object addConfig(@RequestBody TpmRechargeConfig tpmRechargeConfig) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 充值管理->添加,请求参数:{}", JSON.toJSONString(tpmRechargeConfig));
        try {
            tpmRechargeConfigService.addConfig(tpmRechargeConfig);
            return ResponseUtil.ok("添加成功");
        } catch (Exception e) {
            logger.error("【请求结束】充值管理->添加,异常:{}", e.getMessage(), e);
            return ResponseUtil.fail(500, "添加失败:" + e.getMessage());
        }
    }

    /**
     * 修改充值配置
     */
    @PostMapping("/update")
    public Object updateConfig(@RequestBody TpmRechargeConfig tpmRechargeConfig) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 充值管理->修改,请求参数:{}", JSON.toJSONString(tpmRechargeConfig));
        try {
            tpmRechargeConfigService.updateConfig(tpmRechargeConfig);
            return ResponseUtil.ok("修改成功");
        } catch (Exception e) {
            return ResponseUtil.fail(500, "修改失败");
        }
    }

    /**
     * 删除充值配置
     *
     * @param id
     * @return
     */
    @PostMapping("/delete")
    public Object deleteConfig(@RequestBody TpmRechargeConfig tpmRechargeConfig) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 充值管理->删除,请求参数:{}", JSON.toJSONString(tpmRechargeConfig));
        try {
            tpmRechargeConfigService.deleteConfig(tpmRechargeConfig);
            return ResponseUtil.ok("删除成功");
        } catch (Exception e) {
            return ResponseUtil.fail(500, "删除失败");
        }
    }

//    /**
//     * 充值记录分页列表
//     *
//     * @param tpmRechargeSearch
//     * @return
//     */
//    @RequestMapping("/list")
//    public Object list(@RequestBody TpmRechargeSearch tpmRechargeSearch) {
//        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 充值管理->查询,请求参数:{}", JSON.toJSONString(tpmRechargeSearch));
//        List<TpmRecharge> list = tpmRechargeService.querySelective(tpmRechargeSearch);
//        return ResponseUtil.ok(list);
//    }

}
