package com.pioneer.mall.admin.web;

import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.db.bean.request.TpmShopHourSettingListReqVO;
import com.pioneer.mall.db.bean.request.TpmShopHourSettingReqVO;
import com.pioneer.mall.db.service.TpmShopHoursService;
import com.pioneer.mall.db.service.TpmShopInfoService;
import com.pioneer.mall.db.util.ThreadContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/23 20:59
 */
@RestController
@RequestMapping("/admin/shop")
@Validated
@Slf4j
public class AdminShopController {
    @Autowired
    private TpmShopInfoService tpmShopInfoService;
    @Autowired
    private TpmShopHoursService tpmShopHoursService;

    /**
     * 获取当前用户拥有的权限的店铺列表信息
     *
     * @return
     */
    @RequiresPermissions("admin:shop:userShopList")
    @GetMapping("/user/shop/list")
    public Object userShopList() {

        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }

        return ResponseUtil.ok(tpmShopInfoService.selectById(userShopIdList));
    }

    @RequiresPermissions("admin:shop:getShopSetting")
    @GetMapping("/getShopSetting")
    public Object getShopSetting(@RequestParam("shopId") Integer shopId) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        return tpmShopHoursService.getShopSetting(shopId);
    }

    @RequiresPermissions("admin:shop:setShopStatus")
    @RequestMapping("/setShopStatus")
    public Object setShopStatus(@RequestBody TpmShopHourSettingReqVO reqVO) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        return tpmShopHoursService.setShopStatus(reqVO);
    }

    @RequiresPermissions("admin:shop:setShopHourSetting")
    @RequestMapping("/setShopHourSetting")
    public Object setShopHourSetting(@RequestBody TpmShopHourSettingListReqVO reqVo) {
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return ResponseUtil.fail("当前用户没有店铺权限");
        }
        return tpmShopHoursService.saveShopHours(reqVo.getReqVOList());
    }
}
