package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.bean.search.TpmVipSearch;
import com.pioneer.mall.db.domain.TpmVip;
import com.pioneer.mall.db.service.TpmVipService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/24 11:14
 */
@RestController
@RequestMapping("/admin/vip")
@Validated
public class AdminVipController {

    private static final Logger logger = LoggerFactory.getLogger(AdminAdController.class);


    @Autowired
    private TpmVipService tpmVipService;

    @GetMapping("/list")
    @CrossOrigin
    public Object list(@RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @RequestParam(required = false) String name,
                       @Sort @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->查询,请求参数:name:{}", name);
        TpmVipSearch tpmVipSearch = new TpmVipSearch();
        if (name != null) {
            tpmVipSearch.setVipName(name);
        }
        tpmVipSearch.setSort(sort);
        tpmVipSearch.setOrder(order);
        tpmVipSearch.setCurrentPage(page);
        tpmVipSearch.setPageSize(limit);
        List<TpmVip> tpmVipList = tpmVipService.querySelective(tpmVipSearch);
        long total = PageInfo.of(tpmVipList).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", tpmVipList);
        logger.info("【请求结束】VIP管理->查询:响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @GetMapping("/detail")
    @CrossOrigin
    public Object detail(@RequestBody TpmVipSearch tpmVipSearch) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->查询,请求参数:page:{}", JSONObject.toJSONString(tpmVipSearch));

        TpmVip tpmVip = tpmVipService.findById(tpmVipSearch.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("vip", tpmVip);
        logger.info("【请求结束】 VIP管理->查询,响应结果:{}", JSONObject.toJSONString(result));
        return ResponseUtil.ok(result);
    }

    @PostMapping("/create")
    @CrossOrigin
    public Object add(@RequestBody TpmVip tpmVip) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->添加,请求参数:{}", JSONObject.toJSONString(tpmVip));
        tpmVipService.add(tpmVip);
        logger.info("【请求结束】VIP管理->添加,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }

    @PostMapping("/update")
    @CrossOrigin
    public Object update(@RequestBody TpmVip tpmVip) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->修改,请求参数:{}", JSONObject.toJSONString(tpmVip));
        tpmVipService.updateById(tpmVip);
        logger.info("【请求结束】VIP管理->修改,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }

    /**
     * 服务端删除
     *
     * @param tpmVip
     * @return
     */
    @PostMapping("/delete")
    @CrossOrigin
    public Object delete(@RequestBody TpmVip tpmVip) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->删除,请求参数:{}", JSONObject.toJSONString(tpmVip));
        int i = tpmVipService.deleteById(tpmVip);
        if (i < 1) {
            return ResponseUtil.fail();
        }
        return ResponseUtil.ok();
    }

    @PostMapping("/enable")
    @CrossOrigin
    public Object enable(@RequestBody TpmVip tpmVip) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 操作人:[" + AuthSupport.userName() + "] VIP管理->启用,请求参数:{}", JSONObject.toJSONString(tpmVip));
        tpmVipService.updateById(tpmVip);
        logger.info("【请求结束】VIP管理->启用,响应结果:{}", JSONObject.toJSONString(ResponseUtil.ok()));
        return ResponseUtil.ok();
    }
}
