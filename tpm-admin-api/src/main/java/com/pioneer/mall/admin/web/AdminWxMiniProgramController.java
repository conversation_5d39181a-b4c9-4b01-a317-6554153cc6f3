package com.pioneer.mall.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.annotation.RequiresPermissionsDesc;
import com.pioneer.mall.admin.util.AuthSupport;
import com.pioneer.mall.core.storage.StorageService;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain .TpmWxPageConfig;
import com.pioneer.mall.db.service.TpmWxPageConfigService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 微信小程序相关配置controller
 * @date 2024/4/29 16:17
 */
@RequestMapping("/admin/static/page")
@RestController
public class AdminWxMiniProgramController {

    private static final Logger logger = LoggerFactory.getLogger(AdminStorageController.class);

    @Autowired
    private StorageService storageService;
    @Autowired
    private TpmWxPageConfigService tpmWxPageConfigService;

    @PostMapping("/create")
    public Object create(@RequestBody TpmWxPageConfig tpmWxPageConfig) throws IOException {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 微信页面配置上传,请求参数,file:{}", JSONObject.toJSONString(tpmWxPageConfig));

        if (tpmWxPageConfig.getMenu() == null) {
            return ResponseUtil.fail("归属页面不能为空");
        }
        if (tpmWxPageConfig.getUrl() == null) {
            return ResponseUtil.fail("图片地址不能为空");
        }
        tpmWxPageConfigService.add(tpmWxPageConfig);
        logger.info("【请求结束】微信页面配置上传->成功");
        return ResponseUtil.ok();
    }

    @RequiresPermissions("admin:storage:list")
    @RequiresPermissionsDesc(menu = {"微信小程序配置管理", "微信小程序配置管理"}, button = "查询")
    @GetMapping("/list")
    public Object list(@RequestParam(required = false) String menu,
                       @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @Sort @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 系统管理->微信小程序配置管理->查询,请求参数,menu:{},page:{}", menu, page);

        List<TpmWxPageConfig> wxPageConfigList = tpmWxPageConfigService.querySelective(menu, null, null, page, limit, sort, order);
        long total = PageInfo.of(wxPageConfigList).getTotal();
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", wxPageConfigList);

        logger.info("【请求结束】系统管理->微信小程序配置管理->查询:响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    @RequiresPermissions("admin:storage:read")
    @RequiresPermissionsDesc(menu = {"微信小程序配置管理", "微信小程序配置管理"}, button = "详情")
    @PostMapping("/read")
    public Object read(@NotNull Integer id) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 系统管理->微信小程序配置管理->详情,请求参数,id:{}", id);

        TpmWxPageConfig wxPageConfig = tpmWxPageConfigService.findById(id);
        if (wxPageConfig == null) {
            return ResponseUtil.badArgumentValue();
        }

        logger.info("【请求结束】系统管理->微信小程序配置管理->详情:响应结果:{}", JSONObject.toJSONString(wxPageConfig));
        return ResponseUtil.ok(wxPageConfig);
    }

    @RequiresPermissions("admin:storage:update")
    @RequiresPermissionsDesc(menu = {"微信小程序配置管理", "微信小程序配置管理"}, button = "编辑")
    @PostMapping("/update")
    public Object update(@RequestBody TpmWxPageConfig tpmWxPageConfig) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 系统管理->微信小程序配置管理->编辑,请求参数:{}", JSONObject.toJSONString(tpmWxPageConfig));

        if (tpmWxPageConfig.getId() == null) {
            return ResponseUtil.fail("主键ID不能为空");
        }
        if (tpmWxPageConfigService.update(tpmWxPageConfig) == 0) {
            logger.error("系统管理->微信小程序配置管理->编辑 错误:{}", "更新数据失败!");
            return ResponseUtil.updatedDataFailed();
        }

        logger.info("【请求结束】系统管理->微信小程序配置管理->编辑:响应结果:{}", JSONObject.toJSONString(tpmWxPageConfig));
        return ResponseUtil.ok(tpmWxPageConfig);
    }

    @RequiresPermissions("admin:storage:delete")
    @RequiresPermissionsDesc(menu = {"微信小程序配置管理", "微信小程序配置管理"}, button = "删除")
    @PostMapping("/delete")
    public Object delete(@RequestBody TpmWxPageConfig tpmWxPageConfig) {
        logger.info("【请求开始】操作人:[" + AuthSupport.userName() + "] 系统管理->微信小程序配置管理->删除,请求参数:{}", JSONObject.toJSONString(tpmWxPageConfig));

//        String key = tpmWxPageConfig.getKey();
//        if (StringUtils.isEmpty(key)) {
//            return ResponseUtil.badArgument();
//        }

        Integer id = tpmWxPageConfig.getId();
        if (id == null) {
            return ResponseUtil.fail("主键id不能为空");
        }

        tpmWxPageConfigService.deleteByPrimaryKey(id);
//        storageService.delete(key);
        logger.info("【请求结束】系统管理->微信小程序配置管理->删除:响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }
}
