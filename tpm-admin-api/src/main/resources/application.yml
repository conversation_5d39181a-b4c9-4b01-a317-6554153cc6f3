spring:
  profiles:
    active: @profile.active@
  messages:
    encoding: UTF-8
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 80MB
      location: /tmp/tomcat_upload


server:
  port: 8083
  servlet:
    context-path: /demo
  cors:
    allowed-origins: "*" # 允许所有来源的请求，可以根据实际情况设置特定的来源，如：https://example.com
    allowed-methods: "*" # 允许所有 HTTP 方法，如：GET,POST,PUT,DELETE 等
    allowed-headers: "*" # 允许所有请求头
    exposed-headers: "" # 需要暴露给客户端的响应头列表，默认为空
    allow-credentials: false # 是否允许携带凭证（cookies、HTTP 认证等），默认为 false

logging:
  level:
    root:  info
    org.springframework:  ERROR
    org.mybatis:  debug
    com.pioneer.mall.admin:  DEBUG