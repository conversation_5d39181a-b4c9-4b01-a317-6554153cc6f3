package com.pioneer.mall.core.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.beans.Transient;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/12/3 16:02
 */
@Data
public class EveningDiscountConfigDTO {
    /**
     * 是否启用
     */
    private Boolean enable;
    /**
     * 折扣 eg. 0.8
     */
    private BigDecimal discount;
    /**
     * 开始时间 没有则报错
     */
    private LocalTime startTime;
    /**
     * 结束时间 没有或者为null则取店铺营业结束时间
     */
    private LocalTime endTime;
    /**
     * 不参与分类 L2级别
     */
    private List<Integer> excludeCategory;

    @JSONField(serialize = false)
    private Boolean effective;
}
