package com.pioneer.mall.core.config;

import com.pioneer.mall.core.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.Set;

@ControllerAdvice
@Slf4j
@Order(value = Ordered.LOWEST_PRECEDENCE)
public class GlobalExceptionHandler {

	@ExceptionHandler(IllegalArgumentException.class)
	@ResponseBody
	public Object badArgumentHandler(IllegalArgumentException e) {
		log.error("Assert异常:-------------->{}", e.getMessage(), e);
		e.printStackTrace();
		return ResponseUtil.badArgumentValue();
	}

	@ExceptionHandler(MethodArgumentTypeMismatchException.class)
	@ResponseBody
	public Object badArgumentHandler(MethodArgumentTypeMismatchException e) {
		log.error("Assert异常:-------------->{}", e.getMessage(), e);
		e.printStackTrace();
		return ResponseUtil.badArgumentValue();
	}

	@ExceptionHandler(MissingServletRequestParameterException.class)
	@ResponseBody
	public Object badArgumentHandler(MissingServletRequestParameterException e) {
		log.error("Assert异常:-------------->{}", e.getMessage(), e);
		e.printStackTrace();
		return ResponseUtil.badArgumentValue();
	}

	@ExceptionHandler(HttpMessageNotReadableException.class)
	@ResponseBody
	public Object badArgumentHandler(HttpMessageNotReadableException e) {
		log.error("Assert异常:-------------->{}", e.getMessage(), e);
		e.printStackTrace();
		return ResponseUtil.badArgumentValue();
	}

	@ExceptionHandler(ValidationException.class)
	@ResponseBody
	public Object badArgumentHandler(ValidationException e) {
		log.error("Assert异常:-------------->{}", e.getMessage(), e);
		e.printStackTrace();
		if (e instanceof ConstraintViolationException) {
			ConstraintViolationException exs = (ConstraintViolationException) e;
			Set<ConstraintViolation<?>> violations = exs.getConstraintViolations();
			for (ConstraintViolation<?> item : violations) {
				String message = ((PathImpl) item.getPropertyPath()).getLeafNode().getName() + item.getMessage();
				return ResponseUtil.fail(402, message);
			}
		}
		return ResponseUtil.badArgumentValue();
	}

	@ExceptionHandler(Exception.class)
	@ResponseBody
	public Object seriousHandler(Exception e) {
		log.error("Assert异常:-------------->{}", e.getMessage(), e);
		e.printStackTrace();
		return ResponseUtil.serious();
	}
}
