package com.pioneer.mall.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "dts.config")
public class WxConfigProperties {

    private List<WxConfigMap> list;

    // 获取默认配置
    public WxConfigMap getWxProperties() {
        for (WxConfigMap wxProperties : list) {
            if (wxProperties.getDefaultShop().equals(true)) {
                return wxProperties;
            }
        }
        return null;
    }

    // 根据shopid 获取配置
    public WxConfigMap getWxProperties(Integer shopId) {
        for (WxConfigMap wxProperties : list) {
            if (wxProperties.getShopId().equals(shopId)) {
                return wxProperties;
            }
        }
        return null;
    }

    public WxConfigMap getWxPropertiesBySerial(String serial){
        for (WxConfigMap wxProperties : list) {
            if (wxProperties.getSerial().equals(serial)) {
                return wxProperties;
            }
        }
        return null;
    }

    /**
     * 根据shopid 获取 mchId
     */
    public String getMchId(Integer shopId) {
        WxConfigMap wxProperties = getWxProperties(shopId);
        return wxProperties == null ? null : wxProperties.getMchId();
    }

    /**
     * 获取默认 defaultShop
     */

    public String getDefaultMchId() {
        for (WxConfigMap wxProperties : list) {
            if (wxProperties.getDefaultShop()) {
                return wxProperties.getMchId();
            }
        }
        return null;
    }
}
