package com.pioneer.mall.core.config;

import com.github.binarywang.wxpay.service.WxPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WxPayWrapperService {

    @Autowired
    private WxConfigProperties wxConfigProperties;
    @Autowired
    private WxPayService wxPayService;

    public void switchoverTo(String serial) {
        WxConfigMap wxProperties = wxConfigProperties.getWxPropertiesBySerial(serial);
        if (wxProperties == null) {
            throw new RuntimeException("根据serial没有找到mchId");
        }
        wxPayService.switchover(wxProperties.getMchId());
    }

    public void switchoverTo(Integer shopId) {
        String mchId = wxConfigProperties.getMchId(shopId);
        if (mchId == null) {
            throw new RuntimeException("根据shopId没有找到mchId");
        }
        wxPayService.switchover(mchId);
    }

    public void switchoverToDefault() {
        String mchId = wxConfigProperties.getDefaultMchId();
        if (mchId == null) {
            throw new RuntimeException("根据shopId没有找到mchId");
        }
        wxPayService.switchover(mchId);
    }


}
