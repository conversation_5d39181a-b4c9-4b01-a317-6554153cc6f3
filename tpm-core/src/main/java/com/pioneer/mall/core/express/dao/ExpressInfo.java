/**
 * Copyright 2018 bejson.com
 */
package com.pioneer.mall.core.express.dao;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ExpressInfo {

	@JsonProperty("LogisticCode")
	private String LogisticCode;
	@JsonProperty("ShipperCode")
	private String ShipperCode;
	@JsonProperty("Traces")
	private List<Traces> Traces;
	@JsonProperty("State")
	private String State;
	@JsonProperty("EBusinessID")
	private String EBusinessID;
	@JsonProperty("Success")
	private boolean Success;
	@JsonProperty("Reason")
	private String Reason;

	private String ShipperName;

	public String getLogisticCode() {
		return LogisticCode;
	}

	public void setLogisticCode(String LogisticCode) {
		this.LogisticCode = LogisticCode;
	}

	public String getShipperCode() {
		return ShipperCode;
	}

	public void setShipperCode(String ShipperCode) {
		this.ShipperCode = ShipperCode;
	}

	public List<Traces> getTraces() {
		return Traces;
	}

	public void setTraces(List<Traces> Traces) {
		this.Traces = Traces;
	}

	public String getState() {
		return State;
	}

	public void setState(String State) {
		this.State = State;
	}

	public String getEBusinessID() {
		return EBusinessID;
	}

	public void setEBusinessID(String EBusinessID) {
		this.EBusinessID = EBusinessID;
	}

	public boolean getSuccess() {
		return Success;
	}

	public void setSuccess(boolean Success) {
		this.Success = Success;
	}

	public String getReason() {
		return Reason;
	}

	public void setReason(String Reason) {
		this.Reason = Reason;
	}

	public String getShipperName() {
		return ShipperName;
	}

	public void setShipperName(String shipperName) {
		ShipperName = shipperName;
	}

	@Override
	public String toString() {
		return "ExpressInfo{" + "LogisticCode='" + LogisticCode + '\'' + ", ShipperCode='" + ShipperCode + '\''
				+ ", Traces=" + Traces + ", State='" + State + '\'' + ", EBusinessID='" + EBusinessID + '\''
				+ ", Success=" + Success + ", Reason=" + Reason + ", ShipperName='" + ShipperName + '\'' + '}';
	}
}