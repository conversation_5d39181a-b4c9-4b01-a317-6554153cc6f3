package com.pioneer.mall.core.judanke;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pioneer.mall.core.judanke.dto.JdkCommonResDto;
import com.pioneer.mall.core.judanke.dto.JdkShopConfig;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.WechatNotifyUtils;
import com.pioneer.mall.db.domain.TpmCouponCodeUseLog;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.domain.TpmShopInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
@Slf4j
public class JdkOrderService {

    @Value("${jdk.order.host}")
    private String host;
    @Value("${jdk.order.appId}")
    private String appId;
    @Value("${jdk.order.appKey}")
    private String appKey;
//    @Value("${jdk.order.shopId}")
//    private String shopId;
//    // 聚单客商家ID
    @Value("${jdk.order.relativeUserId}")
    private String relativeUserId;

    private static JdkShopConfig getJdkShopConfig(Integer tpmOrder) {
        String jdkConfig = SystemConfig.getJdkShopConfig(tpmOrder);
        JdkShopConfig jdkShopConfig = JSON.parseObject(jdkConfig, JdkShopConfig.class);
        return jdkShopConfig;
    }

    public String preCreateOrder(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList) {
        String path = "/Order/preCreate";
        try {
            JdkShopConfig jdkShopConfig = getJdkShopConfig(tpmOrder.getShopId());
            String shopId = jdkShopConfig.getShopId();
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data", JuDanKeOrderUtils.getPreCreateOrderData(tpmOrder, goodsList, shopId, relativeUserId));
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                if (!StringUtils.isEmpty(jdkCommonResDto.getData())) {
                    Map<String, Objects> orderMap = JSON.parseObject(jdkCommonResDto.getData(), Map.class);
                    Object orderId = orderMap.get("order_id");
                    if (orderId == null) {
                        throw new RuntimeException("聚单客预创建单接口返回订单号为空");
                    }
                    return orderId.toString();
                }
            } else {
                throw new RuntimeException("聚单客预创建单接口返回异常：" + jdkCommonResDto.getMsg());
            }
            return res;
        } catch (Exception e) {
            log.error("聚单客预创建单接口异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客预创建单接口异常:" + e.getMessage());
        }
    }

    public void newThirdQuotationAndThirdPublish(TpmOrder order, Long deliveryOrderId) {
        List<String> deliveryBrandsList;
        try {
            deliveryBrandsList = newThirdQuotation(deliveryOrderId, order.getOrderSn(), order.getAddress(),order.getShopId());
        } catch (Exception e) {
            log.error("聚单客订单询价接口异常 订单号:{} :{}", order.getOrderSn(), e.getMessage(), e);
            throw new RuntimeException("聚单客预创建单接口异常:" + e.getMessage());
        }
        if (CollectionUtils.isEmpty(deliveryBrandsList)) {
            WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(order.getShopId()), null, "聚单客订单询价接口异常，请手动发单：订单号:" + order.getOrderSn() + "地址:" + order.getAddress());
            return;
        }
        thirdPublish(order, deliveryOrderId, deliveryBrandsList);
    }

    public List<String> newThirdQuotation(Long deliveryOrderId, String orderSn, String address, Integer shopId) {
        String path = "/Order/newThirdQuotation";
        List<String> selectedBrands = new ArrayList<>();
        try {
            JdkShopConfig jdkShopConfig = getJdkShopConfig(shopId);
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data", JuDanKeOrderUtils.getNewThirdQuotationData(deliveryOrderId, relativeUserId));
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                if (!StringUtils.isEmpty(jdkCommonResDto.getData())) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode root = objectMapper.readTree(res);
                    JsonNode listNode = root.get("data").get("list");

                    List<JsonNode> validNodes = new ArrayList<>();
                    for (JsonNode node : listNode) {
                        if (node.get("close").asInt() == 0) {
                            validNodes.add(node);
                        }
                    }
                    validNodes.sort(Comparator.comparingInt(node -> node.get("total_fee").asInt()));
                    StringBuilder sb = new StringBuilder("聚单客询价成功:\n订单号" + orderSn + "\n地址:" + address + "\n运力品牌:");
                    for (int i = 0; i < Math.min(3, validNodes.size()); i++) {
                        JsonNode node = validNodes.get(i);
                        selectedBrands.add(node.get("brand").asText());
                        String brandName = node.get("brand_name").asText();
                        int totalFee = node.get("total_fee").asInt();
                        BigDecimal totalFeeDecimal = BigDecimal.valueOf(totalFee).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                        sb.append(brandName).append(":").append(totalFeeDecimal).append("元;\n");
                    }
                    WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(shopId), null, sb.toString());

                    return selectedBrands;
                }
            } else {
                throw new RuntimeException("聚单客预创建单接口返回异常：" + jdkCommonResDto.getMsg());
            }
            return selectedBrands;
        } catch (Exception e) {
            log.error("聚单客预创建单接口异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客预创建单接口异常:" + e.getMessage());
        }
    }

    /**
     * 不传预定时间
     *
     * @param tpmOrder
     * @param goodsList
     * @return
     */
    public String createOrder(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList) {
        return this.createOrder(tpmOrder, goodsList, null);
    }

    public void thirdPublish(TpmOrder order, Long deliveryOrderId, List<String> deliveryBrandsList) {
        String path = "/Order/thirdPublish";
        try {
            JdkShopConfig jdkShopConfig = getJdkShopConfig(order.getShopId());
            String shopId = jdkShopConfig.getShopId();
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data", JuDanKeOrderUtils.getThirdPublishData(deliveryOrderId, relativeUserId, deliveryBrandsList));
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            StringBuilder sb = new StringBuilder();
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                sb.append("聚单客发单成功:\n订单号:").append(order.getOrderSn()).append("\n地址:").append(order.getAddress());
                WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(order.getShopId()), null, sb.toString());
            } else {
                sb.append("聚单客发单失败，请手动发单:\n订单号:").append(order.getOrderSn()).append("\n地址:").append(order.getAddress());
                WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(order.getShopId()), null, sb.toString());
                throw new RuntimeException("聚单客预创建单接口返回异常：" + jdkCommonResDto.getMsg());
            }
//            return new ArrayList<>();
        } catch (Exception e) {
            log.error("聚单客预创建单接口异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客预创建单接口异常:" + e.getMessage());
        }
    }

    public String createOrder(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList, String pickUpTime) {
        String path = "/Order/publish";
        try {
            JdkShopConfig jdkShopConfig = getJdkShopConfig(tpmOrder.getShopId());
            String shopId = jdkShopConfig.getShopId();
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data", JuDanKeOrderUtils.getCreateOrderData(tpmOrder, goodsList, shopId, relativeUserId, pickUpTime));
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                if (!StringUtils.isEmpty(jdkCommonResDto.getData())) {
                    Map<String, Objects> orderMap = JSON.parseObject(jdkCommonResDto.getData(), Map.class);
                    Object orderId = orderMap.get("order_id");
                    if (orderId == null) {
                        throw new RuntimeException("聚单客预创建单接口返回订单号为空");
                    }
                    return orderId.toString();
                }
            } else {
                throw new RuntimeException("聚单客预创建单接口返回异常：" + jdkCommonResDto.getMsg());
            }
            return res;
        } catch (Exception e) {
            log.error("聚单客预创建单接口异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客预创建单接口异常:" + e.getMessage());
        }
    }

    public void printerPrint(TpmOrder order, TpmShopInfo shopInfo, List<TpmOrderGoods> orderGoods, String stickerType, String templateId) {
        this.printerPrint(order,shopInfo, orderGoods, stickerType, templateId, null);
    }


    public void printerPrint(TpmOrder order, TpmShopInfo shopInfo, List<TpmOrderGoods> orderGoods, String stickerType, String templateId, TpmCouponCodeUseLog tpmCouponCodeUseLog) {
        String path = "/Printer/prints";
        try {
            JdkShopConfig jdkShopConfig = getJdkShopConfig(order.getShopId());
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data", JuDanKeOrderUtils.getPrinterPrintData(order,shopInfo, orderGoods, jdkShopConfig,relativeUserId, stickerType, templateId,tpmCouponCodeUseLog));
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                log.info("printerPrint res={}", JSON.toJSONString(jdkCommonResDto));
            } else {
                throw new RuntimeException("聚单客预创建单接口返回异常：" + jdkCommonResDto.getMsg());
            }
        } catch (Exception e) {
            log.error("聚单客打印接口异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客打印接口异常:" + e.getMessage());
        }
    }


    /**
     * 打印 聚单客消息 小票
     * 提醒店员关注平台消息
     */
    public void printerPrintNotice(TpmOrder order) {
        String path = "/Printer/prints";
        try {
            JdkShopConfig jdkShopConfig = getJdkShopConfig(order.getShopId());
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data", JuDanKeOrderUtils.getPrinterPrintNoticeData(jdkShopConfig,relativeUserId));
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                log.info("printerPrint res={}", JSON.toJSONString(jdkCommonResDto));
            } else {
                throw new RuntimeException("聚单客预创建单接口返回异常：" + jdkCommonResDto.getMsg());
            }
        } catch (Exception e) {
            log.error("聚单客打印接口异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客打印接口异常:" + e.getMessage());
        }
    }

    public void orderCompleted(TpmOrder tpmOrder) {
        String path = "/Order/completed";
        try {
            JdkShopConfig jdkShopConfig = getJdkShopConfig(tpmOrder.getShopId());
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data", JuDanKeOrderUtils.getOrderCompletedData(tpmOrder, relativeUserId));
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                log.info("orderCompleted res={}", JSON.toJSONString(jdkCommonResDto));
            } else {
                throw new RuntimeException("聚单客完成订单接口返回异常：" + jdkCommonResDto.getMsg());
            }
        } catch (Exception e) {
            log.error("聚单客完成订单异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客完成订单异常:" + e.getMessage());
        }
    }

    /**
     * 查询聚单客账户余额
     *
     * @param shopId 店铺ID
     * @return 余额查询结果
     */
    public String queryBalance() {
        String path = "/Account/getBalance";
        try {
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data","{}");
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            log.info("queryBalance result={}", res);
            return res;
        } catch (Exception e) {
            log.error("查询聚单客账户余额异常 error:{}", e.getMessage(), e);
            throw new RuntimeException("查询聚单客账户余额异常: " + e.getMessage());
        }
    }




//    @Test
    public void getPrinterList() {
        host = "https://jop.judanke.cn";
        appId = "10501";
        appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
//        shopId = "155595";
        String relativeUserId = "*********";
        String path = "/Printer/bind";
        try {
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("sn", "12505561");
            requestBody.put("key", "9aqwhcn9");
            requestBody.put("brand", "ZW");
            requestBody.put("relative_user_id", relativeUserId);
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(requestBody);
            bodys.put("data", json);
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                log.info("orderCompleted res={}", JSON.toJSONString(jdkCommonResDto));
            } else {
                throw new RuntimeException("聚单客完成订单接口返回异常：" + jdkCommonResDto.getMsg());
            }
        } catch (Exception e) {
            log.error("聚单客完成订单异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客完成订单异常:" + e.getMessage());
        }

    }

    @Test
    public void getNewThirdQuotation() {
        host = "https://jop.judanke.cn";
        appId = "10501";
        appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
//        shopId = "155595";
//        relativeUserId = "*********";
        String path = "/Order/newThirdQuotation";
        try {
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("order_id", 911056528412751L);
//            requestBody.put("relative_user_id", relativeUserId);
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(requestBody);
            System.out.println("getNewThirdQuotationData 拼装data:" + json);
            bodys.put("data", json);
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                log.info("orderCompleted res={}", JSON.toJSONString(jdkCommonResDto));
                ObjectMapper objectMapper1 = new ObjectMapper();
                JsonNode root = objectMapper1.readTree(res);
                JsonNode listNode = root.get("data").get("list");

                List<JsonNode> validNodes = new ArrayList<>();
                for (JsonNode node : listNode) {
                    if (node.get("close").asInt() == 0) {
                        validNodes.add(node);
                    }
                }

                validNodes.sort(Comparator.comparingInt(node -> node.get("total_fee").asInt()));

                List<String> selectedBrands = new ArrayList<>();
                StringBuilder sb = new StringBuilder("聚单客询价成功:\n订单号测试环境" + "\n地址:" + "测试地址" + "\n运力品牌:");
                for (int i = 0; i < Math.min(3, validNodes.size()); i++) {
                    JsonNode node = validNodes.get(i);
                    selectedBrands.add(node.get("brand").asText());
                    String brandName = node.get("brand_name").asText();
                    int totalFee = node.get("total_fee").asInt();
                    BigDecimal totalFeeDecimal = BigDecimal.valueOf(totalFee).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                    sb.append(brandName).append(":").append(totalFeeDecimal).append("元;\n");
                }
//                WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(), null, sb.toString());

                System.out.println(JSON.toJSONString(selectedBrands));
            } else {
                throw new RuntimeException("聚单客完成订单接口返回异常：" + jdkCommonResDto.getMsg());
            }
        } catch (Exception e) {
            log.error("聚单客完成订单异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客完成订单异常:" + e.getMessage());
        }
    }

    /**
     * 聚单客接口调用,响应相关 result={"code":0,"msg":"成功","data":{"order_id":911056528412751}}
     * 22:33:44.030 [main] INFO com.pioneer.mall.core.judanke.JdkOrderService - orderCompleted res={"code":0,"data":"{\"order_id\":911056528412751}","msg":"成功"}
     */
    @Test
    public void getThirdPublish() {
        host = "https://jop.judanke.cn";
        appId = "10501";
        appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
//        shopId = "155595";
//        relativeUserId = "*********";
        String path = "/Order/thirdPublish";
        try {
            List<String> list = Arrays.asList("sftc", "kfw", "ddks");
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("order_id", 911056528412751L);
//            requestBody.put("relative_user_id", relativeUserId);
            requestBody.put("delivery_brands", list);
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(requestBody);
            System.out.println("getNewThirdQuotationData 拼装data:" + json);
            bodys.put("data", json);
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            JdkCommonResDto jdkCommonResDto = JSON.parseObject(res, JdkCommonResDto.class);
            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
                log.info("orderCompleted res={}", JSON.toJSONString(jdkCommonResDto));
            } else {
                throw new RuntimeException("聚单客完成订单接口返回异常：" + jdkCommonResDto.getMsg());
            }
        } catch (Exception e) {
            log.error("聚单客完成订单异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客完成订单异常:" + e.getMessage());
        }
    }


    @Test
    public void test() {
        host = "https://jop.judanke.cn";
        appId = "10501";
        appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
//        shopId = "155595";
//        relativeUserId = "*********";

        TpmOrder tpmOrder = new TpmOrder();
        tpmOrder.setFreightType(new Byte("1"));
        tpmOrder.setOrderSn("ABCDEFGHIGK");
        tpmOrder.setConsignee("醇香");
        tpmOrder.setMobile("13787656787");
        tpmOrder.setLngAndLat("113.324520,23.099692");
        tpmOrder.setGoodsPrice(new BigDecimal("300"));
        tpmOrder.setAddress("店内自提订单");
        List<TpmOrderGoods> goodsList = new ArrayList() {{
            TpmOrderGoods orderGoods = new TpmOrderGoods();
            orderGoods.setGoodsName("将进酒");
            orderGoods.setNumber(new Short("1"));
            orderGoods.setPrice(new BigDecimal("10.01"));
            orderGoods.setPicUrl("http://");
            add(orderGoods);

            TpmOrderGoods orderGoods1 = new TpmOrderGoods();
            orderGoods1.setGoodsName("Chocolate Hazelnut榛子巧克力甜甜圈");
            orderGoods1.setNumber(new Short("1"));
            orderGoods1.setPrice(new BigDecimal("10.01"));
            orderGoods1.setPicUrl("http://");
            add(orderGoods1);

            TpmOrderGoods orderGoods2 = new TpmOrderGoods();
            orderGoods2.setGoodsName("Chocolate Vinegar Banana巧克力黑醋香蕉甜甜圈");
            orderGoods2.setNumber(new Short("1"));
            orderGoods2.setPrice(new BigDecimal("10.01"));
            orderGoods2.setPicUrl("http://");
            add(orderGoods2);

            TpmOrderGoods orderGoods3 = new TpmOrderGoods();
            orderGoods3.setGoodsName("Chocolate Vinegar Banana巧克力黑醋香蕉甜甜圈/Chocolate Hazelnut榛子巧克力甜甜圈");
            orderGoods3.setNumber(new Short("1"));
            orderGoods3.setPrice(new BigDecimal("10.01"));
            orderGoods3.setPicUrl("http://");
            add(orderGoods3);
        }};

        preCreateOrder(tpmOrder, goodsList);

    }

    @Test
    public void getBindMerchantList() {
        host = "https://jop.judanke.cn";
        appId = "10501";
        appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
//        shopId = "155595";
//        relativeUserId = "*********";
        String path = "/Merchant/getBindMerchantList";
        try {
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            Map<String, Object> requestBody = new HashMap<>();
//            requestBody.put("app_id", appId);
//            requestBody.put("relative_user_id", relativeUserId);
//            requestBody.put("delivery_brands",list);
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(requestBody);
//            System.out.println("getNewThirdQuotationData 拼装data:" + json);
            bodys.put("data", json);
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
//            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
//                log.info("orderCompleted res={}",JSON.toJSONString(jdkCommonResDto));
//            } else {
//                throw new RuntimeException("聚单客完成订单接口返回异常：" + jdkCommonResDto.getMsg());
//            }
            System.out.println(JSON.toJSONString(res));
        } catch (Exception e) {
            log.error("聚单客完成订单异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客完成订单异常:" + e.getMessage());
        }
    }

    @Test
    public void sendVerifyCode() {
        host = "https://jop.judanke.cn";
        appId = "10501";
        appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
        String shopId = "155595";
        String relativeUserId = "*********";
        String path = "/Merchant/sendVerifyCode";
        try {
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("phone", "15857102788");
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(requestBody);
            System.out.println("sendVerifyCode 拼装data:" + json);
            bodys.put("data", json);
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
//            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
//                log.info("orderCompleted res={}",JSON.toJSONString(jdkCommonResDto));
//            } else {
//                throw new RuntimeException("聚单客完成订单接口返回异常：" + jdkCommonResDto.getMsg());
//            }
            System.out.println(JSON.toJSONString(res));
        } catch (Exception e) {
            log.error("聚单客完成订单异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客完成订单异常:" + e.getMessage());
        }
    }

    @Test
    public void changeMerchantPhone() {
        host = "https://jop.judanke.cn";
        appId = "10501";
        appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
        String shopId = "155595";
        String relativeUserId = "*********";
        String path = "/Merchant/changeMerchantPhone";
        try {
            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("phone", "15857102788");
            requestBody.put("code", "714227");
            requestBody.put("relative_user_id", relativeUserId);
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(requestBody);
            System.out.println("changeMerchantPhone 拼装data:" + json);
            bodys.put("data", json);
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
//            if (Objects.equals(jdkCommonResDto.getCode(), 0)) {
//                log.info("orderCompleted res={}",JSON.toJSONString(jdkCommonResDto));
//            } else {
//                throw new RuntimeException("聚单客完成订单接口返回异常：" + jdkCommonResDto.getMsg());
//            }
            System.out.println(JSON.toJSONString(res));
        } catch (Exception e) {
            log.error("聚单客完成订单异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客完成订单异常:" + e.getMessage());
        }
    }

}
