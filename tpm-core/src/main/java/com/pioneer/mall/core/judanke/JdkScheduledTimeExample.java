package com.pioneer.mall.core.judanke;

import com.alibaba.fastjson.JSON;
import com.pioneer.mall.core.judanke.dto.JdkOrderDto;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 聚单客预约时间功能示例
 * 展示如何使用 scheduledTime 字段设置 reserve_arrival_time
 */
@Slf4j
public class JdkScheduledTimeExample {

    /**
     * 示例1：使用 scheduledTime 创建预约订单
     */
    public static void exampleCreateScheduledOrder() {
        System.out.println("=== 聚单客预约订单创建示例 ===");
        
        // 创建订单对象
        TpmOrder order = createTestOrder();
        
        // 设置预约时间（HH:mm格式）
        order.setScheduledTime("20:00");
        
        // 创建订单商品列表
        List<TpmOrderGoods> goodsList = createTestOrderGoods();
        
        try {
            // 生成聚单客订单数据
            String orderData = JuDanKeOrderUtils.getPreCreateOrderData(order, goodsList, "SHOP001", "USER001");
            
            System.out.println("原始预约时间: " + order.getScheduledTime());
            
            // 解析生成的数据查看结果
            JdkOrderDto jdkOrderDto = JSON.parseObject(orderData, JdkOrderDto.class);
            System.out.println("聚单客预约到达时间: " + jdkOrderDto.getReserve_arrival_time());
            System.out.println("预约标识: " + jdkOrderDto.getBooking());
            
            // 验证格式
            String expectedDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String expectedTime = expectedDate + " 20:00:00";
            
            if (expectedTime.equals(jdkOrderDto.getReserve_arrival_time())) {
                System.out.println("✓ 预约时间格式正确");
            } else {
                System.out.println("✗ 预约时间格式错误");
                System.out.println("  期望: " + expectedTime);
                System.out.println("  实际: " + jdkOrderDto.getReserve_arrival_time());
            }
            
        } catch (Exception e) {
            System.out.println("✗ 创建预约订单失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 示例2：不同时间格式的测试
     */
    public static void exampleDifferentTimeFormats() {
        System.out.println("\n=== 不同预约时间格式测试 ===");
        
        String[] testTimes = {
            "08:00",    // 早上8点
            "12:30",    // 中午12点30分
            "18:45",    // 下午6点45分
            "23:59"     // 晚上11点59分
        };
        
        for (String timeStr : testTimes) {
            TpmOrder order = createTestOrder();
            order.setScheduledTime(timeStr);
            
            try {
                String orderData = JuDanKeOrderUtils.getPreCreateOrderData(order, createTestOrderGoods(), "SHOP001", "USER001");
                JdkOrderDto jdkOrderDto = JSON.parseObject(orderData, JdkOrderDto.class);
                
                String expectedDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String expectedTime = expectedDate + " " + timeStr + ":00";
                
                System.out.println("输入时间: " + timeStr);
                System.out.println("输出时间: " + jdkOrderDto.getReserve_arrival_time());
                System.out.println("格式正确: " + expectedTime.equals(jdkOrderDto.getReserve_arrival_time()));
                System.out.println("---");
                
            } catch (Exception e) {
                System.out.println("处理时间 " + timeStr + " 失败: " + e.getMessage());
            }
        }
    }

    /**
     * 示例3：无预约时间的订单
     */
    public static void exampleNoScheduledTime() {
        System.out.println("\n=== 无预约时间订单测试 ===");
        
        TpmOrder order = createTestOrder();
        // 不设置 scheduledTime，保持为 null
        
        try {
            String orderData = JuDanKeOrderUtils.getPreCreateOrderData(order, createTestOrderGoods(), "SHOP001", "USER001");
            JdkOrderDto jdkOrderDto = JSON.parseObject(orderData, JdkOrderDto.class);
            
            System.out.println("预约时间: " + order.getScheduledTime());
            System.out.println("预约标识: " + jdkOrderDto.getBooking());
            System.out.println("预约到达时间: " + jdkOrderDto.getReserve_arrival_time());
            
            if (jdkOrderDto.getBooking() == null && jdkOrderDto.getReserve_arrival_time() == null) {
                System.out.println("✓ 无预约时间订单处理正确");
            } else {
                System.out.println("✗ 无预约时间订单处理错误");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 处理无预约时间订单失败: " + e.getMessage());
        }
    }

    /**
     * 示例4：空字符串预约时间的处理
     */
    public static void exampleEmptyScheduledTime() {
        System.out.println("\n=== 空字符串预约时间测试 ===");
        
        String[] emptyValues = {"", "   ", null};
        
        for (String emptyValue : emptyValues) {
            TpmOrder order = createTestOrder();
            order.setScheduledTime(emptyValue);
            
            try {
                String orderData = JuDanKeOrderUtils.getPreCreateOrderData(order, createTestOrderGoods(), "SHOP001", "USER001");
                JdkOrderDto jdkOrderDto = JSON.parseObject(orderData, JdkOrderDto.class);
                
                System.out.println("输入值: '" + emptyValue + "'");
                System.out.println("预约标识: " + jdkOrderDto.getBooking());
                System.out.println("预约到达时间: " + jdkOrderDto.getReserve_arrival_time());
                
                boolean isCorrect = jdkOrderDto.getBooking() == null && jdkOrderDto.getReserve_arrival_time() == null;
                System.out.println("处理正确: " + isCorrect);
                System.out.println("---");
                
            } catch (Exception e) {
                System.out.println("处理空值 '" + emptyValue + "' 失败: " + e.getMessage());
            }
        }
    }

    /**
     * 示例5：完整的订单数据展示
     */
    public static void exampleCompleteOrderData() {
        System.out.println("\n=== 完整订单数据展示 ===");
        
        TpmOrder order = createTestOrder();
        order.setScheduledTime("19:30");
        
        try {
            String orderData = JuDanKeOrderUtils.getPreCreateOrderData(order, createTestOrderGoods(), "SHOP001", "USER001");
            
            System.out.println("生成的聚单客订单数据:");
            System.out.println(JSON.toJSONString(JSON.parseObject(orderData), true));
            
        } catch (Exception e) {
            System.out.println("✗ 生成完整订单数据失败: " + e.getMessage());
        }
    }

    /**
     * 示例6：时间格式验证
     */
    public static void exampleTimeFormatValidation() {
        System.out.println("\n=== 时间格式验证示例 ===");
        
        LocalDate today = LocalDate.now();
        String todayStr = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        System.out.println("当前日期: " + todayStr);
        System.out.println("时间格式转换示例:");
        
        String[] times = {"08:00", "12:30", "18:45", "23:59"};
        
        for (String time : times) {
            String fullDateTime = todayStr + " " + time + ":00";
            System.out.println("  " + time + " -> " + fullDateTime);
        }
    }

    /**
     * 创建测试订单对象
     */
    private static TpmOrder createTestOrder() {
        TpmOrder order = new TpmOrder();
        order.setId(1001);
        order.setOrderSn("TEST" + System.currentTimeMillis());
        order.setConsignee("测试用户");
        order.setMobile("13800138000");
        order.setFreightType((byte) 0); // 外卖
        order.setOrderPrice(new BigDecimal("50.00"));
        order.setActualPrice(new BigDecimal("55.00"));
        order.setAddress("测试地址");
        return order;
    }

    /**
     * 创建测试订单商品列表
     */
    private static List<TpmOrderGoods> createTestOrderGoods() {
        List<TpmOrderGoods> goodsList = new ArrayList<>();
        
        TpmOrderGoods goods1 = new TpmOrderGoods();
        goods1.setId(1);
        goods1.setGoodsName("测试商品1");
        goods1.setNumber((short) 2);
        goods1.setPrice(new BigDecimal("20.00"));
        goodsList.add(goods1);
        
        TpmOrderGoods goods2 = new TpmOrderGoods();
        goods2.setId(2);
        goods2.setGoodsName("测试商品2");
        goods2.setNumber((short) 1);
        goods2.setPrice(new BigDecimal("30.00"));
        goodsList.add(goods2);
        
        return goodsList;
    }

    /**
     * 运行所有示例
     */
    public static void runAllExamples() {
        System.out.println("聚单客预约时间功能演示");
        System.out.println("========================");
        
        exampleCreateScheduledOrder();
        exampleDifferentTimeFormats();
        exampleNoScheduledTime();
        exampleEmptyScheduledTime();
        exampleCompleteOrderData();
        exampleTimeFormatValidation();
        
        System.out.println("\n========================");
        System.out.println("演示完成");
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        runAllExamples();
    }
}
