package com.pioneer.mall.core.judanke;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pioneer.mall.core.judanke.dto.*;
import com.pioneer.mall.db.domain.TpmCouponCodeUseLog;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.util.HttpUtils;
import com.pioneer.mall.db.util.SpecificationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 聚单客平台对接
 * @date 2024/5/30 19:20
 */
@Slf4j
public class JuDanKeOrderUtils {


    //构造请求体
    public static Map<String, String> getHttpBodys(String path) {
        // 到http://open.judanke.cn/申请
//        String appId = SystemConfig.getJDKAppid();
//        String appKey = SystemConfig.getJDKAppKey();
        String appId = null;
        String appKey = null;
        return getHttpBodys(path, appId, appKey);
    }

    public static Map<String, String> getHttpBodys(String path, String appId, String appKey) {
        Map<String, String> bodys = new HashMap<>();
        // 当前时间戳
        String ts = new Date().getTime() + "";
        // 随机字符串
        String nonce = getNonce(16);
        // 按照规则(sha1(ts + app_key + api + app_id +
        // nonce))生成的合法性验证签名(40位字符串，字母小写)。如：166849492256139936e011ade195c154b1ed709c7cbd9f099ea61/Other/getAllDeliveryBrand100100ghod8141m7h加密后e053c9a216916de4a1a4ba9f5a1c04d408accd925
        // 计算签名
        String signStr = ts + appKey + path + appId + nonce;
        String sign = Sha1(signStr, 40);

        bodys.put("app_id", appId);
        bodys.put("sign", sign);
        bodys.put("ts", ts);
        bodys.put("nonce", nonce);
        return bodys;
    }

    //发起请求
    private static String doHttpRequest(String path, Map<String, String> bodys) throws Exception {
        String host = "https://jop.judanke.cn";
        String requestMethod = "POST";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> querys = new HashMap<>();
        // 根据API的要求，定义相对应的Content-Type
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        HttpResponse response = HttpUtils.doPost(host, path, requestMethod, headers,
                querys, bodys);
        System.out.println(response.toString());
        //获取response的body
        String result = EntityUtils.toString(response.getEntity());
        System.out.println("JuDanKeOrderUtils doHttpRequest result=" + result);
        return result;
    }

    private static String getNonce(int size) {
        byte[] array = new byte[size];
        new Random().nextBytes(array);
        // return Base64.getEncoder().encodeToString(array);
        return toHexString(array);
    }

    // 计算Sha1
    public static String Sha1(String plainText, int length) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA1");// 获取MD5实例
            md.update(plainText.getBytes(StandardCharsets.UTF_8));// 此处传入要加密的byte类型值
            byte[] digest = md.digest();// 此处得到的是md5加密后的byte类型值

            return toHexString(digest).substring(0, length);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String toHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(java.lang.String.format("%02x", b));
        }
        return sb.toString();
    }


    /**
     * 预创建订单
     * https://open.judanke.cn/docs/130/86
     * 正式地址	https://jop.judanke.cn/Order/preCreate
     * 测试地址	https://joptest.judanke.cn/Order/preCreate
     *
     * @param tpmOrder
     * @param goodsList
     * @return 聚客单平台order_id
     * @throws JsonProcessingException
     */
    @Deprecated
    public String preCreateOrder(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList) throws Exception {
        String path = "/Order/preCreate";
        Map<String, String> bodys = getHttpBodys(path);
        bodys.put("data", getPreCreateOrderData(tpmOrder, goodsList));
        try {
            String res = doHttpRequest(path, bodys);
            return res;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 构造预下单报文
     *
     * @param tpmOrder
     * @param goodsList
     * @return
     * @throws Exception
     */
    public static String getPreCreateOrderData(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList) throws Exception {

        return getPreCreateOrderData(tpmOrder, goodsList, null, null);
    }

    public static String getPreCreateOrderData(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList, String shopId, String relativeUserId) throws Exception {

        JdkOrderDto jdkOrderDto = new JdkOrderDto();
        jdkOrderDto.setThird_order_no(tpmOrder.getOrderSn());
        jdkOrderDto.setReceiver_name(tpmOrder.getConsignee());
        jdkOrderDto.setReceiver_phone(tpmOrder.getMobile());

        // 处理预约时间
        if (tpmOrder.getScheduledTime() != null && !tpmOrder.getScheduledTime().trim().isEmpty()) {
            jdkOrderDto.setBooking(1);

            // 使用 scheduledTime 字段（HH:mm格式）拼接为当天的完整时间
            String scheduledTimeStr = tpmOrder.getScheduledTime().trim();

            // 获取当天日期
            LocalDate today = LocalDate.now();

            // 拼接为完整的日期时间格式 "yyyy-MM-dd HH:mm:ss"
            String reserveArrivalTime = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + scheduledTimeStr + ":00";

            jdkOrderDto.setReserve_arrival_time(reserveArrivalTime);
            log.info("设置聚单客预约到达时间: {} (原始预约时间: {})", reserveArrivalTime, scheduledTimeStr);
        }

        // 配送方式 ：0 快递, 1 自提
        if (Objects.equals(tpmOrder.getFreightType(), Byte.valueOf("1"))) {
            // 自提的订单
            if (StringUtils.isEmpty(tpmOrder.getConsignee())) {
                jdkOrderDto.setReceiver_name("自提用户");
            }
            if (StringUtils.isEmpty(tpmOrder.getMobile())) {
                jdkOrderDto.setReceiver_phone("15336557627");
            }
            jdkOrderDto.setReceiver_lng("120.214997");
            jdkOrderDto.setReceiver_lat("30.212986");
            jdkOrderDto.setReceiver_address("===>店内自提订单<===");
        } else {
            String lngAndLat = tpmOrder.getLngAndLat();
            String[] split = StringUtils.split(lngAndLat, ",");
            jdkOrderDto.setReceiver_lng(split[0]);
            jdkOrderDto.setReceiver_lat(split[1]);
            jdkOrderDto.setReceiver_address(tpmOrder.getAddress());
        }
        // 转下分
        Integer price = tpmOrder.getGoodsPrice().multiply(new BigDecimal(100)).intValue();
        jdkOrderDto.setGoods_price(price);
        jdkOrderDto.setGoods_weight(500);
        jdkOrderDto.setGoods_quantity(goodsList.stream().mapToInt(TpmOrderGoods::getNumber).sum());
        // 明细
        List<JdkOrderItemDto> order_item_list = goodsList.stream().map(goods -> {
            JdkOrderItemDto jdkOrderItemDto = new JdkOrderItemDto();
            jdkOrderItemDto.setName(goods.getGoodsName());
            jdkOrderItemDto.setQuantity(Integer.valueOf(goods.getNumber()));
//            jdkOrderItemDto.setWeight();
            jdkOrderItemDto.setPrice(goods.getPrice().multiply(new BigDecimal(100)).intValue());
            jdkOrderItemDto.setTotal(jdkOrderItemDto.getQuantity() * jdkOrderItemDto.getPrice());
            jdkOrderItemDto.setImg(goods.getPicUrl());
            return jdkOrderItemDto;
        }).collect(Collectors.toList());

        JdkSourceOrderDataDto source_order_data = new JdkSourceOrderDataDto();
        source_order_data.setOrder_item_list(order_item_list);
        jdkOrderDto.setSource_order_data(source_order_data);

        return getPreCreateOrderData(jdkOrderDto, shopId, relativeUserId);
    }

    public static String getCreateOrderData(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList, String shopId, String relativeUserId) throws Exception {
        return getCreateOrderData(tpmOrder, goodsList, shopId, relativeUserId);
    }

    public static String getCreateOrderData(TpmOrder tpmOrder, List<TpmOrderGoods> goodsList, String shopId, String relativeUserId, String pickupTime) throws Exception {
        JdkOrderDto jdkOrderDto = new JdkOrderDto();
        jdkOrderDto.setThird_order_no(tpmOrder.getOrderSn());
        jdkOrderDto.setReceiver_name(tpmOrder.getConsignee());
        jdkOrderDto.setReceiver_phone(tpmOrder.getMobile());
        if (Objects.nonNull(pickupTime)) {
            jdkOrderDto.setBooking(1);
            jdkOrderDto.setPickup_time(pickupTime);
        }
        // 配送方式 ：0 外卖, 1 自提
        if (Objects.equals(tpmOrder.getFreightType(), Byte.valueOf("0"))) {
            String lngAndLat = tpmOrder.getLngAndLat();
            String[] split = StringUtils.split(lngAndLat, ",");
            jdkOrderDto.setReceiver_lng(split[0]);
            jdkOrderDto.setReceiver_lat(split[1]);
            jdkOrderDto.setReceiver_address(tpmOrder.getAddress());
        }
        // 转下分
        Integer price = tpmOrder.getGoodsPrice().multiply(new BigDecimal(100)).intValue();
        jdkOrderDto.setGoods_price(price);
        jdkOrderDto.setGoods_weight(500);
        jdkOrderDto.setGoods_quantity(goodsList.stream().mapToInt(TpmOrderGoods::getNumber).sum());
        // 明细
        List<JdkOrderItemDto> order_item_list = goodsList.stream().map(goods -> {
            JdkOrderItemDto jdkOrderItemDto = new JdkOrderItemDto();
            jdkOrderItemDto.setName(goods.getGoodsName());
            jdkOrderItemDto.setQuantity(Integer.valueOf(goods.getNumber()));
//            jdkOrderItemDto.setWeight();
            jdkOrderItemDto.setPrice(goods.getPrice().multiply(new BigDecimal(100)).intValue());
            jdkOrderItemDto.setTotal(jdkOrderItemDto.getQuantity() * jdkOrderItemDto.getPrice());
            jdkOrderItemDto.setImg(goods.getPicUrl());
            return jdkOrderItemDto;
        }).collect(Collectors.toList());

        JdkSourceOrderDataDto source_order_data = new JdkSourceOrderDataDto();
        source_order_data.setOrder_item_list(order_item_list);
        jdkOrderDto.setSource_order_data(source_order_data);
        return getPreCreateOrderData(jdkOrderDto, shopId, relativeUserId);
    }

    public static String getPreCreateOrderData(JdkOrderDto jdkOrderDto, String shopId, String relativeUserId) throws Exception {
//        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("third_order_no", jdkOrderDto.getThird_order_no());
//        requestBody.put("shop_id", shopId);
//        requestBody.put("receiver_name", jdkOrderDto.getReceiver_name());
//        requestBody.put("receiver_phone", jdkOrderDto.getReceiver_phone());
//        requestBody.put("receiver_lng", jdkOrderDto.getReceiver_lng());
//        requestBody.put("receiver_lat", jdkOrderDto.getReceiver_lat());
//        requestBody.put("goods_price", jdkOrderDto.getGoods_price());
//        requestBody.put("goods_weight", jdkOrderDto.getGoods_weight());
//        requestBody.put("goods_quantity", jdkOrderDto.getGoods_quantity());
//        requestBody.put("goods_type_id", 5);
//        requestBody.put("relative_user_id", relativeUserId);
//        requestBody.put("third_platform", "WEIXIN");
//        requestBody.put("receiver_address", jdkOrderDto.getReceiver_address());

//        ObjectMapper objectMapper = new ObjectMapper();
//        String json = objectMapper.writeValueAsString(requestBody);


        jdkOrderDto.setShop_id(shopId);
        jdkOrderDto.setGoods_type_id(5);
        jdkOrderDto.setRelative_user_id(relativeUserId);
        jdkOrderDto.setThird_platform("WEIXIN");
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(jdkOrderDto);
        System.out.println("getPreCreateOrderData 拼装data:" + json);
        return json;
    }

    public static String getOrderCompletedData(TpmOrder tpmOrder, String relativeUserId) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("order_id", tpmOrder.getDeliveryOrderId());
        requestBody.put("brand", "XY");
        requestBody.put("relative_user_id", relativeUserId);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        System.out.println("getOrderCompletedData 拼装data:" + json);
        return json;

    }

    public static String getPrinterPrintData(TpmOrder tpmOrder, TpmShopInfo shopInfo, List<TpmOrderGoods> goodsList,JdkShopConfig jdkShopConfig, String relativeUserId, String stickerType, String templateId, TpmCouponCodeUseLog tpmCouponCodeUseLog) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("guid", "PTC-ZW-12503110");
//        requestBody.put("brand", "ZW");
        requestBody.put("guid", jdkShopConfig.getPrintGuid());
        requestBody.put("brand",jdkShopConfig.getPrintBrand());
        requestBody.put("relative_user_id", relativeUserId);

        JSONObject recipient = new JSONObject();
        JSONObject address = new JSONObject();
        address.put("province", shopInfo.getProvinceName());
        address.put("city",  shopInfo.getCityName());
        address.put("district", "");
        address.put("town", "");
        address.put("detail", tpmOrder.getAddress());
        recipient.put("address", address);
        recipient.put("mobile", tpmOrder.getMobile());
        recipient.put("name", tpmOrder.getConsignee());
        recipient.put("privacy_phone", tpmOrder.getMobile());

        JSONObject printSetting = new JSONObject();
        printSetting.put("deliver_time_size", "l");
        printSetting.put("customer_addr_size", "m");
        printSetting.put("customer_name_size", "m");
        printSetting.put("customer_phone_size", "m");
        printSetting.put("goods_size", "s");
        printSetting.put("note_size", "l");

        JSONObject cost1 = new JSONObject();
        cost1.put("name", "餐盒费");
        cost1.put("amount", tpmOrder.getPackingFee());

        JSONObject cost2 = new JSONObject();
        cost2.put("name", "配送费");
        cost2.put("amount", tpmOrder.getFreightPrice());
        BigDecimal goodsAmount = BigDecimal.ZERO;
        BigDecimal totalCount = BigDecimal.ZERO;
        List<JSONObject> goods = new ArrayList<>();
        for (TpmOrderGoods tpmOrderGoods : goodsList) {
            JSONObject good = new JSONObject();
            if (!StringUtils.isEmpty(tpmOrderGoods.getSpecifications())) {
                String specificationsValueJoinByAttributes = SpecificationUtil.getSpecificationsValueJoinByAttributes(tpmOrderGoods.getSpecifications(), "/");
                good.put("name", tpmOrderGoods.getGoodsName() + "(" + specificationsValueJoinByAttributes + ")");
            }else{
                good.put("name", tpmOrderGoods.getGoodsName());
            }
            good.put("count", tpmOrderGoods.getNumber());
            good.put("price", tpmOrderGoods.getPrice());
            BigDecimal total = BigDecimal.valueOf(tpmOrderGoods.getNumber()).multiply(tpmOrderGoods.getPrice());
            good.put("amount", total);
            goodsAmount = goodsAmount.add(total);
            totalCount = totalCount.add(BigDecimal.valueOf(tpmOrderGoods.getNumber()));
            goods.add(good);
        }

        JSONObject printerData = new JSONObject();
        printerData.put("pick_type", "0");
        printerData.put("order_seq", tpmOrder.getMealCode());
        printerData.put("order_source", "7RiverLight");
        printerData.put("shop_name", "7RiverLight");
        printerData.put("pay_type", "用户在线支付");
        printerData.put("order_platform", "小程序");
        LocalDateTime localDateTime = tpmOrder.getPayTime().plusMinutes(60);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        String formattedTime = localDateTime.format(formatter);
        printerData.put("deliver_time", formattedTime);
        printerData.put("booking", "0");
        printerData.put("order_time", tpmOrder.getPayTime().format(formatter));
        printerData.put("order_sn", tpmOrder.getOrderSn());
        printerData.put("total_amount", tpmOrder.getActualPrice());
        printerData.put("pay_amount", tpmOrder.getActualPrice());

        printerData.put("goods_amount", goodsAmount);
        printerData.put("cost_amount", tpmOrder.getActualPrice());
        printerData.put("discount_amount", tpmOrder.getCouponPrice() == null ? BigDecimal.ZERO : tpmOrder.getCouponPrice());
        printerData.put("goods_total_number", totalCount);
        printerData.put("recipient", recipient);
        printerData.put("shop_phone", "13792093760");
        if(StringUtils.isNotBlank(tpmOrder.getMessage())){
            printerData.put("note",tpmOrder.getMessage());
        }else{
            printerData.put("note", "依据餐量提供餐具");
        }
        printerData.put("goods", goods);
        printerData.put("cost", new JSONObject[]{cost1, cost2});
        if (Objects.nonNull(tpmCouponCodeUseLog)){
            JSONObject discount = new JSONObject();
            discount.put("name", "优惠口令:"+tpmCouponCodeUseLog.getCode());
            discount.put("amount", tpmCouponCodeUseLog.getDiscountAmount());
            printerData.put("discount", new JSONObject[]{discount});
        }else{
            printerData.put("discount", new JSONObject[]{});
        }
        printerData.put("is_new_customer", "");
        printerData.put("buyer", "");
        printerData.put("show_deliver_time", "1");
        printerData.put("print_setting", printSetting);
        printerData.put("sticker_type", stickerType);
        printerData.put("page_width", "58");
        printerData.put("template_id", templateId);
        printerData.put("tid", "80417387710645916817266137451");

        requestBody.put("printer_datas", new JSONObject[]{printerData});

        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        log.info(" 拼装data:" + json);
        return json;
    }

    public static String getPrinterPrintNoticeData(JdkShopConfig jdkShopConfig,String relativeUserId) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("guid", "PTC-ZW-12503110");
//        requestBody.put("brand", "ZW");
        requestBody.put("guid", jdkShopConfig.getPrintGuid());
        requestBody.put("brand",jdkShopConfig.getPrintBrand());
        requestBody.put("relative_user_id", relativeUserId);

        JSONObject printerData = new JSONObject();
        printerData.put("template_id", "195109");
        printerData.put("tid", UUID.randomUUID().toString());

        requestBody.put("printer_datas", new JSONObject[]{printerData});

        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        log.info(" 拼装data:" + json);
        return json;
    }


    public static String getNewThirdQuotationData(Long orderId, String relativeUserId) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("order_id", orderId);
        requestBody.put("relative_user_id", relativeUserId);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        System.out.println("getNewThirdQuotationData 拼装data:" + json);
        return json;
    }
    public static String getThirdPublishData(Long orderId, String relativeUserId, List<String> deliveryBrandsList) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("order_id", orderId);
        requestBody.put("relative_user_id", relativeUserId);
        requestBody.put("delivery_brands",deliveryBrandsList);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        System.out.println("getThirdPublishData 拼装data:" + json);
        return json;
    }


    public static void main(String[] args) throws JsonProcessingException {
        String path = "/Order/preCreate";
        Map<String, String> bodys = getHttpBodys(path);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("third_order_no", "ABC34234234234234");
        requestBody.put("shop_id", "154789");
        requestBody.put("receiver_name", "test");
        requestBody.put("receiver_phone", "13300001111");
        requestBody.put("receiver_lng", "120.214897");
        requestBody.put("receiver_lat", "30.212976");
        requestBody.put("goods_price", 50);
        requestBody.put("goods_weight", 500);
        requestBody.put("goods_quantity", 1);
        requestBody.put("goods_type_id", 5);
        requestBody.put("relative_user_id", "340020352");
        requestBody.put("third_platform", "WEIXIN");
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        System.out.println("getPreCreateOrderData 拼装data:" + json);
        bodys.put("data", json);
        log.info("bodys = {}", JSON.toJSONString(bodys));
        try {
            String host = "https://jop.judanke.cn";
            String requestMethod = "POST";
            Map<String, String> headers = new HashMap<>();
            Map<String, String> querys = new HashMap<>();
            // 根据API的要求，定义相对应的Content-Type
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            HttpResponse response = HttpUtils.doPost(host, path, requestMethod, headers,
                    querys, bodys);
            System.out.println(response.toString());
            //获取response的body
            String result = EntityUtils.toString(response.getEntity());
            System.out.println("JuDanKeOrderUtils doHttpRequest result=" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取打印机
     *
     * @throws JsonProcessingException
     */
    @Test
    public void testPrintList() throws JsonProcessingException {
        String host = "https://jop.judanke.cn";
        String appId = "10501";
        String appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
        String shopId = "155595";
        String relativeUserId = "340986925";
        String path = "/Printer/getList";
        Map<String, String> bodys = getHttpBodys(path, appId, appKey);
        Map<String, Object> requestBody = new HashMap<>();

        requestBody.put("relative_user_id", "340986925");
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        System.out.println("getPreCreateOrderData 拼装data:" + json);
        bodys.put("data", json);
        log.info("bodys = {}", JSON.toJSONString(bodys));
        try {
            String requestMethod = "POST";
            Map<String, String> headers = new HashMap<>();
            Map<String, String> querys = new HashMap<>();
            // 根据API的要求，定义相对应的Content-Type
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            HttpResponse response = HttpUtils.doPost(host, path, requestMethod, headers,
                    querys, bodys);
            System.out.println(response.toString());
            //获取response的body
            String result = EntityUtils.toString(response.getEntity());
            System.out.println("JuDanKeOrderUtils doHttpRequest result=" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String doHttpRequestPost(String host, String path, Map<String, String> bodys) throws Exception {
        try {
            String requestMethod = "POST";
            Map<String, String> headers = new HashMap<>();
            Map<String, String> querys = new HashMap<>();
            log.info("聚单客接口调用,请求相关：host:{}, path={}, body= {}", host, path, JSON.toJSONString(bodys));
            // 根据API的要求，定义相对应的Content-Type
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            HttpResponse response = HttpUtils.doPost(host, path, requestMethod, headers,
                    querys, bodys);
            log.info(response.toString());
            //获取response的body
            String result = EntityUtils.toString(response.getEntity());
            log.info("聚单客接口调用,响应相关 result={}", result);
            return result;
        } catch (Exception e) {
            log.error("聚单客接口调用异常：{}", e.getMessage(), e);
            throw new RuntimeException("聚单客接口调用异常:" + e.getMessage());
        }
    }

    @Test
    public void shopList(){
        String host = "https://jop.judanke.cn";

        String appId = "10501";
        String appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
        // 需要访问的api
        String path = "/Shop/getList";
        String requestMethod = "POST";
        Map<String, String> headers = new HashMap<String, String>();
        // 根据API的要求，定义相对应的Content-Type
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, String> querys = new HashMap<String, String>();
        Map<String, String> bodys = new HashMap<String, String>();

        // 当前时间戳
        String ts = new Date().getTime() + "";

        // 随机字符串
        String nonce = getNonce(16);
        // 按照规则(sha1(ts + app_key + api + app_id +
        // nonce))生成的合法性验证签名(40位字符串，字母小写)。如：166849492256139936e011ade195c154b1ed709c7cbd9f099ea61/Other/getAllDeliveryBrand100100ghod8141m7h加密后e053c9a216916de4a1a4ba9f5a1c04d408accd925
        // 计算签名
        String signStr = ts + appKey + path + appId + nonce;
        String sign = Sha1(signStr, 40);

        bodys.put("app_id", appId);
        bodys.put("sign", sign);
        bodys.put("ts", ts);
        bodys.put("nonce", nonce);
        // data参数是个json格式的字符串 建议使用函数或方法去生成
        bodys.put("data", "{\"page\":1, \"size\": 20}");

        try {
            /**
             * 重要提示如下:
             * HttpUtils请从
             *
             https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
             * 下载
             *
             * 相应的依赖请参照
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
             */
            HttpResponse response = HttpUtils.doPost(host, path, requestMethod, headers,
                    querys, bodys);
            System.out.println(response.toString());

            //获取response的body
            System.out.println(EntityUtils.toString(response.getEntity()));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


//    @Test
    public void testPrint() throws JsonProcessingException {
        String host = "https://jop.judanke.cn";
        String appId = "10501";
        String appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
        String relativeUserId = "340986925";
        String path = "/Printer/prints";
        Map<String, String> bodys = getHttpBodys(path, appId, appKey);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("guid", "PTC-ZW-12505561");
        requestBody.put("brand", "ZW");
        requestBody.put("relative_user_id", relativeUserId);

        JSONObject recipient = new JSONObject();
        JSONObject address = new JSONObject();
        address.put("province", "上海市");
        address.put("city", "上海市");
        address.put("district", "长宁区");
        address.put("detail", "建滔商业广场中庭 6 号楼 6 号楼 6 楼 快宝");
        address.put("town", "");
        recipient.put("address", address);
        recipient.put("mobile", "158****9712");
        recipient.put("name", "徐**1");
        recipient.put("privacy_phone", "18466818985#641");

        JSONObject printSetting = new JSONObject();
        printSetting.put("deliver_time_size", "l");
        printSetting.put("customer_addr_size", "m");
        printSetting.put("customer_name_size", "m");
        printSetting.put("customer_phone_size", "m");
        printSetting.put("goods_size", "m");
        printSetting.put("note_size", "l");

        JSONObject cost1 = new JSONObject();
        cost1.put("name", "餐盒费");
        cost1.put("amount", 0.01);
        cost1.put("type", "4");

        JSONObject cost2 = new JSONObject();
        cost2.put("name", "配送费");
        cost2.put("amount", 0.1);
        cost2.put("type", "5");

        JSONObject good = new JSONObject();
        good.put("name", "玫瑰花");
        good.put("count", 1);
        good.put("price", 0.1);
        good.put("amount", 0.1);

        JSONObject printerData = new JSONObject();
        printerData.put("pick_type", "0");
        printerData.put("order_seq", "2");
        printerData.put("order_source", "ele");
        printerData.put("shop_name", "聚单客测试店铺");
        printerData.put("pay_type", "用户在线支付");
        printerData.put("order_platform", "饿了么");
        printerData.put("deliver_time", "今天 12:50");
        printerData.put("booking", "0");
        printerData.put("order_time", "2024-08-25 10:46:10");
        printerData.put("order_sn", "908254464112968");
        printerData.put("total_amount", 0.21);
        printerData.put("pay_amount", 0.21);
        printerData.put("goods_amount", 0.1);
        printerData.put("cost_amount", 0.11);
        printerData.put("discount_amount", 0);
        printerData.put("goods_total_number", "1");
        printerData.put("recipient", recipient);
        printerData.put("shop_phone", "18516011018");
        printerData.put("note", "依据餐量提供餐具");
        printerData.put("goods", new JSONObject[]{good});
        printerData.put("cost", new JSONObject[]{cost1, cost2});
        printerData.put("discount", new JSONObject[]{});
        printerData.put("is_new_customer", "");
        printerData.put("greeting", "祝福语测试");
        printerData.put("buyer", "购买人");
        printerData.put("show_deliver_time", "1");
        printerData.put("print_setting", printSetting);
        printerData.put("sticker_type", "商家联");
        printerData.put("page_width", "58");
        printerData.put("template_id", "195109");
        printerData.put("tid", "80417387710645916817266137451");

        requestBody.put("printer_datas", new JSONObject[]{printerData});

        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        System.out.println(" 拼装data:" + json);
        bodys.put("data", json);
        log.info("bodys = {}", JSON.toJSONString(bodys));
        try {
            String requestMethod = "POST";
            Map<String, String> headers = new HashMap<>();
            Map<String, String> querys = new HashMap<>();
            // 根据API的要求，定义相对应的Content-Type
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            HttpResponse response = HttpUtils.doPost(host, path, requestMethod, headers,
                    querys, bodys);
            System.out.println(response.toString());
            //获取response的body
            String result = EntityUtils.toString(response.getEntity());
            System.out.println("JuDanKeOrderUtils doHttpRequest result=" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试预约时间设置到 reserve_arrival_time 字段
     */
    @Test
    public void testScheduledTimeToReserveArrivalTime() throws Exception {
        System.out.println("=== 测试预约时间设置 ===");

        // 创建测试订单
        TpmOrder testOrder = new TpmOrder();
        testOrder.setOrderSn("TEST" + System.currentTimeMillis());
        testOrder.setConsignee("测试用户");
        testOrder.setMobile("13800138000");
        testOrder.setFreightType((byte) 0);
        testOrder.setScheduledTime("20:30"); // 设置预约时间

        // 创建测试商品列表
        List<TpmOrderGoods> goodsList = new ArrayList<>();
        TpmOrderGoods goods = new TpmOrderGoods();
        goods.setGoodsName("测试商品");
        goods.setNumber((short) 1);
        goods.setPrice(new BigDecimal("50.00"));
        goodsList.add(goods);

        // 生成订单数据
        String orderData = getPreCreateOrderData(testOrder, goodsList, "SHOP001", "USER001");
        System.out.println("生成的订单数据: " + orderData);

        // 解析验证
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(orderData);
        String reserveArrivalTime = jsonObject.getString("reserve_arrival_time");
        Integer booking = jsonObject.getInteger("booking");

        System.out.println("预约时间输入: " + testOrder.getScheduledTime());
        System.out.println("预约到达时间: " + reserveArrivalTime);
        System.out.println("预约标识: " + booking);

        // 验证格式
        String expectedDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String expectedTime = expectedDate + " 20:30:00";

        assert booking != null && booking == 1 : "预约标识应该为1";
        assert expectedTime.equals(reserveArrivalTime) : "预约时间格式不正确";

        System.out.println("✓ 预约时间设置测试通过");
    }

    @Test
    public void testPrintNotice() throws JsonProcessingException {
        String host = "https://jop.judanke.cn";
        String appId = "10501";
        String appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
        String relativeUserId = "340986925";
        String path = "/Printer/prints";
        Map<String, String> bodys = getHttpBodys(path, appId, appKey);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("guid", "PTC-ZW-12503110");
        requestBody.put("brand", "ZW");
        requestBody.put("relative_user_id", relativeUserId);

        JSONObject printerData = new JSONObject();
        printerData.put("template_id", "195123");
        printerData.put("tid", "80417387710645916817266137451");

        requestBody.put("printer_datas", new JSONObject[]{printerData});

        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(requestBody);
        System.out.println(" 拼装data:" + json);
        bodys.put("data", json);
        log.info("bodys = {}", JSON.toJSONString(bodys));
        try {
            String requestMethod = "POST";
            Map<String, String> headers = new HashMap<>();
            Map<String, String> querys = new HashMap<>();
            // 根据API的要求，定义相对应的Content-Type
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            HttpResponse response = HttpUtils.doPost(host, path, requestMethod, headers,
                    querys, bodys);
            System.out.println(response.toString());
            //获取response的body
            String result = EntityUtils.toString(response.getEntity());
            System.out.println("JuDanKeOrderUtils doHttpRequest result=" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetBalance() {
        String host = "https://jop.judanke.cn";
        String appId = "10501";
        String appKey = "d1748f6a645a542e9aac47437d3a008e37db989c";
        String path = "/Finance/getBalance";
        try {
            // 构建通知消息
            StringBuilder message = new StringBuilder("聚单客账户余额查询结果：\n");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            message.append("查询时间：").append(LocalDateTime.now().format(formatter)).append("\n\n");

            Map<String, String> bodys = JuDanKeOrderUtils.getHttpBodys(path, appId, appKey);
            bodys.put("data","{}");
            String res = JuDanKeOrderUtils.doHttpRequestPost(host, path, bodys);
            log.info("queryBalance result={}", res);
            JdkCommonResDto resDto = JSON.parseObject(res, JdkCommonResDto.class);
            // 解析余额查询结果
            if (resDto != null && resDto.getCode() == 0 && !org.springframework.util.StringUtils.isEmpty(resDto.getData())) {
                JSONObject balanceData = JSON.parseObject(resDto.getData());

                // 提取余额信息
                if (balanceData.containsKey("total_avail")) {
                    // 假设余额是以分为单位的整数
                    Integer balanceInCents = balanceData.getInteger("total_avail");
                    BigDecimal balance = new BigDecimal(balanceInCents).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

                    message.append("余额: ").append(balance).append("元\n");

                    // 如果余额低于阈值，添加警告信息
                    BigDecimal threshold = new BigDecimal(100); // 设置100元为警戒线
                    if (balance.compareTo(threshold) < 0) {
                        message.append(" 余额不足，请及时充值！\n");
                    }
                } else {
                    message.append("余额查询成功，但返回数据格式异常\n");
                }
            } else {
                message.append("查询失败: ").append(resDto != null ? resDto.getMsg() : "未知错误").append("\n");
            }
            System.out.printf(message.toString());
        } catch (Exception e) {
            log.error("查询聚单客账户余额异常 error:{}", e.getMessage(), e);
            throw new RuntimeException("查询聚单客账户余额异常: " + e.getMessage());
        }
    }
}
