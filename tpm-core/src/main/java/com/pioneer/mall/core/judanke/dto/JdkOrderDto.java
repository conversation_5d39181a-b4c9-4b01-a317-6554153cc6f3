package com.pioneer.mall.core.judanke.dto;

import lombok.Data;

@Data
public class JdkOrderDto {

    private String shop_id;
    private String relative_user_id;
    private Integer goods_type_id;
    private String third_platform;
    private Integer booking;
    private String reserve_arrival_time;
    private String pickup_time;
    private String third_order_no;
    private String receiver_name;
    private String receiver_phone;
    private String receiver_lng;
    private String receiver_lat;
    private Integer goods_price;
    private Integer goods_weight;
    private Integer goods_quantity;
    private String receiver_address;
    private JdkSourceOrderDataDto source_order_data;
}
