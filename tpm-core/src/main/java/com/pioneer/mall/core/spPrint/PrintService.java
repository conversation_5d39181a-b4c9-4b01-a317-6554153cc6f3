package com.pioneer.mall.core.spPrint;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.pioneer.mall.core.consts.CommConsts;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.vo.SpApi;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.service.TpmGoodsService;
import com.pioneer.mall.db.service.TpmOrderGoodsService;
import com.pioneer.mall.db.service.TpmOrderService;
import com.pioneer.mall.db.util.SpecificationUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PrintService {

    @Autowired
    private TpmOrderGoodsService orderGoodsService;
    @Autowired
    private TpmOrderService orderService;

    @Value("${sp.appid:}")
    private String spAppId;
    @Value("${sp.appsecret:}")
    private String spAppSecret;
    @Value("${sp.printersn:}")
    private String spPrinterSn;

    private static final String split = "  ";
    private static final String NAME_SPEC_PLACEHOLDER = "           ";
    private static final String _SEPARATOR_ = "_SEPARATOR_";

    public Object mallPrint(List<Integer> orderIds, List<String> printTypes) {

        List<TpmOrder> orderList = orderService.findById(orderIds);
        List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOids(orderIds);
        // 后厨联
        if (printTypes.contains(CommConsts.ORDER_KITCHEN)) {
            log.info("打印备货单");
            String messageJoin = orderList.stream().map(z -> {
                return "<BR>订单号：" + z.getOrderSn() + "，" + z.getMessage();
            }).collect(Collectors.joining(","));
            this.printKitchen(orderGoodsList, messageJoin);
        }
        // 发货联
        if (printTypes.contains(CommConsts.ORDER_DELIVERY)) {
            log.info("打印发货单");
            // 打印发货数据
            for (TpmOrder tpmOrder : orderList) {
                List<TpmOrderGoods> goodsList = orderGoodsList.stream()
                        .filter(z -> z.getOrderId().equals(tpmOrder.getId()))
                        .collect(Collectors.toList());
                if (Objects.isNull(goodsList)) {
                    continue;
                }
                this.printDelivery(tpmOrder, goodsList);
            }
        }

        return ResponseUtil.ok();
    }


    public void printKitchen(List<TpmOrderGoods> orderGoodsList, String message) {

        // 拼接内容
        // 拼接内容
        StringBuilder builder = new StringBuilder("<C><L1>备货单</L1></C>");
        builder.append("<BR>");
        builder.append("<BR>打印时间：").append(DateUtil.date());
        builder.append("<BR>");
        builder.append("<BR><B>名称　　　　　规格　　　　　数量</B>");
        orderGoodsList.stream().collect(Collectors.groupingBy(k -> {
            String specificationJoin = SpecificationUtil.getSpecificationsValueJoinByAttributes(k.getSpecifications());
            specificationJoin = StringUtils.isEmpty(specificationJoin) ? "无规格" : specificationJoin;
            return "<BR><BR>" + k.getGoodsName() + split + specificationJoin + split;
        }, Collectors.summingInt(TpmOrderGoods::getNumber))).forEach((k, v) -> {
            builder.append(k).append("x").append(v);
        });
        // 备货单的备注
        builder.append("<BR><BR>备注：");
        if (!StringUtils.isEmpty(message)) {
            builder.append("<BR><H>").append(message).append("</H>");
        }

        builder.append("<BR>");
        builder.append("<BR>");
        builder.append("<BR>=============裁剪线=============");
        builder.append("<BR>");
        this.print(builder.toString());
    }

    public void printDelivery(TpmOrder order, List<TpmOrderGoods> orderGoodsList) {
        // 拼接内容
        StringBuilder builder = new StringBuilder("<C><L1>发货单</L1></C>");
        builder.append("<BR>");
        builder.append("<BR>姓名：").append(order.getConsignee());
        builder.append("<BR>手机号：").append(order.getMobile());
        builder.append("<BR>收件地址：").append(order.getAddress());
        builder.append("<BR>订单编号：").append(order.getOrderSn());
        builder.append("<BR>");
        builder.append("<BR><B>名称　　　　　规格　　　　　数量</B>");
        builder.append("<BR>");
        for (TpmOrderGoods orderGoods : orderGoodsList) {
            builder.append("<BR>").append(orderGoods.getGoodsName()).append(split);
            String specificationJoin = SpecificationUtil.getSpecificationsValueJoinByAttributes(orderGoods.getSpecifications());
            specificationJoin = StringUtils.isEmpty(specificationJoin) ? "无规格" : specificationJoin;
            if (!StringUtils.isEmpty(specificationJoin)) {
                builder.append(specificationJoin).append(split);
            }
            builder.append("x").append(orderGoods.getNumber());
            builder.append("<BR>");
        }
        builder.append("<BR>备注：");
        if (!StringUtils.isEmpty(order.getMessage())) {
            builder.append("<BR><H>").append(order.getMessage()).append("</H>");
        }
        builder.append("<BR>");
        builder.append("<BR>");
        builder.append("<QRCODE>").append(order.getMealQrCode()).append("</QRCODE>");
        builder.append("<BR>");
        builder.append("<BR>=============裁剪线=============");
        builder.append("<BR>");
        this.print(builder.toString());
    }

    private StringBuilder printGoodsDetail(String goodsName, String specificationJoin, Integer number) {

        TpmOrderGoods orderGoods = new TpmOrderGoods();
        orderGoods.setGoodsName(goodsName);
        orderGoods.setSpecifications(specificationJoin);
        orderGoods.setNumber(Short.valueOf(number + ""));
        return printGoodsDetail(orderGoods);
    }

    private StringBuilder printGoodsDetail(TpmOrderGoods orderGoods) {
        StringBuilder builder = new StringBuilder();
        List<String> goodNameList = splitStringBySixRegex(orderGoods.getGoodsName());
        List<String> specificationJoinList = splitStringBySixRegex(SpecificationUtil.getSpecificationsValueJoinByAttributes(orderGoods.getSpecifications()));
        int goodNameListSize = goodNameList.size();
        int specificationJoinListSize = specificationJoinList.size();
        // 商品名称一定会有，所有以商品名称做循环
        Boolean addNum = false;
        for (int i = 1; i <= goodNameListSize; i++) {
            int index = i - 1;
            String goodName = goodNameList.get(index);
            // 商品名称小于等于属性
            if (i <= specificationJoinListSize) {
                builder.append("<BR>").append(goodName).append(split).append(specificationJoinList.get(index)).append(split);
                if (!addNum) {
                    addNum = true;
                    builder.append("x").append(orderGoods.getNumber());
                }
            } else {
                // 商品名称大于属性
                builder.append("<BR>").append(goodName).append(split).append(NAME_SPEC_PLACEHOLDER).append(split);
                if (!addNum) {
                    addNum = true;
                    builder.append("x").append(orderGoods.getNumber());
                }
            }
        }
        // 如果属性的长度大于商品
        if (specificationJoinListSize > goodNameListSize) {

            for (int i = goodNameListSize; i < specificationJoinListSize; i++) {
                int index = i;
                builder.append("<BR>").append(NAME_SPEC_PLACEHOLDER).append(split).append(specificationJoinList.get(index)).append(split);
                if (!addNum) {
                    addNum = true;
                    builder.append("x").append(orderGoods.getNumber());
                }
            }
        }
        builder.append("<BR>");
        return builder;
    }

    public void print(String content) {
        log.info("远程打印任务发送开始，内容：{}", content);

        if (StringUtils.isEmpty(spAppId) || StringUtils.isEmpty(spAppSecret) || StringUtils.isEmpty(spPrinterSn)) {
            log.error("远程打印任务发送失败，spAppId、spAppSecret、spPrinterSn不能为空");
            return;
        }

        SpApi api = new SpApi(spAppId, spAppSecret);
        try {
            api.print(spPrinterSn, content, 1);
        } catch (IOException e) {
            log.error("远程打印任务发送失败,message={}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
        log.info("远程打印任务发送完成");

    }

    @Test
    public void test() {
        TpmOrder order = new TpmOrder();
        order.setConsignee("张三");
        order.setAddress("广东省深圳市南山区");
        order.setOrderSn("123456789");
        order.setMealQrCode("24b884d7bb184b2eb6f3156a00d0a3b9");
        order.setMessage("加急送");
        List<TpmOrderGoods> orderGoodsList = new ArrayList();
        for (int i = 0; i < 1; i++) {
            TpmOrderGoods orderGoods = new TpmOrderGoods();
            orderGoods.setGoodsName("小糕点" + i);
            orderGoods.setNumber(new Short("10"));
            orderGoods.setSpecifications("[{\"goodsId\":66,\"goodsSpecifications\":[{\"enable\":true,\"id\":419,\"price\":\"10.00\",\"selected\":true,\"value\":\"多冰多冰多冰\"},{\"enable\":true,\"id\":420,\"price\":\"0.00\",\"selected\":false,\"value\":\"少冰\"},{\"enable\":true,\"id\":421,\"price\":\"0.00\",\"selected\":false,\"value\":\"去冰\"},{\"enable\":true,\"id\":422,\"price\":\"0.01\",\"selected\":false,\"value\":\"无冰\"},{\"enable\":true,\"id\":423,\"price\":\"0.00\",\"selected\":false,\"value\":\"热\"}],\"id\":42,\"required\":true,\"value\":\"冰量\"},{\"goodsId\":66,\"goodsSpecifications\":[{\"enable\":true,\"id\":424,\"price\":\"0.01\",\"selected\":false,\"value\":\"大杯\"},{\"enable\":true,\"id\":425,\"price\":\"10.00\",\"selected\":true,\"value\":\"中杯\"},{\"enable\":true,\"id\":426,\"price\":\"0.02\",\"selected\":false,\"value\":\"小杯\"}],\"id\":43,\"required\":true,\"value\":\"尺寸\"}]");
            orderGoodsList.add(orderGoods);
        }

        TpmOrderGoods orderGoods = new TpmOrderGoods();
        orderGoods.setGoodsName("小糕点小糕点小糕点小糕点小糕点小糕点" + 0);
        orderGoods.setNumber(new Short("10"));
        orderGoods.setSpecifications("[{\"goodsId\":66,\"goodsSpecifications\":[{\"enable\":true,\"id\":419,\"price\":\"10.00\",\"selected\":true,\"value\":\"多冰\"},{\"enable\":true,\"id\":420,\"price\":\"0.00\",\"selected\":false,\"value\":\"少冰\"},{\"enable\":true,\"id\":421,\"price\":\"0.00\",\"selected\":false,\"value\":\"去冰\"},{\"enable\":true,\"id\":422,\"price\":\"0.01\",\"selected\":false,\"value\":\"无冰\"},{\"enable\":true,\"id\":423,\"price\":\"0.00\",\"selected\":false,\"value\":\"热\"}],\"id\":42,\"required\":true,\"value\":\"冰量\"},{\"goodsId\":66,\"goodsSpecifications\":[{\"enable\":true,\"id\":424,\"price\":\"0.01\",\"selected\":false,\"value\":\"大杯\"},{\"enable\":true,\"id\":425,\"price\":\"10.00\",\"selected\":true,\"value\":\"中杯\"},{\"enable\":true,\"id\":426,\"price\":\"0.02\",\"selected\":false,\"value\":\"小杯\"}],\"id\":43,\"required\":true,\"value\":\"尺寸\"}]");
        orderGoodsList.add(orderGoods);

        TpmOrderGoods orderGoods1 = new TpmOrderGoods();
        orderGoods1.setGoodsName("小糕点小糕点小糕点小糕点小糕点小糕点" + 0);
        orderGoods1.setNumber(new Short("10"));
//        orderGoods1.setSpecifications("[{\"goodsId\":66,\"goodsSpecifications\":[{\"enable\":true,\"id\":419,\"price\":\"10.00\",\"selected\":true,\"value\":\"多冰\"},{\"enable\":true,\"id\":420,\"price\":\"0.00\",\"selected\":false,\"value\":\"少冰\"},{\"enable\":true,\"id\":421,\"price\":\"0.00\",\"selected\":false,\"value\":\"去冰\"},{\"enable\":true,\"id\":422,\"price\":\"0.01\",\"selected\":false,\"value\":\"无冰\"},{\"enable\":true,\"id\":423,\"price\":\"0.00\",\"selected\":false,\"value\":\"热\"}],\"id\":42,\"required\":true,\"value\":\"冰量\"},{\"goodsId\":66,\"goodsSpecifications\":[{\"enable\":true,\"id\":424,\"price\":\"0.01\",\"selected\":false,\"value\":\"大杯\"},{\"enable\":true,\"id\":425,\"price\":\"10.00\",\"selected\":true,\"value\":\"中杯\"},{\"enable\":true,\"id\":426,\"price\":\"0.02\",\"selected\":false,\"value\":\"小杯\"}],\"id\":43,\"required\":true,\"value\":\"尺寸\"}]");
        orderGoodsList.add(orderGoods1);


//        printKitchen(orderGoodsList, null);
        printDelivery(order, orderGoodsList);

    }

    public static List<String> splitStringBySixRegex(String input) {
        if (StringUtils.isEmpty(input)) {
            return new ArrayList<>();
        }
        // 定义正则表达式
        Pattern pattern = Pattern.compile(".{1,6}");
        Matcher matcher = pattern.matcher(input);
        List<String> list = new java.util.ArrayList<>();
        while (matcher.find()) {
            list.add(matcher.group());
        }
        return list.stream().map(s -> {
            int length = s.length();
            if (length < 6) {
                int index = 6 - length;
                String str = "";
                for (int i = 0; i < index; i++) {
                    str += "  ";
                }
                return s + str;
            } else {
                return s;
            }
        }).collect(Collectors.toList());
    }

    public static void main(String[] args) {

        List<String> result = splitStringBySixRegex("名称名称DD名称名SSSSSS");

        System.out.println(JSON.toJSONString(result));


//        String content = "<C>\n" +
//                "    <L1>远程测试打印</L1>\n" +
//                "</C>\n" +
////                "<BR>名称　　　　　 单价 数量 金额\n" +
////                "<BR>炒饭　　　　　 10.0 ×10 10\n" +
////                "<BR>蛋炒饭　　　　 10.0 ×10 100\n" +
////                "<BR>西红柿蛋炒饭　 100.0 ×10 100\n" +
////                "<BR>西红柿鸡蛋炒饭西红柿鸡蛋炒饭西红柿鸡蛋炒饭西红柿鸡蛋炒饭西红柿鸡蛋炒饭西红柿鸡蛋炒饭 15.0 ×1 15\n" +
////                "<BR>备注：\n" +
////                "<H>加辣</H>\n" +
////                "<BR>--------------------------------\n" +
////                "<BR>合计：xx.0元\n" +
////                "<BR>亲，满意请给我们5星好评哦\n" +
////                "<BR>送货地点：广州市南沙区xx路xx号\n" +
////                "<BR>\n" +
////                "<BR>\n" +
//                "<BR><B>    名称　　　　　规格　　数量</B>" +
//                "<BR>名称名称名称  规格规格规格  *10" +
//
//
//                "<QRCODE>http://www.spyun.net/</QRCODE>\n" +
//                "<C>\n" +
//                "\t<BC128_C>0123456789</BC128_C>\n" +
//                "</C>";
//        SpApi api = new SpApi("sp67b5dc3fec604", "f050b21f95efd636745f77986609dd09");
//        try {
//            api.print("1929124091", content, 1);
//        } catch (IOException e) {
//            log.error("打印失败:{}", e.getMessage(), e);
//            throw new RuntimeException(e);
//        }
    }
}
