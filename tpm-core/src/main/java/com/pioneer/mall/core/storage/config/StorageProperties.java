package com.pioneer.mall.core.storage.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "dts.storage")
public class StorageProperties {
	private String active;
	private Local local;
	private <PERSON><PERSON> aliyun;
	private Tencent tencent;
	private <PERSON>iu qiniu;

	public String getActive() {
		return active;
	}

	public void setActive(String active) {
		this.active = active;
	}

	public Local getLocal() {
		return local;
	}

	public void setLocal(Local local) {
		this.local = local;
	}

	public Aliyun getAliyun() {
		return aliyun;
	}

	public void setAliyun(<PERSON><PERSON> aliyun) {
		this.aliyun = aliyun;
	}

	public Tencent getTencent() {
		return tencent;
	}

	public void setTencent(Tencent tencent) {
		this.tencent = tencent;
	}

	public Qiniu getQiniu() {
		return qiniu;
	}

	public void setQiniu(Qiniu qiniu) {
		this.qiniu = qiniu;
	}

	public static class Local {
		private String address;
		private String storagePath;

		public String getAddress() {
			return address;
		}

		public void setAddress(String address) {
			this.address = address;
		}

		public String getStoragePath() {
			return storagePath;
		}

		public void setStoragePath(String storagePath) {
			this.storagePath = storagePath;
		}
	}

	public static class Tencent {
		private String secretId;
		private String secretKey;
		private String region;
		private String bucketName;

		public String getSecretId() {
			return secretId;
		}

		public void setSecretId(String secretId) {
			this.secretId = secretId;
		}

		public String getSecretKey() {
			return secretKey;
		}

		public void setSecretKey(String secretKey) {
			this.secretKey = secretKey;
		}

		public String getRegion() {
			return region;
		}

		public void setRegion(String region) {
			this.region = region;
		}

		public String getBucketName() {
			return bucketName;
		}

		public void setBucketName(String bucketName) {
			this.bucketName = bucketName;
		}
	}

	public static class Aliyun {
		private String endpoint;
		private String accessKeyId;
		private String accessKeySecret;
		private String bucketName;

		public String getEndpoint() {
			return endpoint;
		}

		public void setEndpoint(String endpoint) {
			this.endpoint = endpoint;
		}

		public String getAccessKeyId() {
			return accessKeyId;
		}

		public void setAccessKeyId(String accessKeyId) {
			this.accessKeyId = accessKeyId;
		}

		public String getAccessKeySecret() {
			return accessKeySecret;
		}

		public void setAccessKeySecret(String accessKeySecret) {
			this.accessKeySecret = accessKeySecret;
		}

		public String getBucketName() {
			return bucketName;
		}

		public void setBucketName(String bucketName) {
			this.bucketName = bucketName;
		}
	}

	public static class Qiniu {
		private String endpoint;
		private String accessKey;
		private String secretKey;
		private String bucketName;

		public String getEndpoint() {
			return endpoint;
		}

		public void setEndpoint(String endpoint) {
			this.endpoint = endpoint;
		}

		public String getAccessKey() {
			return accessKey;
		}

		public void setAccessKey(String accessKey) {
			this.accessKey = accessKey;
		}

		public String getSecretKey() {
			return secretKey;
		}

		public void setSecretKey(String secretKey) {
			this.secretKey = secretKey;
		}

		public String getBucketName() {
			return bucketName;
		}

		public void setBucketName(String bucketName) {
			this.bucketName = bucketName;
		}
	}
}
