package com.pioneer.mall.core.system;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 配置基类，该类实际持有所有的配置，子类只是提供代理访问方法
 */
abstract class BaseConfig {

	// 所有的配置均保存在该 HashMap 中，支持shopId维度
	// key格式：对于全局配置直接使用keyName，对于店铺配置使用 keyName + ":" + shopId
	protected static Map<String, String> configs = new HashMap<>();

	/**
	 * 添加配置到公共Map中
	 *
	 * @param key
	 * @param value
	 */
	public static void addConfig(String key, String value) {
		configs.put(key, value);
	}

	/**
	 * 添加配置到公共Map中，支持shopId维度
	 *
	 * @param key
	 * @param value
	 * @param shopId 店铺ID，为null时表示全局配置
	 */
	public static void addConfig(String key, String value, Integer shopId) {
		String configKey = buildConfigKey(key, shopId);
		configs.put(configKey, value);
	}

	/**
	 * 重载配置,传入子类的prefix
	 */
	public static void reloadConfig(String prefix) {
		// 先遍历删除该 prefix 所有配置
		for (Iterator<Map.Entry<String, String>> it = configs.entrySet().iterator(); it.hasNext();) {
			Map.Entry<String, String> item = it.next();
			String configKey = item.getKey();
			// 检查是否匹配prefix（考虑shopId后缀的情况）
			if (configKey.startsWith(prefix) || (configKey.contains(":") && configKey.substring(0, configKey.indexOf(":")).startsWith(prefix))) {
				it.remove();
			}
		}

		ConfigService.getSystemConfigService().reloadConfig(prefix);
	}

	/**
	 * 重载配置,传入子类的prefix和shopId
	 */
	public static void reloadConfig(String prefix, Integer shopId) {
		// 先遍历删除该 prefix 和 shopId 的所有配置
		for (Iterator<Map.Entry<String, String>> it = configs.entrySet().iterator(); it.hasNext();) {
			Map.Entry<String, String> item = it.next();
			String configKey = item.getKey();
			// 检查是否匹配prefix和shopId
			if (shopId == null) {
				// 删除全局配置
				if (configKey.startsWith(prefix) && !configKey.contains(":")) {
					it.remove();
				}
			} else {
				// 删除特定shopId的配置
				String expectedKey = prefix;
				String expectedSuffix = ":" + shopId;
				if (configKey.startsWith(expectedKey) && configKey.endsWith(expectedSuffix)) {
					it.remove();
				}
			}
		}

		ConfigService.getSystemConfigService().reloadConfig(prefix, shopId);
	}

	/**
	 * 按String类型获取配置值
	 *
	 * @param keyName
	 * @return
	 */
	protected static String getConfig(String keyName) {
		return configs.get(keyName);
	}

	/**
	 * 按String类型获取配置值，支持shopId维度
	 * 优先查找shopId对应的配置，如果不存在则查找全局配置
	 *
	 * @param keyName
	 * @param shopId 店铺ID，为null时只查找全局配置
	 * @return
	 */
	protected static String getConfig(String keyName, Integer shopId) {
		if (shopId != null) {
			// 先尝试获取店铺特定配置
			String shopConfigKey = buildConfigKey(keyName, shopId);
			String shopConfig = configs.get(shopConfigKey);
			if (shopConfig != null) {
				return shopConfig;
			}
		}
		// 如果没有店铺特定配置，则返回全局配置
		return configs.get(keyName);
	}

	/**
	 * 以Integer类型获取配置值
	 *
	 * @param keyName
	 * @return
	 */
	protected static Integer getConfigInt(String keyName) {
		String value = configs.get(keyName);
		return value != null ? Integer.parseInt(value) : null;
	}

	/**
	 * 以Integer类型获取配置值，支持shopId维度
	 *
	 * @param keyName
	 * @param shopId 店铺ID，为null时只查找全局配置
	 * @return
	 */
	protected static Integer getConfigInt(String keyName, Integer shopId) {
		String value = getConfig(keyName, shopId);
		return value != null ? Integer.parseInt(value) : null;
	}

	/**
	 * 以BigDecimal类型获取配置值
	 *
	 * @param keyName
	 * @return
	 */
	protected static BigDecimal getConfigBigDec(String keyName) {
		String value = configs.get(keyName);
		return value != null ? new BigDecimal(value) : null;
	}

	/**
	 * 以BigDecimal类型获取配置值，支持shopId维度
	 *
	 * @param keyName
	 * @param shopId 店铺ID，为null时只查找全局配置
	 * @return
	 */
	protected static BigDecimal getConfigBigDec(String keyName, Integer shopId) {
		String value = getConfig(keyName, shopId);
		return value != null ? new BigDecimal(value) : null;
	}

	/**
	 * 构建配置键，支持shopId维度
	 *
	 * @param keyName
	 * @param shopId 店铺ID，为null时返回原始keyName
	 * @return
	 */
	private static String buildConfigKey(String keyName, Integer shopId) {
		if (shopId == null) {
			return keyName;
		}
		return keyName + ":" + shopId;
	}

	/**
	 * 子类实现该方法，并告知父类配置前缀，该前缀用来索引配置组用于简化访问和按组重读配置
	 *
	 * @return
	 */
	abstract String getPrefix();
}
