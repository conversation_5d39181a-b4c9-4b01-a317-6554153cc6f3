package com.pioneer.mall.core.system;

import com.pioneer.mall.db.domain.TpmSystem;
import com.pioneer.mall.db.service.TpmSystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 该类用于自动初始化数据库配置到BaseConfig中，以便BaseConfig的子类调用
 */
@Component
class ConfigService {
	private static ConfigService systemConfigService;
	@Autowired
	private TpmSystemConfigService tpmSystemConfigService;

	// 不允许实例化
	private ConfigService() {

	}

	static ConfigService getSystemConfigService() {
		return systemConfigService;
	}

	@PostConstruct
	public void inist() {
		systemConfigService = this;
		systemConfigService.inistConfigs();
	}

	/**
	 * 根据 prefix 重置该 prefix 下所有设置
	 *
	 * @param prefix
	 */
	public void reloadConfig(String prefix) {
		List<TpmSystem> list = tpmSystemConfigService.queryAll();
		for (TpmSystem item : list) {
			// 符合条件，添加
			if (item.getKeyName().startsWith(prefix)) {
				BaseConfig.addConfig(item.getKeyName(), item.getKeyValue(), item.getShopId());
			}
		}
	}

	/**
	 * 根据 prefix 和 shopId 重置该 prefix 下所有设置
	 *
	 * @param prefix
	 * @param shopId 店铺ID，为null时只加载全局配置
	 */
	public void reloadConfig(String prefix, Integer shopId) {
		List<TpmSystem> list = tpmSystemConfigService.queryAll();
		for (TpmSystem item : list) {
			// 符合条件，添加
			if (item.getKeyName().startsWith(prefix)) {
				// 如果指定了shopId，只加载对应shopId的配置和全局配置
				if (shopId == null) {
					// 只加载全局配置
					if (item.getShopId() == null) {
						BaseConfig.addConfig(item.getKeyName(), item.getKeyValue(), item.getShopId());
					}
				} else {
					// 加载指定shopId的配置和全局配置
					if (item.getShopId() == null || shopId.equals(item.getShopId())) {
						BaseConfig.addConfig(item.getKeyName(), item.getKeyValue(), item.getShopId());
					}
				}
			}
		}
	}

	/**
	 * 读取全部配置
	 */
	private void inistConfigs() {
		List<TpmSystem> list = tpmSystemConfigService.queryAll();
		for (TpmSystem item : list) {
			BaseConfig.addConfig(item.getKeyName(), item.getKeyValue(), item.getShopId());
		}
	}
}