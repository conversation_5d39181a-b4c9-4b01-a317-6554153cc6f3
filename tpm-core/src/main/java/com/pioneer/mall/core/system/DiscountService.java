package com.pioneer.mall.core.system;

import com.alibaba.fastjson.JSON;
import com.pioneer.mall.core.config.CodeDiscountConfigDTO;
import com.pioneer.mall.core.config.EveningDiscountConfigDTO;
import com.pioneer.mall.db.domain.TpmCouponCode;
import com.pioneer.mall.db.service.TpmCouponCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 折扣服务
 * @date 2024/12/3 15:16
 */
@Slf4j
@Service
public class DiscountService {
    @Autowired
    private TpmCouponCodeService tpmCouponCodeService;

    public CodeDiscountConfigDTO getCouponCodeDiscount(String code) {
        synchronized (this) {
            TpmCouponCode tpmCouponCode = tpmCouponCodeService.findByCode(code);
            if (Objects.isNull(tpmCouponCode)) {
                throw new RuntimeException("优惠口令不存在");
            }
            Boolean enable = tpmCouponCode.getEnable();
            if (!enable) {
                throw new RuntimeException("优惠口令已失效");
            }
            LocalDateTime validFrom = tpmCouponCode.getValidFrom();
            LocalDateTime validTo = tpmCouponCode.getValidTo();
            if (Objects.isNull(validFrom) || Objects.isNull(validTo)) {
                throw new RuntimeException("优惠口令未设置有效期");
            }
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(validFrom) || now.isAfter(validTo)) {
                throw new RuntimeException("优惠口令已过期");
            }
            Integer remainingTimes = tpmCouponCode.getRemainingTimes();
            if (remainingTimes <= 0) {
                throw new RuntimeException("优惠口令已用完");
            }
            BigDecimal discount = tpmCouponCode.getDiscount();
            if (Objects.isNull(discount)) {
                discount = BigDecimal.ONE;
            }

            CodeDiscountConfigDTO codeDiscountConfigDTO = new CodeDiscountConfigDTO();
            codeDiscountConfigDTO.setCouponCodeId(tpmCouponCode.getId());
            codeDiscountConfigDTO.setEnable(true);
            codeDiscountConfigDTO.setDiscount(discount);
            codeDiscountConfigDTO.setEffective(true);
            return codeDiscountConfigDTO;
        }
    }


    public EveningDiscountConfigDTO getCurrentEveningDiscount(Integer shopId) {
        EveningDiscountConfigDTO notEffectiveDTO = new EveningDiscountConfigDTO();
        notEffectiveDTO.setEffective(false);
        notEffectiveDTO.setDiscount(BigDecimal.ONE);
        try {
            String config = SystemConfig.getEveningDiscountConfig(shopId);
            EveningDiscountConfigDTO eveningDiscountConfigDTO = JSON.parseObject(config, EveningDiscountConfigDTO.class);
            if (Objects.isNull(eveningDiscountConfigDTO)) {
                return notEffectiveDTO;
            }
            //先统一设置为false
            eveningDiscountConfigDTO.setEffective(false);
            Boolean enable = eveningDiscountConfigDTO.getEnable();
            if (Objects.isNull(enable) || !enable) {
                return eveningDiscountConfigDTO;
            }
            if (Objects.isNull(eveningDiscountConfigDTO.getDiscount())) {
                return eveningDiscountConfigDTO;
            }
            LocalTime startTime = eveningDiscountConfigDTO.getStartTime();
            LocalTime endTime = eveningDiscountConfigDTO.getEndTime();
            LocalTime currentTime = LocalTime.now();
            // 判断当前时间是否在启用时间（晚上八点）之后且在营业时间结束前，这里简单假设营业时间到23:59结束
            if (currentTime.isAfter(startTime) && currentTime.isBefore(endTime)) {
                eveningDiscountConfigDTO.setEffective(true);
            }
            return eveningDiscountConfigDTO;
        } catch (Exception e) {
            log.error("获取当前晚上折扣配置失败：" + e.getMessage(), e);
        }
        return notEffectiveDTO;
    }

    public static void main(String[] args) {
        EveningDiscountConfigDTO eveningDiscountConfigDTO = new EveningDiscountConfigDTO();
        eveningDiscountConfigDTO.setEnable(true);
        eveningDiscountConfigDTO.setDiscount(BigDecimal.valueOf(0.8));
        eveningDiscountConfigDTO.setStartTime(LocalTime.of(20, 00));
        eveningDiscountConfigDTO.setEndTime(LocalTime.of(23, 59));
        eveningDiscountConfigDTO.setExcludeCategory(Arrays.asList(111));
        System.out.println(JSON.toJSONString(eveningDiscountConfigDTO));
    }
}
