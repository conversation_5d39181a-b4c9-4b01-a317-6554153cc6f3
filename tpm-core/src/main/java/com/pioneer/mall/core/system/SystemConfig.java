package com.pioneer.mall.core.system;

import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 系统设置,其他配置请参考该类的实现
 */
public class SystemConfig extends BaseConfig {
	public static final String PRE_FIX = "dts.system.";

	public static String getWechatNotifyUrl() {
		return getConfig(PRE_FIX + "wechat.notify");
	}

	public static String getWechatNotifyUrl(Integer shopId) {
		return getConfig(PRE_FIX + "wechat.notify", shopId);
	}

	public static String getWechatShopNotifyUrl() {
		return getConfig(PRE_FIX + "wechat.notify.shop",1);
	}

	public static String getWechatShopNotifyUrl(Integer shopId) {
		return getConfig(PRE_FIX + "wechat.notify.shop", shopId);
	}

	public static Integer getNewLimit() {
		return getConfigInt(PRE_FIX + "indexlimit.new");
	}

	public static Integer getNewLimit(Integer shopId) {
		return getConfigInt(PRE_FIX + "indexlimit.new", shopId);
	}

	public static Integer getHotLimit() {
		return getConfigInt(PRE_FIX + "indexlimit.hot");
	}

	public static Integer getHotLimit(Integer shopId) {
		return getConfigInt(PRE_FIX + "indexlimit.hot", shopId);
	}

	public static Integer getBrandLimit() {
		return getConfigInt(PRE_FIX + "indexlimit.brand");
	}

	public static Integer getBrandLimit(Integer shopId) {
		return getConfigInt(PRE_FIX + "indexlimit.brand", shopId);
	}

	public static Integer getTopicLimit() {
		return getConfigInt(PRE_FIX + "indexlimit.topic");
	}

	public static Integer getTopicLimit(Integer shopId) {
		return getConfigInt(PRE_FIX + "indexlimit.topic", shopId);
	}

	public static Integer getCatlogListLimit() {
		return getConfigInt(PRE_FIX + "indexlimit.catloglist");
	}

	public static Integer getCatlogListLimit(Integer shopId) {
		return getConfigInt(PRE_FIX + "indexlimit.catloglist", shopId);
	}

	public static Integer getCatlogMoreLimit() {
		return getConfigInt(PRE_FIX + "indexlimit.catloggood");
	}

	public static Integer getCatlogMoreLimit(Integer shopId) {
		return getConfigInt(PRE_FIX + "indexlimit.catloggood", shopId);
	}

	public static String getHotBannerTitle() {
		return getConfig(PRE_FIX + "banner.hot.title");
	}

	public static String getHotBannerTitle(Integer shopId) {
		return getConfig(PRE_FIX + "banner.hot.title", shopId);
	}

	public static String getNewBannerTitle() {
		return getConfig(PRE_FIX + "banner.new.title");
	}

	public static String getNewBannerTitle(Integer shopId) {
		return getConfig(PRE_FIX + "banner.new.title", shopId);
	}

	public static String getHotImageUrl() {
		return getConfig(PRE_FIX + "banner.hot.imageurl");
	}

	public static String getHotImageUrl(Integer shopId) {
		return getConfig(PRE_FIX + "banner.hot.imageurl", shopId);
	}

	public static String getNewImageUrl() {
		return getConfig(PRE_FIX + "banner.new.imageurl");
	}

	public static String getNewImageUrl(Integer shopId) {
		return getConfig(PRE_FIX + "banner.new.imageurl", shopId);
	}

	public static BigDecimal getFreight() {
		return getConfigBigDec(PRE_FIX + "freight.value");
	}

	public static BigDecimal getFreight(Integer shopId) {
		return getConfigBigDec(PRE_FIX + "freight.value", shopId);
	}

	public static BigDecimal getFreightLimit() {
		return getConfigBigDec(PRE_FIX + "freight.limit");
	}

	public static BigDecimal getFreightLimit(Integer shopId) {
		return getConfigBigDec(PRE_FIX + "freight.limit", shopId);
	}

	public static boolean isAutoFreightCalculation() {
		Integer autoCalculate = getConfigInt(PRE_FIX + "fright.autoCalculation");
		return autoCalculate != null && autoCalculate != 0;
	}

	public static boolean isAutoFreightCalculation(Integer shopId) {
		Integer autoCalculate = getConfigInt(PRE_FIX + "fright.autoCalculation", shopId);
		return autoCalculate != null && autoCalculate != 0;
	}

	public static String getMallName() {
		return getConfig(PRE_FIX + "mallname");
	}

	public static String getMallName(Integer shopId) {
		return getConfig(PRE_FIX + "mallname", shopId);
	}

	public static boolean isAutoCreateShareImage() {
		Integer autoCreate = getConfigInt(PRE_FIX + "shareimage.autocreate");
		return autoCreate != null && autoCreate != 0;
	}

	public static boolean isAutoCreateShareImage(Integer shopId) {
		Integer autoCreate = getConfigInt(PRE_FIX + "shareimage.autocreate", shopId);
		return autoCreate != null && autoCreate != 0;
	}

	/**
	 * 是否为多订单模式 dts.system.multi.order.model 1表示是, 0 表示不拆单
	 *
	 * @return
	 */
	public static boolean isMultiOrderModel() {
		Integer multiOrderModel = getConfigInt(PRE_FIX + "multi.order.model");
		return multiOrderModel != null && multiOrderModel != 0;
	}

	/**
	 * 是否为多订单模式 dts.system.multi.order.model 1表示是, 0 表示不拆单
	 *
	 * @param shopId 店铺ID
	 * @return
	 */
	public static boolean isMultiOrderModel(Integer shopId) {
		Integer multiOrderModel = getConfigInt(PRE_FIX + "multi.order.model", shopId);
		return multiOrderModel != null && multiOrderModel != 0;
	}

	//纬度 ==> tpmShopInfoService.getLatitudeByDefaultShopInfo();
	public static String getBinjiangLatitude() {
		return getConfig(PRE_FIX + "shop.binjiang.latitude");
	}

	public static String getBinjiangLatitude(Integer shopId) {
		return getConfig(PRE_FIX + "shop.binjiang.latitude", shopId);
	}

	//经度 ==> tpmShopInfoService.getLongitudeByDefaultShopInfo();
	public static String getBinjiangLongitude() {
		return getConfig(PRE_FIX + "shop.binjiang.longitude");
	}

	public static String getBinjiangLongitude(Integer shopId) {
		return getConfig(PRE_FIX + "shop.binjiang.longitude", shopId);
	}

	public static String getBinjiangShopCode() {
		return getConfig(PRE_FIX + "shop.binjiang.shopCode");
	}

	public static String getBinjiangShopCode(Integer shopId) {
		return getConfig(PRE_FIX + "shop.binjiang.shopCode", shopId);
	}

	//经度
	public static Integer getBinjiangSendType() {
		return getConfigInt(PRE_FIX + "shop.binjiang.sendType");
	}

	public static Integer getBinjiangSendType(Integer shopId) {
		return getConfigInt(PRE_FIX + "shop.binjiang.sendType", shopId);
	}
	//纬度
	public static String getXinChenLatitude() {
		return getConfig(PRE_FIX + "shop.xinchen.latitude");
	}

	//经度
	public static String getXinChenLongitude() {
		return getConfig(PRE_FIX + "shop.xinchen.longitude");
	}

	public static String getXinChenTelephone() {
		return getConfig(PRE_FIX + "shop.xinchen.telephone");
	}
	public static String getXinChenShopCode() {
		return getConfig(PRE_FIX + "shop.xinchen.shopCode");
	}

	//经度
	public static Integer getXinChenSendType() {
		return getConfigInt(PRE_FIX + "shop.xinchen.sendType");
	}

	public static List<Integer> getDrinkPackingFeeCategory() {
		String config = getConfig(PRE_FIX + "shop.binjiang.drinkPackingFeeCategory");
		if (Objects.isNull(config) || StringUtils.isEmpty(config)) {
			return new ArrayList<>();
		}
		return Arrays.stream(config.split(",")).map(Integer::parseInt).collect(Collectors.toList());
	}

	public static List<Integer> getDrinkPackingFeeCategory(Integer shopId) {
		String config = getConfig(PRE_FIX + "shop.binjiang.drinkPackingFeeCategory",shopId);
		if (Objects.isNull(config) || StringUtils.isEmpty(config)) {
			return new ArrayList<>();
		}
		return Arrays.stream(config.split(",")).map(Integer::parseInt).collect(Collectors.toList());
	}

	public static boolean getAllowSubmitOrder() {
		Integer allowOrderSubmit = getConfigInt(PRE_FIX + "allow.order.submit");
		return allowOrderSubmit != null && allowOrderSubmit != 0;
	}

	public static boolean getAllowSubmitOrder(Integer shopId) {
		Integer allowOrderSubmit = getConfigInt(PRE_FIX + "allow.order.submit", shopId);
		return allowOrderSubmit != null && allowOrderSubmit != 0;
	}

	public static boolean getAllowSubmitShopOrder() {
		Integer allowOrderShopSubmit = getConfigInt(PRE_FIX + "shop.allow.order.submit.shop");
		return allowOrderShopSubmit != null && allowOrderShopSubmit != 0;
	}

	public static boolean getAllowSubmitShopOrder(Integer shopId) {
		Integer allowOrderShopSubmit = getConfigInt(PRE_FIX + "shop.allow.order.submit.shop", shopId);
		return allowOrderShopSubmit != null && allowOrderShopSubmit != 0;
	}

	public static boolean getAutoPushJDK() {
		Integer autoPushJDK = getConfigInt(PRE_FIX + "order.jdk.autoPush");
		return autoPushJDK != null && autoPushJDK != 0;
	}

	public static boolean getAutoPushJDK(Integer shopId) {
		Integer autoPushJDK = getConfigInt(PRE_FIX + "order.jdk.autoPush", shopId);
		return autoPushJDK != null && autoPushJDK != 0;
	}

    public static Integer getAllowCancelTimeGap() {
		return getConfigInt(PRE_FIX + "order.allowCancel.timeGap");
    }

    public static Integer getAllowCancelTimeGap(Integer shopId) {
		return getConfigInt(PRE_FIX + "order.allowCancel.timeGap", shopId);
    }

    public static Integer getInventoryNotifyCount() {
		return getConfigInt(PRE_FIX + "goods.inventory.notify.count");
    }

    public static Integer getInventoryNotifyCount(Integer shopId) {
		return getConfigInt(PRE_FIX + "goods.inventory.notify.count", shopId);
    }

	public static String getEveningDiscountConfig() {
		String config = getConfig(PRE_FIX + "shop.discount.evening.config");
		return config;
	}

	public static String getEveningDiscountConfig(Integer shopId) {
		String config = getConfig(PRE_FIX + "shop.discount.evening.config", shopId);
		return config;
	}

	public static BigDecimal getExpressColdChainLimit() {
		return getConfigBigDec(PRE_FIX + "shop.express.coldChain");
	}

	public static BigDecimal getExpressColdChainLimit(Integer shopId) {
		return getConfigBigDec(PRE_FIX + "shop.express.coldChain", shopId);
	}

	public static BigDecimal getExpressCommonLimit() {
		return getConfigBigDec(PRE_FIX + "shop.express.common");
	}

	public static BigDecimal getExpressCommonLimit(Integer shopId) {
		return getConfigBigDec(PRE_FIX + "shop.express.common", shopId);
	}

	public static String getJdkShopConfig(Integer shopId) {
		String config = getConfig(PRE_FIX + "jdk.config", shopId);
		return config;
	}

	@Override
	public String getPrefix() {
		return PRE_FIX;
	}
}