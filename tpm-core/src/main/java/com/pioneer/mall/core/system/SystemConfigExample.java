package com.pioneer.mall.core.system;

/**
 * SystemConfig 使用示例
 * 展示如何使用支持 shopId 维度的配置方法
 */
public class SystemConfigExample {

    /**
     * 示例：获取商场名称
     * 如果指定了 shopId，优先返回该店铺的配置，否则返回全局配置
     */
    public static void exampleGetMallName() {
        // 获取全局商场名称
        String globalMallName = SystemConfig.getMallName();
        System.out.println("全局商场名称: " + globalMallName);
        
        // 获取特定店铺的商场名称
        Integer shopId = 123;
        String shopMallName = SystemConfig.getMallName(shopId);
        System.out.println("店铺 " + shopId + " 的商场名称: " + shopMallName);
    }

    /**
     * 示例：获取运费配置
     */
    public static void exampleGetFreight() {
        // 获取全局运费
        java.math.BigDecimal globalFreight = SystemConfig.getFreight();
        System.out.println("全局运费: " + globalFreight);
        
        // 获取特定店铺的运费
        Integer shopId = 123;
        java.math.BigDecimal shopFreight = SystemConfig.getFreight(shopId);
        System.out.println("店铺 " + shopId + " 的运费: " + shopFreight);
    }

    /**
     * 示例：获取首页显示限制
     */
    public static void exampleGetIndexLimits() {
        Integer shopId = 123;
        
        // 获取新品显示数量
        Integer newLimit = SystemConfig.getNewLimit(shopId);
        System.out.println("店铺 " + shopId + " 新品显示数量: " + newLimit);
        
        // 获取热门商品显示数量
        Integer hotLimit = SystemConfig.getHotLimit(shopId);
        System.out.println("店铺 " + shopId + " 热门商品显示数量: " + hotLimit);
        
        // 获取品牌显示数量
        Integer brandLimit = SystemConfig.getBrandLimit(shopId);
        System.out.println("店铺 " + shopId + " 品牌显示数量: " + brandLimit);
    }

    /**
     * 示例：获取订单相关配置
     */
    public static void exampleGetOrderConfig() {
        Integer shopId = 123;
        
        // 是否允许提交订单
        boolean allowSubmitOrder = SystemConfig.getAllowSubmitOrder(shopId);
        System.out.println("店铺 " + shopId + " 是否允许提交订单: " + allowSubmitOrder);
        
        // 是否允许提交店铺订单
        boolean allowSubmitShopOrder = SystemConfig.getAllowSubmitShopOrder(shopId);
        System.out.println("店铺 " + shopId + " 是否允许提交店铺订单: " + allowSubmitShopOrder);
        
        // 订单取消时间间隔
        Integer cancelTimeGap = SystemConfig.getAllowCancelTimeGap(shopId);
        System.out.println("店铺 " + shopId + " 订单取消时间间隔: " + cancelTimeGap);
    }

    /**
     * 示例：获取晚间折扣配置
     */
    public static void exampleGetEveningDiscountConfig() {
        Integer shopId = 123;
        
        // 获取晚间折扣配置
        String eveningDiscountConfig = SystemConfig.getEveningDiscountConfig(shopId);
        System.out.println("店铺 " + shopId + " 晚间折扣配置: " + eveningDiscountConfig);
    }

    /**
     * 示例：兼容性 - 使用原有方法（不传 shopId）
     */
    public static void exampleBackwardCompatibility() {
        // 原有的方法仍然可以正常使用，获取全局配置
        String mallName = SystemConfig.getMallName();
        Integer newLimit = SystemConfig.getNewLimit();
        java.math.BigDecimal freight = SystemConfig.getFreight();
        boolean allowSubmitOrder = SystemConfig.getAllowSubmitOrder();
        
        System.out.println("兼容性测试 - 全局配置:");
        System.out.println("商场名称: " + mallName);
        System.out.println("新品显示数量: " + newLimit);
        System.out.println("运费: " + freight);
        System.out.println("是否允许提交订单: " + allowSubmitOrder);
    }

    /**
     * 示例：在业务代码中的实际使用
     */
    public static class BusinessService {
        
        /**
         * 根据店铺获取运费
         * @param shopId 店铺ID，可以为null（表示使用全局配置）
         * @return 运费
         */
        public java.math.BigDecimal getFreightByShop(Integer shopId) {
            if (shopId != null) {
                return SystemConfig.getFreight(shopId);
            } else {
                return SystemConfig.getFreight();
            }
        }
        
        /**
         * 检查店铺是否允许提交订单
         * @param shopId 店铺ID，可以为null（表示使用全局配置）
         * @return 是否允许提交订单
         */
        public boolean isOrderSubmitAllowed(Integer shopId) {
            if (shopId != null) {
                return SystemConfig.getAllowSubmitOrder(shopId);
            } else {
                return SystemConfig.getAllowSubmitOrder();
            }
        }
        
        /**
         * 获取店铺的首页显示配置
         * @param shopId 店铺ID，可以为null（表示使用全局配置）
         * @return 首页显示配置对象
         */
        public IndexDisplayConfig getIndexDisplayConfig(Integer shopId) {
            IndexDisplayConfig config = new IndexDisplayConfig();
            
            if (shopId != null) {
                config.setNewLimit(SystemConfig.getNewLimit(shopId));
                config.setHotLimit(SystemConfig.getHotLimit(shopId));
                config.setBrandLimit(SystemConfig.getBrandLimit(shopId));
                config.setTopicLimit(SystemConfig.getTopicLimit(shopId));
            } else {
                config.setNewLimit(SystemConfig.getNewLimit());
                config.setHotLimit(SystemConfig.getHotLimit());
                config.setBrandLimit(SystemConfig.getBrandLimit());
                config.setTopicLimit(SystemConfig.getTopicLimit());
            }
            
            return config;
        }
    }
    
    /**
     * 首页显示配置类
     */
    public static class IndexDisplayConfig {
        private Integer newLimit;
        private Integer hotLimit;
        private Integer brandLimit;
        private Integer topicLimit;
        
        // getters and setters
        public Integer getNewLimit() { return newLimit; }
        public void setNewLimit(Integer newLimit) { this.newLimit = newLimit; }
        
        public Integer getHotLimit() { return hotLimit; }
        public void setHotLimit(Integer hotLimit) { this.hotLimit = hotLimit; }
        
        public Integer getBrandLimit() { return brandLimit; }
        public void setBrandLimit(Integer brandLimit) { this.brandLimit = brandLimit; }
        
        public Integer getTopicLimit() { return topicLimit; }
        public void setTopicLimit(Integer topicLimit) { this.topicLimit = topicLimit; }
    }
}
