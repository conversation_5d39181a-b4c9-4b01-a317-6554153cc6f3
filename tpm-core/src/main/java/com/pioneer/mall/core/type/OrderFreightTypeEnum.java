package com.pioneer.mall.core.type;

import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.db.enums.TpmTransportTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum OrderFreightTypeEnum {

    Distribution((byte) 0, "配送"),
    Pickup((byte) 1, "自提"),
    DINE_IN((byte) 2, "堂食"),
    COLD_CHAIN((byte) 3, "冷链"),
    COMMON((byte) 4, "普通");

    private final Byte value;
    private final String desc;

    public static OrderFreightTypeEnum convertTransPortType(Integer value){
        if (Objects.equals(TpmTransportTypeEnums.COLD_CHAIN.getCode(),value)){
            return COLD_CHAIN;
        }else if (Objects.equals(TpmTransportTypeEnums.COMMON.getCode(),value)){
            return COMMON;
        }
        return null;
    }
}
