package com.pioneer.mall.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/7 16:25
 */
@Slf4j
public class ConvertUtil {

    public static  <T> T beanConvert(Object obj,Class<T> clz){
        return beanConvert(obj,clz,null);
    }

    public static  <T> T beanConvert(Object obj,Class<T> clz,String... ignore){
        if (Objects.isNull(obj)){
            return null;
        }
        try {
            T t = clz.newInstance();
            BeanUtils.copyProperties(obj,t,ignore);
            return t;
        } catch (Exception e) {
            log.error("ConvertUtil beanConvert error={}",e.getMessage(),e);
        }
        return null;
    }

    public static <K,V> List<V> listConvert(List<K> list,Class<V> clz){
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        List<V> collect = list.stream().map(l -> {
            try {
                V v = clz.newInstance();
                BeanUtils.copyProperties(l, v);
                return v;
            } catch (Exception e) {
                log.error("ConvertUtil listConvert error={}",e.getMessage(),e);
                return null;
            }
        }).collect(Collectors.toList());
        return collect;
    }
}
