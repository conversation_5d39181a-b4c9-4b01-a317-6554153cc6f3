package com.pioneer.mall.core.util;

import com.pioneer.mall.core.vo.TpmOrderGoodsResVo;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.util.SpecificationUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class GoodsSpecificationUtil {

    public static void main(String[] args) {
        // 给getOrderGoodsResVoList 写个测试用例
        List<TpmOrderGoods> orderGoodsList = new ArrayList<>();
        TpmOrderGoods tpmOrderGoods = new TpmOrderGoods();
        tpmOrderGoods.setSpecifications("[\"黑色\",\"256G\"]");


    }

    public static List<TpmOrderGoodsResVo> getOrderGoodsResVoList(List<TpmOrderGoods> orderGoodsList) {
        if (CollectionUtils.isEmpty(orderGoodsList)) {
            return new ArrayList<>();
        }
        return orderGoodsList.stream().map(z -> {
            TpmOrderGoodsResVo tpmOrderGoodsResVo = new TpmOrderGoodsResVo();
            BeanUtils.copyProperties(z, tpmOrderGoodsResVo);
            String specificationJoin = SpecificationUtil.getSpecificationsValueJoinByAttributes(z.getSpecifications());
            tpmOrderGoodsResVo.setSpecificationJoin(specificationJoin);
            return tpmOrderGoodsResVo;
        }).collect(Collectors.toList());
    }
}
