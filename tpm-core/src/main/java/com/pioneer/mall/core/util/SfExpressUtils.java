package com.pioneer.mall.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.vo.SfApiResponse;
import com.pioneer.mall.core.vo.SfResultData;
import com.sf.csim.express.service.CallExpressServiceTools;
import com.sf.csim.express.service.HttpClientUtil;
import com.sf.csim.express.service.IServiceCodeStandard;
import com.sf.csim.express.service.code.ExpressServiceCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/17 20:10
 */
@Slf4j
@Component
public class SfExpressUtils {

    @Value("${sf.express.partnerID}")
    private String partnerID;
    @Value("${sf.express.md5Key}")
    private String md5Key;
    @Value("${sf.express.url}")
    private String url;


    /**
     * 路由注册
     *
     * @param orderSn
     * @param wayBill
     * @throws Exception
     */
    public void registerRoute(String orderSn, String wayBill) throws Exception {
        IServiceCodeStandard testService = ExpressServiceCodeEnum.EXP_RECE_REGISTER_ROUTE;
//        CallExpressServiceTools client = CallExpressServiceTools.getInstance();
        Map<String, String> params = new HashMap<>();
        String timeStamp = String.valueOf(System.currentTimeMillis());
//        String msgData = CallExpressServiceTools.packageMsgData(testService);
        // 封装查询参数
        Map<String, Object> msgDataMap = new HashMap<>();
        msgDataMap.put("type", "2");
        msgDataMap.put("attributeNo", wayBill);
        String msgData = JSONObject.toJSONString(msgDataMap);

        params.put("partnerID", partnerID);
        params.put("requestID", UUID.randomUUID().toString().replace("-", ""));
        params.put("serviceCode", testService.getCode());
        params.put("timestamp", timeStamp);
        params.put("msgData", msgData);
        params.put("msgDigest", CallExpressServiceTools.getMsgDigest(msgData, timeStamp, md5Key));
        long startTime = System.currentTimeMillis();
        log.info("====调用实际请求：" + params);
        String result = HttpClientUtil.post(url, params);
        log.info("===返回结果：" + result);
        log.info("====调用丰桥的接口服务代码：" + testService.getCode() + " 接口耗时：" + (System.currentTimeMillis() - startTime) + "====");
        ObjectMapper mapper = new ObjectMapper();
        // 解析成功响应
        SfApiResponse sfApiResponse = mapper.readValue(result, SfApiResponse.class);
        boolean callSuccess = sfApiResponse.isCallSuccess();
        String apiResultData = sfApiResponse.getApiResultData();
        SfResultData resultData = mapper.readValue(apiResultData, SfResultData.class);
        if (callSuccess) {
            log.info("成功响应调用: {}", JSON.toJSONString(resultData));
            WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatShopNotifyUrl(1),
                    null,
                    "订单:"+orderSn+"运单:"+wayBill+"注册运单轨迹成功");
        } else {
            String errorMsg = resultData.getErrorMsg();
            log.error("失败响应调用: {}", errorMsg);
            WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatShopNotifyUrl(1),
                    null,
                    "订单:"+orderSn+"运单:"+wayBill+"注册运单轨迹失败:"+errorMsg);
        }
    }



    /**
     * 运单号模式：（type=2）
     * type为2运单号模式，丰桥页面勾选没有强制效果
     * 根据运单号+顾客编码,查询订单信息，存在，即可注册成功
     * 根据运单号+顾客编码+校验月结卡号,查询订单信息，存在，即可注册成功
     *
     * @param args
     * @throws UnsupportedEncodingException
     */
    public static void main1(String[] args) throws UnsupportedEncodingException {

        IServiceCodeStandard testService = ExpressServiceCodeEnum.EXP_RECE_REGISTER_ROUTE;
        CallExpressServiceTools client = CallExpressServiceTools.getInstance();
        Map<String, String> params = new HashMap();
        String timeStamp = String.valueOf(System.currentTimeMillis());
//        String msgData = CallExpressServiceTools.packageMsgData(testService);
        // 封装查询参数
        Map<String, Object> msgDataMap = new HashMap<>();
        msgDataMap.put("type", "2");
        msgDataMap.put("attributeNo", "运单号");
        String msgData = JSONObject.toJSONString(msgDataMap);

        params.put("partnerID", "HRCSWJUOMZGZ");
        params.put("requestID", UUID.randomUUID().toString().replace("-", ""));
        params.put("serviceCode", testService.getCode());
        params.put("timestamp", timeStamp);
        params.put("msgData", msgData);
        params.put("msgDigest", CallExpressServiceTools.getMsgDigest(msgData, timeStamp, "5v3xnNC1kQ8wdJNVXQyT3dBOKDqw14tU"));
        long startTime = System.currentTimeMillis();
        log.info("====调用实际请求：" + params);
        log.info(JSONObject.toJSONString(params));
        String result = HttpClientUtil.post("https://sfapi.sf-express.com/std/service", params);
        log.info("====调用丰桥的接口服务代码：" + testService.getCode() + " 接口耗时：" + (System.currentTimeMillis() - startTime) + "====");
        log.info("===调用地址 ===https://sfapi.sf-express.com/std/service");
        log.info("===顾客编码 ===LZKJNqzovjR");
//        log.info("===返回结果：" + result);
    }
}
