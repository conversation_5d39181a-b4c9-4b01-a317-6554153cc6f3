package com.pioneer.mall.core.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.net.ssl.SSLContext;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

@Slf4j
public class WechatNotifyUtils {

    public static boolean wechatNoteSend(String webHookAddress, List<String> chatPhoneList, String errorMsg) {
        try {
            HttpClient httpclient = HttpClients.createDefault();
            HttpPost httppost = new HttpPost(webHookAddress);
            httppost.addHeader("Content-Type", "application/json; charset=utf-8");
            String testMsg = "{\"msgtype\":\"text\",\"text\":{\"mentioned_mobile_list\":" + chatPhoneList + ",\"content\":\"" + errorMsg + "\"}}";
            StringEntity se = new StringEntity(testMsg, "utf-8");
            httppost.setEntity(se);
            HttpResponse response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                log.info("消息发送成功!");
                return true;
            } else {
                log.error("消息发送失败！异常码如下：{}", response.getStatusLine().getStatusCode());
                return false;
            }
        } catch (IOException e) {
            log.error("消息发送失败, 异常信息如下: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取发送附件所用的 mediaId
     *
     * @param key 机器人url 中的 key 值
     * @return mediaId 文件id，通过下文的文件上传接口获取
     */
    public static String requestMediaId(String key, byte[] file, String fileName) {
        HttpRequest httpRequest = HttpUtil.createPost("https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?type=file&key=" + key)
                .form("media", file, fileName);
        cn.hutool.http.HttpResponse httpResponse = httpRequest.execute();
        String body = httpResponse.body();
        log.info("requestMediaId - responseBody=" + body);
        Map<String, Object> paramMap = JSON.parseObject(body, Map.class);

        return (String) paramMap.get("media_id");
    }

    /**
     * 企微发送附件
     *
     * @param webHookAddress 机器人url
     * @param file           文件字节流
     * @return
     */
    public static boolean sendFileMsg(String webHookAddress, byte[] file, String fileName) {
        String key = webHookAddress.substring(webHookAddress.lastIndexOf("key=") + 4);
        String mediaId = requestMediaId(key, file, fileName);
        try {
            HttpClient httpclient = HttpClients.createDefault();
            HttpPost httppost = new HttpPost(webHookAddress);
            httppost.addHeader("Content-Type", "application/json; charset=utf-8");
            String testMsg = "{\"msgtype\":\"file\",\"file\":{\"media_id\":\"" + mediaId + "\"}}";
            log.info("sendFileMsg - testMsg={}", testMsg);
            StringEntity se = new StringEntity(testMsg, "utf-8");
            httppost.setEntity(se);
            HttpResponse response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                log.info("消息发送成功!");
                return true;
            } else {
                log.error("消息发送失败！异常码如下：{}", response.getStatusLine().getStatusCode());
                return false;
            }
        } catch (IOException e) {
            log.error("消息发送失败, 异常信息如下: {}", e.getMessage());
            return false;
        }
    }

    public static boolean sendFileMsg(String webHookAddress, ByteArrayOutputStream file, String fileName) {
        return sendFileMsg(webHookAddress, file.toByteArray(), fileName);
    }

    public static boolean wechatNotifyMd(String webHookAddress, List<String> chatPhoneList, String message) {
        if (StringUtils.isEmpty(webHookAddress)) {
            return false;
        }
        boolean result = true;
        try {
            HttpClient httpclient = HttpClients.createDefault();
            HttpPost httppost = new HttpPost(webHookAddress);
            httppost.addHeader("Content-Type", "application/json; charset=utf-8");
            String testMsg = "{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"" + message + "\"}}";
            StringEntity se = new StringEntity(testMsg, "utf-8");
            httppost.setEntity(se);
            HttpResponse response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                log.info("消息发送成功!");
            } else {
                log.error("消息发送失败！异常码如下：{}", response.getStatusLine().getStatusCode());
                result = false;
            }
        } catch (IOException e) {
            log.error("消息发送失败, 异常信息如下: {}", e.getMessage());
            result = false;
        }

        // 通知@人
        if (result && !CollectionUtils.isEmpty(chatPhoneList)) {
            wechatNoteSend(webHookAddress, chatPhoneList, "");
        }
        return result;
    }

    /**
     * markdown 警告信息
     *
     * @param title     表体
     * @param declareNo 申报单号
     * @param exception 异常信息
     * @param desc      描述
     * @return
     */
    public static String mdWarnMessage(String title, String declareNo, String exception, String desc) {
        StringBuilder builder = new StringBuilder();
        builder.append("<font color=\\\"warning\\\">**" + title + "**</font>，请相关同事注意。\\n");
        builder.append(String.format("> 单号：%s\n", declareNo));
        builder.append(String.format("> 异常信息：%s\n", exception));
        builder.append(String.format("> 备注：%s\n", desc));
        return builder.toString();
    }


    /**
     * SSL忽略证书连接
     */
    private static CloseableHttpClient buildSSLCloseableHttpClient() {
        SSLContext sslContext = null;
        try {
            sslContext = new SSLContextBuilder().loadTrustMaterial(null,
                    new TrustStrategy() {
                        // 信任所有
                        @Override
                        public boolean isTrusted(X509Certificate[] chain,
                                                 String authType) throws CertificateException {
                            return true;
                        }
                    }).build();
        } catch (Exception e) {
            log.error("[企微消息发送]创建忽略证书验证的SSL连接异常:{}", e.getMessage(), e);
        }
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext, new String[]{"TLSv1"}, null,
                SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
        return HttpClients.custom().setSSLSocketFactory(sslsf).build();
    }
}
