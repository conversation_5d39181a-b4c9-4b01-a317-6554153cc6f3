package com.pioneer.mall.core.util;


import com.pioneer.mall.db.util.WxResponseCode;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 封装
 * @author: 潘本乐（Belep）
 * @create: 2022-03-30 14:49
 **/
@Data
public class WxResult<T> implements Serializable {

    private static final long serialVersionUID = 8351783528625556686L;

    private Integer code;

    private String message;

    private T data;

    /**
     * Instantiates a new Rpc result.
     */
    public WxResult() {
    }

    /**
     * Instantiates a new Rpc result.
     *
     * @param code    the code
     * @param message the message
     * @param data    the data
     */
    public WxResult(final Integer code, final String message, final T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * return success.
     *
     * @return {@linkplain WxResult}
     */
    public static <T> WxResult<T> success() {
        return success("success");
    }

    /**
     * return success.
     *
     * @param msg msg
     * @return {@linkplain WxResult}
     */
    public static <T> WxResult<T> success(final String msg) {
        return success(msg, null);
    }

    /**
     * return success.
     *
     * @param data this is result data.
     * @return {@linkplain WxResult}
     */
    public static <T> WxResult<T> success(final T data) {
        return success("success", data);
    }

    /**
     * return success.
     *
     * @param msg  this ext msg.
     * @param data this is result data.
     * @return {@linkplain WxResult}
     */
    public static <T> WxResult<T> success(final String msg, final T data) {
        return get(ResultCodeCons.SUCCESSFUL, msg, data);
    }


    /**
     * Success soul web result.
     *
     * @param code the code
     * @param msg  the msg
     * @param data the data
     * @return the rpc result
     */
    public static <T> WxResult<T> success(final Integer code, final String msg, final T data) {
        return get(code, msg, data);
    }

    /**
     * return error .
     *
     * @param msg error msg
     * @return {@linkplain WxResult}
     */
    public static <T> WxResult<T> error(final String msg) {
        return error(ResultCodeCons.BUSINESS_ERROR, msg);
    }

    /**
     * return error .
     *
     * @param code error code
     * @param msg  error msg
     * @return {@linkplain WxResult}
     */
    public static <T> WxResult<T> error(final Integer code, final String msg) {
        return get(code, msg, null);
    }

    public static <T> WxResult<T> error(final WxResponseCode code) {
        return get(code.code(), code.desc(), null);
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> WxResult<T> isSuccess(final Boolean isSuccess, final String errorMsg) {
        if (isSuccess) {
            return success();
        } else {
            return error(errorMsg);
        }
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> WxResult<T> isSuccess(final Boolean isSuccess, final String errorMsg, final Integer errorCode) {
        if (isSuccess) {
            return success();
        } else {
            return error(errorCode, errorMsg);
        }
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> WxResult<T> isSuccess(final Boolean isSuccess, final String errorMsg, final T data) {
        if (isSuccess) {
            return success(data);
        } else {
            return error(errorMsg);
        }
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> WxResult<T> isSuccess(final Boolean isSuccess, final String errorMsg, final Integer errorCode, final T data) {
        if (isSuccess) {
            return success(data);
        } else {
            return error(errorCode, errorMsg);
        }
    }

    /**
     * return error .
     *
     * @param code error code
     * @param msg  error msg
     * @param data the data
     * @return {@linkplain WxResult}
     */
    public static <T> WxResult<T> error(final Integer code, final String msg, final T data) {
        return get(code, msg, data);
    }

    private static <T> WxResult<T> get(final Integer code, final String msg, final T data) {
        return new WxResult(code, msg, data);
    }

    public static <T> WxResult<T> unlogin() {
        return error(-1001, "请登录");
    }

    public static <T> WxResult<T> badArgument() {
        return error(-401, "参数不对");
    }

    public static <T> WxResult<T> badArgumentValue() {
        return error(-402, "参数值不对");
    }

    public static <T> WxResult<T> userNotExist() {
        return error(-403, "用户不存在");
    }

    public static <T> WxResult<T> serious() {
        return error(-502, "系统内部错误");
    }

    public static <T> WxResult<T> updatedDataFailed() {
        return error(-505, "更新数据失败");
    }

    public static <T> WxResult<T> updatedDateExpired() {
        return error(-504, "更新数据已经失效");
    }

}
