package com.pioneer.mall.core.util;

import cn.binarywang.wx.miniapp.api.WxMaExpressService;
import cn.binarywang.wx.miniapp.api.WxMaOrderShippingService;
import cn.binarywang.wx.miniapp.bean.express.WxMaExpressDelivery;
import cn.binarywang.wx.miniapp.bean.shop.request.shipping.*;
import cn.binarywang.wx.miniapp.bean.shop.response.WxMaOrderShippingInfoBaseResponse;
import com.alibaba.fastjson.JSON;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.type.OrderFreightTypeEnum;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.domain.TpmRecharge;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.service.*;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/6/11 15:12
 */
@Service
public class WxUploadShippingInfoUtils {
    private static final Logger logger = LoggerFactory.getLogger(WxUploadShippingInfoUtils.class);
    @Autowired
    private TpmOrderService tpmOrderService;
    @Autowired
    private TpmOrderGoodsService tpmOrderGoodsService;
    @Autowired
    private TpmUserService tpmUserService;
    @Autowired
    private TpmGoodsService tpmGoodsService;
    @Autowired
    private WxMaOrderShippingService wxMaOrderShippingService;
    @Autowired
    private TpmCategoryService tpmCategoryService;
    @Autowired
    private WxMaExpressService wxMaExpressService;

    public void doUploadShippingInfoVirtual(TpmRecharge tpmRecharge, TpmUser tpmUser) throws WxErrorException {
        WxMaOrderShippingInfoUploadRequest request = new WxMaOrderShippingInfoUploadRequest();
        OrderKeyBean orderKeyBean = new OrderKeyBean();
        orderKeyBean.setOrderNumberType(2);
        orderKeyBean.setTransactionId(tpmRecharge.getPayId());
        request.setOrderKey(orderKeyBean);
        request.setLogisticsType(3);
        request.setDeliveryMode(1);
        request.setIsAllDelivered(true);
        ShippingListBean shippingListBean = new ShippingListBean();
        shippingListBean.setItemDesc("用户充值:" + tpmRecharge.getRechargeAmount() + "元");
        request.setShippingList(Collections.singletonList(shippingListBean));

        // 获取当前时间并设置时区偏移量
        OffsetDateTime currentTime = OffsetDateTime.now(ZoneOffset.of("+08:00"));
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

        // 格式化当前时间
        String formattedTime = currentTime.format(formatter);
        request.setUploadTime(formattedTime);
        PayerBean payerBean = new PayerBean();
        payerBean.setOpenid(tpmUser.getWeixinOpenid());
        request.setPayer(payerBean);
        logger.info("doUploadShippingInfo req={}", JSON.toJSONString(request));
        WxMaOrderShippingInfoBaseResponse response = wxMaOrderShippingService.upload(request);
        logger.info("doUploadShippingInfo res={}", JSON.toJSONString(response));
    }

    @Async
    public void doUploadShippingInfo(Integer orderId) {
        TpmOrder tpmOrder = tpmOrderService.findById(orderId);
        List<TpmOrderGoods> tpmOrderGoods = tpmOrderGoodsService.queryByOid(orderId);
        Integer userId = tpmOrder.getUserId();
        TpmUser tpmUser = tpmUserService.findById(userId);
        try {
            this.doUploadShippingInfo(tpmUser, tpmOrder, tpmOrderGoods);
        } catch (Exception e) {
            logger.error("doUploadShippingInfo error", e);
        }
    }

    public void doUploadShippingInfo(TpmUser tpmUser, TpmOrder tpmOrder, List<TpmOrderGoods> goodsList) throws WxErrorException {
        try {
            WxMaOrderShippingInfoUploadRequest request = new WxMaOrderShippingInfoUploadRequest();
            OrderKeyBean orderKeyBean = new OrderKeyBean();
            orderKeyBean.setOrderNumberType(2);
            orderKeyBean.setTransactionId(tpmOrder.getPayId());
            request.setOrderKey(orderKeyBean);
            if (Objects.equals(tpmOrder.getFreightType(), OrderFreightTypeEnum.Distribution.getValue())) {
                //2、同城配送
                request.setLogisticsType(2);
            } else {
                //4、用户自提
                request.setLogisticsType(4);
            }
            //1、UNIFIED_DELIVERY（统一发货
            request.setDeliveryMode(1);
            request.setIsAllDelivered(true);
            // 拼接结果字符串
            StringBuilder result = new StringBuilder();
            goodsList.forEach(tpmGoods -> {
                String categoryName = tpmGoods.getGoodsName();
                String part = categoryName + ":" + tpmGoods.getNumber() + "件;";
                result.append(part);
            });

            // 转换为字符串并截取
            String resultString = result.toString();
            if (resultString.length() > 120) {
                resultString = resultString.substring(0, 120);
            }
            ShippingListBean shippingListBean = new ShippingListBean();
            shippingListBean.setItemDesc(resultString);
            request.setShippingList(Collections.singletonList(shippingListBean));


            // 获取当前时间并设置时区偏移量
            OffsetDateTime currentTime = OffsetDateTime.now(ZoneOffset.of("+08:00"));
            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

            // 格式化当前时间
            String formattedTime = currentTime.format(formatter);
            request.setUploadTime(formattedTime);
            PayerBean payerBean = new PayerBean();
            payerBean.setOpenid(tpmUser.getWeixinOpenid());
            request.setPayer(payerBean);
            logger.info("doUploadShippingInfo req={}", JSON.toJSONString(request));
            WxMaOrderShippingInfoBaseResponse response = wxMaOrderShippingService.upload(request);
            logger.info("doUploadShippingInfo res={}", JSON.toJSONString(response));
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(tpmOrder.getShopId()), null, "订单:" + tpmOrder.getOrderSn() + "上传发货信息成功");
        } catch (Exception e) {
            logger.error("doUploadShippingInfo error={}",e.getMessage(), e);
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(tpmOrder.getShopId()), null, "订单:" + tpmOrder.getOrderSn() + "上传发货信息失败，请及时处理 error:"+e.getMessage());
        }
    }

    public void doUploadExpressShippingInfo(Integer orderId){
        TpmOrder tpmOrder = tpmOrderService.findById(orderId);
        List<TpmOrderGoods> tpmOrderGoods = tpmOrderGoodsService.queryByOid(orderId);
        Integer userId = tpmOrder.getUserId();
        TpmUser tpmUser = tpmUserService.findById(userId);
        try {
            this.doUploadExpressShippingInfo(tpmUser, tpmOrder, tpmOrderGoods);
        } catch (Exception e) {
            logger.error("doUploadShippingInfo error", e);
        }
    }

    public void doUploadExpressShippingInfo(TpmUser tpmUser, TpmOrder tpmOrder, List<TpmOrderGoods> goodsList) throws WxErrorException {
        try {

            if (Objects.isNull(tpmOrder)) {
                logger.info("doUploadExpressShippingInfo 订单为空");
                return;
            }
            if (!Objects.equals(tpmOrder.getBusinessType(), TpmBusinessTypeEnums.EXPRESS.getCode())) {
                logger.info("doUploadExpressShippingInfo 订单类型不是商城");
                return;
            }
            WxMaOrderShippingInfoUploadRequest request = new WxMaOrderShippingInfoUploadRequest();
            OrderKeyBean orderKeyBean = new OrderKeyBean();
            orderKeyBean.setOrderNumberType(2);
            orderKeyBean.setTransactionId(tpmOrder.getPayId());
            request.setOrderKey(orderKeyBean);
            //1、实体物流配送采用快递公司进行实体物流配送形式
            request.setLogisticsType(1);
            //1、UNIFIED_DELIVERY（统一发货
            List<TpmOrder> tpmOrderServiceByPayId = tpmOrderService.findByPayId(tpmOrder.getPayId());
            if (tpmOrderServiceByPayId.size() > 1){
                logger.info("doUploadExpressShippingInfo orderSn={} 分拆发货",tpmOrder.getOrderSn());
                //分拆
                request.setDeliveryMode(2);
            }else {
                logger.info("doUploadExpressShippingInfo orderSn={} 统一发货",tpmOrder.getOrderSn());
                //统一
                request.setDeliveryMode(1);
            }
            request.setIsAllDelivered(tpmOrderServiceByPayId.stream().allMatch(t -> Objects.nonNull(t.getShipSn())));
            // 拼接结果字符串
            StringBuilder result = new StringBuilder();
            goodsList.forEach(tpmGoods -> {
                String categoryName = tpmGoods.getGoodsName();
                String part = categoryName + ":" + tpmGoods.getNumber() + "件;";
                result.append(part);
            });

            // 转换为字符串并截取
            String resultString = result.toString();
            if (resultString.length() > 120) {
                resultString = resultString.substring(0, 120);
            }
            ShippingListBean shippingListBean = new ShippingListBean();
            shippingListBean.setTrackingNo(tpmOrder.getShipSn());
            shippingListBean.setExpressCompany("SF");
            shippingListBean.setItemDesc(resultString);
            ContactBean contactBean = new ContactBean();
            String mobile = tpmOrder.getMobile();
            String s = maskPhoneNumber(mobile);
            contactBean.setReceiverContact(s);
            shippingListBean.setContact(contactBean);
            request.setShippingList(Collections.singletonList(shippingListBean));


            // 获取当前时间并设置时区偏移量
            OffsetDateTime currentTime = OffsetDateTime.now(ZoneOffset.of("+08:00"));
            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

            // 格式化当前时间
            String formattedTime = currentTime.format(formatter);
            request.setUploadTime(formattedTime);
            PayerBean payerBean = new PayerBean();
            payerBean.setOpenid(tpmUser.getWeixinOpenid());
            request.setPayer(payerBean);
            logger.info("doUploadShippingInfo req={}", JSON.toJSONString(request));
            WxMaOrderShippingInfoBaseResponse response = wxMaOrderShippingService.upload(request);
            logger.info("doUploadShippingInfo res={}", JSON.toJSONString(response));
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatShopNotifyUrl(tpmOrder.getShopId()), null, "订单:" + tpmOrder.getOrderSn() + "上传发货信息成功");
        } catch (Exception e) {
            logger.error("doUploadShippingInfo error={}",e.getMessage(), e);
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatShopNotifyUrl(tpmOrder.getShopId()), null, "订单:" + tpmOrder.getOrderSn() + "上传发货信息失败，请及时处理");
        }
    }

    public static String maskPhoneNumber(String phoneNumber) {
        // 校验：非空 + 11位 + 全数字
        if (phoneNumber == null || phoneNumber.length() != 11 || !phoneNumber.matches("\\d+")) {
            return phoneNumber; // 可替换为抛异常或默认值
        }

        // 拼接掩码格式
        return new StringBuilder()
                .append(phoneNumber.substring(0, 3)) // 前3位
                .append("****")                     // 固定4位掩码
                .append(phoneNumber.substring(7))    // 后4位
                .toString();
    }

    public void test() throws WxErrorException {
        List<WxMaExpressDelivery> allDelivery = wxMaExpressService.getAllDelivery();
        System.out.println(JSON.toJSONString(allDelivery));
    }


}
