package com.pioneer.mall.core.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/18 22:33
 */
@Data
public class SfApiResponse {
    @JsonProperty("apiErrorMsg")
    private String apiErrorMsg;

    @JsonProperty("apiResponseID")
    private String apiResponseID;

    @JsonProperty("apiResultCode")
    private String apiResultCode;

    @JsonProperty("apiResultData")
    private String apiResultData;

    // 解析 apiResultData 中的 success 字段
    public boolean isCallSuccess() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        SfResultData resultData = mapper.readValue(apiResultData, SfResultData.class);
        return resultData.isSuccess();
    }
}

