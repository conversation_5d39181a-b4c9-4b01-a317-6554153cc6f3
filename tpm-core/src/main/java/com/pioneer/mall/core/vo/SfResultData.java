package com.pioneer.mall.core.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SfResultData {
    @JsonProperty("success")
    private boolean success;

    @JsonProperty("errorCode")
    private String errorCode;

    @JsonProperty("errorMsg")
    private String errorMsg;

    @JsonProperty("msgData")
    private Object msgData;

    public boolean isSuccess() {
        return success;
    }
}
