sp:
  appid: sp67b5dc3fec604
  appsecret: f050b21f95efd636745f77986609dd09
  printersn: 1923600370

dts:
  # 开发者应该设置成自己的wx相关信息
  wx:
    app-id: wx3b6b056e9dc0950e
    app-secret: c859568b8a69dc39b4b8ebffe1416337
    mch-id: 1671908119
    mch-key: 4C4F8D903D3A9139F64D0E133FD874DA
    notify-url: http://www.7riverlight.com/wx/order/tpmNotify
    # 商户证书文件路径
    # 请参考“商户证书”一节 https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=4_3
    key-path: "src/main/resources/apiclient_key.pem"
    apiV3-key: 4C4F8D903D3A9139F64D0E133FD874DA
    private-key-path: "/usr/local/cert/apiclient_key.pem"
    private-cert-path: "/usr/local/cert/apiclient_cert.pem"
  config:
    list:
      - app-id: wx3b6b056e9dc0950e
        app-secret: c859568b8a69dc39b4b8ebffe1416337
        mch-id: 1671908119
        mch-key: 4C4F8D903D3A9139F64D0E133FD874DA
        notify-url: http://www.7riverlight.com/wx/order/tpmNotify
        # 商户证书文件路径
        # 请参考“商户证书”一节 https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=4_3
        key-path: "src/main/resources/apiclient_key.pem"
        apiV3-key: 4C4F8D903D3A9139F64D0E133FD874DA
        private-key-path: "/usr/local/cert/apiclient_key.pem"
        private-cert-path: "/usr/local/cert/apiclient_cert.pem"
        shopId: 1
        serial: 1FE65B2F0D15686061D6A88E94545C92C1B5F840
        defaultShop: true
        domain: https://www.7riverlight.com/
      - app-id: wx3b6b056e9dc0950e
        app-secret: c859568b8a69dc39b4b8ebffe1416337
        mch-id: 1721166532
        mch-key: E70DAED5A75E5E5D20245EF360301779
        notify-url: http://www.7riverlight.com/wx/order/tpmNotify
        # 商户证书文件路径
        # 请参考“商户证书”一节 https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=4_3
        key-path: "src/main/resources/apiclient_key.pem"
        apiV3-key: E70DAED5A75E5E5D20245EF360301779
        private-key-path: "/usr/local/cert/qilai/apiclient_key.pem"
        private-cert-path: "/usr/local/cert/qilai/apiclient_cert.pem"
        shopId: 2
        serial: 7B67FAA73F7E38784F316633FD89BC877617A85E
        domain: https://www.7riverlight.com/

  #通知相关配置
  notify:
    mail:
      # 邮件通知配置,邮箱一般用于接收业务通知例如收到新的订单，sendto 定义邮件接收者，通常为商城运营人员
      enable: true
      host: smtp.aliyun.com
      username: <EMAIL>
      password: xxx
      sendfrom: <EMAIL>
      sendto: <EMAIL>

    # 短消息模版通知配置
    # 短信息用于通知客户，例如发货短信通知，注意配置格式；template-name，template-templateId 请参考 NotifyType 枚举值
    sms:
      enable: true
      appid: 1400286323
      appkey: 223123sade14c129c4ce9a30b5f90ad2c
      template:
        - name: paySucceed
          templateId: 112233
        - name: captcha
          templateId: 112233
        - name: ship
          templateId: 112233
        - name: refund
          templateId: 112233

    # 微信模版通知配置
    # 微信模版用于通知客户或者运营者，注意配置格式；template-name，template-templateId 请参考 NotifyType 枚举值
    wx:
      enable: true
      template:
        - name: paySucceed
          templateId: sirUcgTAIQfoIxxxxxx
        - name: captcha
          templateId: OfBkXh7UILpsDkcxxxxxx
        - name: ship
          templateId: dqTuWzrmL_wwK-SJsxxxxxx
        - name: applyRefund
          templateId: lzPHltMpUOiBol7i2Yxxxxxx
        - name: refund
          templateId: NDy6EpPuu2C9NMUxxxxxx

  # 快鸟物流查询配置
  express:
    enable: true
    appId: "xxxxxx"
    appKey: "xxxxxx"
    vendors:
      - code: "HTKY"
        name: "百世快递"
      - code: "YZBK"
        name: "邮政国内标快"
      - code: "ZTO"
        name: "中通快递"
      - code: "YTO"
        name: "圆通速递"
      - code: "YD"
        name: "韵达速递"
      - code: "YZPY"
        name: "邮政快递包裹"
      - code: "EMS"
        name: "EMS"
      - code: "DBL"
        name: "德邦快递"
      - code: "FAST"
        name: "快捷快递"
      - code: "ZJS"
        name: "宅急送"
      - code: "TNT"
        name: "TNT快递"
      - code: "UPS"
        name: "UPS"
      - code: "DHL"
        name: "DHL"
      - code: "FEDEX"
        name: "FEDEX联邦(国内件)"
      - code: "FEDEX_GJ"
        name: "FEDEX联邦(国际件)"

  # 对象存储配置
  storage:
    # 当前工作的对象存储模式，分别是local、aliyun、tencent、qiniu
    active: aliyun
    # 本地对象存储配置信息
    local:
      storagePath: dts/storage
      # 这个地方应该是wx模块的WxStorageController的fetch方法对应的地址
      address: http://localhost:8080/demo/storage/
    # 阿里云对象存储配置信息
    aliyun:
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI5tKT2ouxitxiZkHjq8tM
      accessKeySecret: ******************************
      bucketName: 7riverlight
    # 腾讯对象存储配置信息
    # 请参考 https://cloud.tencent.com/document/product/436/6249
    tencent:
      secretId: 111111
      secretKey: xxxxxx
      region: xxxxxx
      bucketName: dts
    # 七牛云对象存储配置信息
    qiniu:
      endpoint: http://dtsshop.wx.clouddn.com
      accessKey: 111111
      secretKey: xxxxxx
      bucketName: dts

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

spring:
  datasource:
    druid:
      url: ******************************************************************************************************************************************************************
      #      url:  ***********************************************************************************************************************************************************************
      driver-class-name: com.mysql.jdbc.Driver
      username: tpm
      password: 5er2WSX4rfwex8
      initial-size: 10
      max-active: 50
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      filters: stat,wall
jdk:
  order:
    host: https://jop.judanke.cn
    appId: 10501
    appKey: d1748f6a645a542e9aac47437d3a008e37db989c
#    shopId: 155595
    relativeUserId: 340986925


sf:
  express:
    partnerID: HRCSWJUOMZGZ
    md5Key: cC5WiKEZ6KNSDTxbV3eIqWNOcYNKdlIE
    url: https://sfapi.sf-express.com/std/service