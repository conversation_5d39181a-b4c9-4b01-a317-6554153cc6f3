package com.pioneer.mall.core.system;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BaseConfig 测试类，验证 shopId 维度的配置功能
 */
public class BaseConfigTest {

    @BeforeEach
    public void setUp() {
        // 清空配置
        BaseConfig.configs.clear();
    }

    @Test
    public void testAddConfigWithoutShopId() {
        // 测试添加全局配置
        BaseConfig.addConfig("test.key", "global_value");
        
        assertEquals("global_value", BaseConfig.getConfig("test.key"));
        assertEquals("global_value", BaseConfig.getConfig("test.key", null));
    }

    @Test
    public void testAddConfigWithShopId() {
        // 测试添加店铺特定配置
        BaseConfig.addConfig("test.key", "shop_value", 123);
        
        assertEquals("shop_value", BaseConfig.getConfig("test.key", 123));
        assertNull(BaseConfig.getConfig("test.key")); // 全局配置不存在
        assertNull(BaseConfig.getConfig("test.key", null)); // 明确查询全局配置
    }

    @Test
    public void testConfigPriority() {
        // 测试配置优先级：店铺配置优先于全局配置
        BaseConfig.addConfig("test.key", "global_value");
        BaseConfig.addConfig("test.key", "shop_value", 123);
        
        // 查询全局配置
        assertEquals("global_value", BaseConfig.getConfig("test.key"));
        assertEquals("global_value", BaseConfig.getConfig("test.key", null));
        
        // 查询店铺配置，应该返回店铺特定值
        assertEquals("shop_value", BaseConfig.getConfig("test.key", 123));
        
        // 查询不存在的店铺配置，应该回退到全局配置
        assertEquals("global_value", BaseConfig.getConfig("test.key", 456));
    }

    @Test
    public void testConfigInt() {
        BaseConfig.addConfig("test.int", "100");
        BaseConfig.addConfig("test.int", "200", 123);
        
        assertEquals(Integer.valueOf(100), BaseConfig.getConfigInt("test.int"));
        assertEquals(Integer.valueOf(200), BaseConfig.getConfigInt("test.int", 123));
        assertEquals(Integer.valueOf(100), BaseConfig.getConfigInt("test.int", 456)); // 回退到全局
    }

    @Test
    public void testConfigBigDecimal() {
        BaseConfig.addConfig("test.decimal", "10.5");
        BaseConfig.addConfig("test.decimal", "20.8", 123);
        
        assertEquals(0, BaseConfig.getConfigBigDec("test.decimal").compareTo(new java.math.BigDecimal("10.5")));
        assertEquals(0, BaseConfig.getConfigBigDec("test.decimal", 123).compareTo(new java.math.BigDecimal("20.8")));
        assertEquals(0, BaseConfig.getConfigBigDec("test.decimal", 456).compareTo(new java.math.BigDecimal("10.5"))); // 回退到全局
    }

    @Test
    public void testNullValues() {
        // 测试空值处理
        assertNull(BaseConfig.getConfig("nonexistent.key"));
        assertNull(BaseConfig.getConfig("nonexistent.key", 123));
        assertNull(BaseConfig.getConfigInt("nonexistent.key"));
        assertNull(BaseConfig.getConfigInt("nonexistent.key", 123));
        assertNull(BaseConfig.getConfigBigDec("nonexistent.key"));
        assertNull(BaseConfig.getConfigBigDec("nonexistent.key", 123));
    }

    @Test
    public void testBuildConfigKey() {
        // 通过反射测试 buildConfigKey 方法的逻辑
        BaseConfig.addConfig("test.key", "global_value", null);
        BaseConfig.addConfig("test.key", "shop_value", 123);
        
        // 验证内部存储结构
        assertTrue(BaseConfig.configs.containsKey("test.key")); // 全局配置
        assertTrue(BaseConfig.configs.containsKey("test.key:123")); // 店铺配置
        
        assertEquals("global_value", BaseConfig.configs.get("test.key"));
        assertEquals("shop_value", BaseConfig.configs.get("test.key:123"));
    }
}
