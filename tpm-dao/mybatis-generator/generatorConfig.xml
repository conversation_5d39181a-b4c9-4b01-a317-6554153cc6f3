<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="mysqlgenerator" targetRuntime="MyBatis3">
        <property name="autoDelimitKeywords" value="true"/>
        <!--可以使用``包括字段名，避免字段名与sql保留字冲突报错-->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 自动生成toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <!-- 自动生成equals方法和hashcode方法 -->
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>

        <!-- 非官方插件 https://github.com/itfsw/mybatis-generator-plugin -->
        <!-- 查询单条数据插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.SelectOneByExamplePlugin"/>
        <!-- 查询结果选择性返回插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.SelectSelectivePlugin"/>
        <!-- Example Criteria 增强插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.ExampleEnhancedPlugin"/>
        <!-- 数据Model属性对应Column获取插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.ModelColumnPlugin"/>
        <!-- 逻辑删除插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.LogicalDeletePlugin">
            <!-- 这里配置的是全局逻辑删除列和逻辑删除值，当然在table中配置的值会覆盖该全局配置 -->
            <!-- 逻辑删除列类型只能为数字、字符串或者布尔类型 -->
            <property name="logicalDeleteColumn" value="deleted"/>
            <!-- 逻辑删除-已删除值 -->
            <property name="logicalDeleteValue" value="1"/>
            <!-- 逻辑删除-未删除值 -->
            <property name="logicalUnDeleteValue" value="0"/>
        </plugin>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <!--<property name="suppressAllComments" value="true"/>-->
        </commentGenerator>

        <!--数据库连接信息-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**************************************************************************************************************************************************************"
                        userId="pioneer"
                        password="3edcdfW$wsx56^"/>

        <javaTypeResolver>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <!-- fixme 直接生成到db目录， 注意需不需要把已经生成的替换调，按需执行-->
        <!--        <javaModelGenerator targetPackage="com.pioneer.mall.db.domain" targetProject="src/main/java"/>-->
        <!--        <sqlMapGenerator targetPackage="com.pioneer.mall.db.dao" targetProject="src/main/resources"/>-->
        <!--        <javaClientGenerator type="XMLMAPPER" targetPackage="com.pioneer.mall.db.dao"-->
        <!--                             targetProject="src/main/java"/>-->

        <!-- 生成到generate 目录，需要手动copy到，db目录  -->
        <javaModelGenerator targetPackage="com.pioneer.mall.generate.domain" targetProject="src/main/java"/>
        <sqlMapGenerator targetPackage="com.pioneer.mall.generate.dao" targetProject="src/main/resources"/>
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.pioneer.mall.generate.dao"
                             targetProject="src/main/java"/>
        <!--表名-->
        <!--                <table tableName="tpm_goods">-->
        <!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                </table>-->
        <!--        <table tableName="tpm_freight_step_price">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_recharge">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_recharge_config">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_order_goods">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_ad">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_address">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_admin">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="role_ids" javaType="java.lang.Integer[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_brand">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--                <table tableName="tpm_cart">-->
        <!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                    <columnOverride column="specifications" javaType="java.lang.String[]"-->
        <!--                                    typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler"/>-->
        <!--                </table>-->
        <!--                <table tableName="tpm_category">-->
        <!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                </table>-->
        <!--        <table tableName="tpm_collect">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_comment">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="pic_urls" javaType="java.lang.String[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler"/>-->
        <!--        </table>-->

        <!--        <table tableName="tpm_feedback">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="pic_urls" javaType="java.lang.String[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler"/>-->
        <!--        </table>-->

        <!--        <table tableName="tpm_footprint">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--                <table tableName="tpm_goods">-->
        <!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                    <columnOverride column="gallery" javaType="java.lang.String[]"-->
        <!--                                    typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler"/>-->
        <!--                </table>-->
        <!--        <table tableName="tpm_goods_attribute">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_goods_specification">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_goods_product">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="specifications" javaType="java.lang.String[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_issue">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_keyword">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_order">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_order_goods">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="specifications" javaType="java.lang.String[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler"/>-->
        <!--            <columnOverride column="comments" javaType="java.lang.Integer[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler"/>-->
        <!--        </table>-->

        <!--        </table>-->
        <!--        <table tableName="tpm_region">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_search_history">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_storage">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_topic">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="goods" javaType="java.lang.String[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_user">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_system">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table tableName="tpm_user_formid">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table tableName="tpm_groupon_rules">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_groupon">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_coupon">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="goods_value" javaType="java.lang.Integer[]"-->
        <!--                            typeHandler="com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_coupon_user">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_role">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_permission">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_account_trace">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_user_account">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_article">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_del_picture">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_agency_share">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_balance">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_point">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_membership">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_recharge">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_wx_page_config">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tpm_recharge_config">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--                <table tableName="tpm_shop_hours">-->
        <!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                </table>-->
<!--                        <table tableName="tpm_shop_info">-->
<!--                            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--                        </table>-->
        <!--                        <table tableName="tpm_coupon_code">-->
        <!--                            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                        </table>-->
        <!--                        <table tableName="tpm_coupon_code_use_log">-->
        <!--                            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                        </table>-->
<!--        <table tableName="tpm_waybill_route">-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
    </context>
</generatorConfiguration>