package com.pioneer.mall.db.bean.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 购物车提单结算优惠券对象
 * @date 2024/6/24 20:43
 */
@Data
public class CartCheckOutCouponInfoDTO {
    private BigDecimal couponPrice;
    private int couponLength;
    private int couponId;
    private int couponUserId;
    private String couponName;
    private Integer categoryId;
    private Integer goodsId;
    private Integer goodsType;
}
