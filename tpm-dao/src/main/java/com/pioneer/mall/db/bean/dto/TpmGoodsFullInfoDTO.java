package com.pioneer.mall.db.bean.dto;

import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/18 21:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TpmGoodsFullInfoDTO {
    private TpmGoods tpmGoods;
    private List<TpmGoodsAttributesDto> goodsAttributes;
}
