package com.pioneer.mall.db.bean.search;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName tpm_balance
 */
@Data
@NoArgsConstructor
public class TpmBalanceSearch extends Page {

    /**
     *
     */
    private Integer id;
    /**
     * 会员ID
     */
    private Integer userId;
    /**
     *
     */
    private String mobile;
    /**
     * 订单号
     */
    private String orderSn;
    /**
     * 余额变化数量
     */
    private BigDecimal amount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 状态，A正常；D作废
     */
    private String status;
    /**
     * 创建时间
     */
    private Date addTime;
    /**
     *
     */
    private Integer updateTime;
    /**
     *
     */
    private Integer deleted;

}
