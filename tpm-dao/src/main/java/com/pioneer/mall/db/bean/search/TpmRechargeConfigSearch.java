package com.pioneer.mall.db.bean.search;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/7 14:06
 */
@Data
public class TpmRechargeConfigSearch extends Page {
    private Integer id;

    private BigDecimal amount;

    private BigDecimal giveAmount;

    private BigDecimal givePoint;

//    private Integer sort;

    private Integer status;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    private Boolean deleted;

    private String title;

    private String sort;

    private String order;
}
