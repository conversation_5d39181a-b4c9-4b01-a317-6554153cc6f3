package com.pioneer.mall.db.bean.search;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 充值查询
 * @date 2024/3/6 22:01
 */
@Data
public class TpmRechargeSearch extends Page {

    private Integer id;
    private Integer userId;
    /**
     * 昵称
     */
    private String nickname;
    private String orderNo;
    private BigDecimal rechargeAmount;
    private BigDecimal giveAmount;
    private String rechargeType;
    private Integer status;
    private LocalDateTime payTime;
    private LocalDateTime payTimeBegin;
    private LocalDateTime payTimeEnd;
    private LocalDateTime addTime;
    private LocalDateTime addTimeBegin;
    private LocalDateTime addTimeEnd;
    private LocalDateTime updateTime;
    private Boolean deleted;
}
