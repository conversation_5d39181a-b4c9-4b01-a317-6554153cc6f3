package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmAccountTrace;
import com.pioneer.mall.db.domain.TpmAccountTraceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmAccountTraceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    long countByExample(TpmAccountTraceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int deleteByExample(TpmAccountTraceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int insert(TpmAccountTrace record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int insertSelective(TpmAccountTrace record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAccountTrace selectOneByExample(TpmAccountTraceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAccountTrace selectOneByExampleSelective(@Param("example") TpmAccountTraceExample example, @Param("selective") TpmAccountTrace.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmAccountTrace> selectByExampleSelective(@Param("example") TpmAccountTraceExample example, @Param("selective") TpmAccountTrace.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    List<TpmAccountTrace> selectByExample(TpmAccountTraceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAccountTrace selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmAccountTrace.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    TpmAccountTrace selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmAccountTrace record, @Param("example") TpmAccountTraceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmAccountTrace record, @Param("example") TpmAccountTraceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmAccountTrace record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_account_trace
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmAccountTrace record);
}