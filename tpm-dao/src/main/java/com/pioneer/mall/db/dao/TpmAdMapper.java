package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmAd;
import com.pioneer.mall.db.domain.TpmAdExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmAdMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    long countByExample(TpmAdExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int deleteByExample(TpmAdExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int insert(TpmAd record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int insertSelective(TpmAd record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAd selectOneByExample(TpmAdExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAd selectOneByExampleSelective(@Param("example") TpmAdExample example, @Param("selective") TpmAd.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmAd> selectByExampleSelective(@Param("example") TpmAdExample example, @Param("selective") TpmAd.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    List<TpmAd> selectByExample(TpmAdExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAd selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmAd.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    TpmAd selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAd selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmAd record, @Param("example") TpmAdExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmAd record, @Param("example") TpmAdExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmAd record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmAd record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmAdExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_ad
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}