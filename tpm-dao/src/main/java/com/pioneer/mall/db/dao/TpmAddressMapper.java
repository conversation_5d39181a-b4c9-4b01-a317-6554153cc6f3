package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmAddress;
import com.pioneer.mall.db.domain.TpmAddressExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmAddressMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    long countByExample(TpmAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int deleteByExample(TpmAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int insert(TpmAddress record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int insertSelective(TpmAddress record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAddress selectOneByExample(TpmAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAddress selectOneByExampleSelective(@Param("example") TpmAddressExample example, @Param("selective") TpmAddress.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmAddress> selectByExampleSelective(@Param("example") TpmAddressExample example, @Param("selective") TpmAddress.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    List<TpmAddress> selectByExample(TpmAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAddress selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmAddress.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    TpmAddress selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAddress selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmAddress record, @Param("example") TpmAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmAddress record, @Param("example") TpmAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmAddress record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmAddress record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_address
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}