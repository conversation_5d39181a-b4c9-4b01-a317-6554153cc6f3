package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmAdmin;
import com.pioneer.mall.db.domain.TpmAdminExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmAdminMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    long countByExample(TpmAdminExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int deleteByExample(TpmAdminExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int insert(TpmAdmin record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int insertSelective(TpmAdmin record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAdmin selectOneByExample(TpmAdminExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAdmin selectOneByExampleSelective(@Param("example") TpmAdminExample example, @Param("selective") TpmAdmin.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmAdmin> selectByExampleSelective(@Param("example") TpmAdminExample example, @Param("selective") TpmAdmin.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    List<TpmAdmin> selectByExample(TpmAdminExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAdmin selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmAdmin.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    TpmAdmin selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAdmin selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmAdmin record, @Param("example") TpmAdminExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmAdmin record, @Param("example") TpmAdminExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmAdmin record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmAdmin record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmAdminExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_admin
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}