package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmAgencyShare;
import com.pioneer.mall.db.domain.TpmAgencyShareExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmAgencyShareMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    long countByExample(TpmAgencyShareExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int deleteByExample(TpmAgencyShareExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int insert(TpmAgencyShare record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int insertSelective(TpmAgencyShare record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAgencyShare selectOneByExample(TpmAgencyShareExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAgencyShare selectOneByExampleSelective(@Param("example") TpmAgencyShareExample example, @Param("selective") TpmAgencyShare.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmAgencyShare> selectByExampleSelective(@Param("example") TpmAgencyShareExample example, @Param("selective") TpmAgencyShare.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    List<TpmAgencyShare> selectByExample(TpmAgencyShareExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmAgencyShare selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmAgencyShare.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    TpmAgencyShare selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmAgencyShare record, @Param("example") TpmAgencyShareExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmAgencyShare record, @Param("example") TpmAgencyShareExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmAgencyShare record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_agency_share
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmAgencyShare record);
}