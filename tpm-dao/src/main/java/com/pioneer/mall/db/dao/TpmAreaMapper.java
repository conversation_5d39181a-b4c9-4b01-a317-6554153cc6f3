package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmArea;
import com.pioneer.mall.db.domain.TpmAreaExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmAreaMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    long countByExample(TpmAreaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int deleteByExample(TpmAreaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int insert(TpmArea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int insertSelective(TpmArea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArea selectOneByExample(TpmAreaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArea selectOneByExampleSelective(@Param("example") TpmAreaExample example, @Param("selective") TpmArea.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmArea> selectByExampleSelective(@Param("example") TpmAreaExample example, @Param("selective") TpmArea.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    List<TpmArea> selectByExample(TpmAreaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArea selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") TpmArea.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    TpmArea selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArea selectByPrimaryKeyWithLogicalDelete(@Param("id") Long id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmArea record, @Param("example") TpmAreaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmArea record, @Param("example") TpmAreaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmArea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmArea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmAreaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_area
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Long id);
}