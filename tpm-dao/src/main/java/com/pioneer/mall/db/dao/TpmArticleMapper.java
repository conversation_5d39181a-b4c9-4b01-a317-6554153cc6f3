package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmArticle;
import com.pioneer.mall.db.domain.TpmArticleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmArticleMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    long countByExample(TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int deleteByExample(TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int insert(TpmArticle record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int insertSelective(TpmArticle record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArticle selectOneByExample(TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArticle selectOneByExampleSelective(@Param("example") TpmArticleExample example, @Param("selective") TpmArticle.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArticle selectOneByExampleWithBLOBs(TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmArticle> selectByExampleSelective(@Param("example") TpmArticleExample example, @Param("selective") TpmArticle.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    List<TpmArticle> selectByExampleWithBLOBs(TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    List<TpmArticle> selectByExample(TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArticle selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmArticle.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    TpmArticle selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmArticle selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmArticle record, @Param("example") TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TpmArticle record, @Param("example") TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmArticle record, @Param("example") TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmArticle record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TpmArticle record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmArticle record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmArticleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_article
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}