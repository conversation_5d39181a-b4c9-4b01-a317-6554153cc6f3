package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmBalance;
import com.pioneer.mall.db.domain.TpmBalanceExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmBalanceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    long countByExample(TpmBalanceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int deleteByExample(TpmBalanceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int insert(TpmBalance record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int insertSelective(TpmBalance record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBalance selectOneByExample(TpmBalanceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBalance selectOneByExampleSelective(@Param("example") TpmBalanceExample example, @Param("selective") TpmBalance.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmBalance> selectByExampleSelective(@Param("example") TpmBalanceExample example, @Param("selective") TpmBalance.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    List<TpmBalance> selectByExample(TpmBalanceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBalance selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmBalance.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    TpmBalance selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBalance selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmBalance record, @Param("example") TpmBalanceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmBalance record, @Param("example") TpmBalanceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmBalance record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmBalance record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmBalanceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_balance
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);

    List<TpmBalance> getBalanceListByOrderSn(String orderSn);
}