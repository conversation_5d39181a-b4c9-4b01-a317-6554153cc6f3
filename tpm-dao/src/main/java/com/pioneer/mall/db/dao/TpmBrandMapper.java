package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmBrand;
import com.pioneer.mall.db.domain.TpmBrandExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmBrandMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    long countByExample(TpmBrandExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int deleteByExample(TpmBrandExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int insert(TpmBrand record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int insertSelective(TpmBrand record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBrand selectOneByExample(TpmBrandExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBrand selectOneByExampleSelective(@Param("example") TpmBrandExample example, @Param("selective") TpmBrand.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmBrand> selectByExampleSelective(@Param("example") TpmBrandExample example, @Param("selective") TpmBrand.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    List<TpmBrand> selectByExample(TpmBrandExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBrand selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmBrand.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    TpmBrand selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmBrand selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmBrand record, @Param("example") TpmBrandExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmBrand record, @Param("example") TpmBrandExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmBrand record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmBrand record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmBrandExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_brand
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}