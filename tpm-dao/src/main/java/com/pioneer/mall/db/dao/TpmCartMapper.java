package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmCart;
import com.pioneer.mall.db.domain.TpmCartExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmCartMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    long countByExample(TpmCartExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCartExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int insert(TpmCart record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int insertSelective(TpmCart record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCart selectOneByExample(TpmCartExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCart selectOneByExampleSelective(@Param("example") TpmCartExample example, @Param("selective") TpmCart.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmCart> selectByExampleSelective(@Param("example") TpmCartExample example, @Param("selective") TpmCart.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    List<TpmCart> selectByExample(TpmCartExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCart selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmCart.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    TpmCart selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCart selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmCart record, @Param("example") TpmCartExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmCart record, @Param("example") TpmCartExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmCart record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmCart record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCartExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_cart
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}