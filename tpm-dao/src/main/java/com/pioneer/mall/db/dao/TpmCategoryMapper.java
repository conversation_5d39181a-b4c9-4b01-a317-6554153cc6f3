package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmCategory;
import com.pioneer.mall.db.domain.TpmCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmCategoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    long countByExample(TpmCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int insert(TpmCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int insertSelective(TpmCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCategory selectOneByExample(TpmCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCategory selectOneByExampleSelective(@Param("example") TpmCategoryExample example, @Param("selective") TpmCategory.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmCategory> selectByExampleSelective(@Param("example") TpmCategoryExample example, @Param("selective") TpmCategory.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    List<TpmCategory> selectByExample(TpmCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCategory selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmCategory.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    TpmCategory selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCategory selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmCategory record, @Param("example") TpmCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmCategory record, @Param("example") TpmCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_category
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}