package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmCollect;
import com.pioneer.mall.db.domain.TpmCollectExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmCollectMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    long countByExample(TpmCollectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCollectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int insert(TpmCollect record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int insertSelective(TpmCollect record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCollect selectOneByExample(TpmCollectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCollect selectOneByExampleSelective(@Param("example") TpmCollectExample example, @Param("selective") TpmCollect.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmCollect> selectByExampleSelective(@Param("example") TpmCollectExample example, @Param("selective") TpmCollect.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    List<TpmCollect> selectByExample(TpmCollectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCollect selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmCollect.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    TpmCollect selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCollect selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmCollect record, @Param("example") TpmCollectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmCollect record, @Param("example") TpmCollectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmCollect record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmCollect record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCollectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_collect
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}