package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmComment;
import com.pioneer.mall.db.domain.TpmCommentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmCommentMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    long countByExample(TpmCommentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCommentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int insert(TpmComment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int insertSelective(TpmComment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmComment selectOneByExample(TpmCommentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmComment selectOneByExampleSelective(@Param("example") TpmCommentExample example, @Param("selective") TpmComment.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmComment> selectByExampleSelective(@Param("example") TpmCommentExample example, @Param("selective") TpmComment.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    List<TpmComment> selectByExample(TpmCommentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmComment selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmComment.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    TpmComment selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmComment selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmComment record, @Param("example") TpmCommentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmComment record, @Param("example") TpmCommentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmComment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmComment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCommentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_comment
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}