package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmCouponCode;
import com.pioneer.mall.db.domain.TpmCouponCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmCouponCodeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    long countByExample(TpmCouponCodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCouponCodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int insert(TpmCouponCode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int insertSelective(TpmCouponCode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCode selectOneByExample(TpmCouponCodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCode selectOneByExampleSelective(@Param("example") TpmCouponCodeExample example, @Param("selective") TpmCouponCode.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmCouponCode> selectByExampleSelective(@Param("example") TpmCouponCodeExample example, @Param("selective") TpmCouponCode.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    List<TpmCouponCode> selectByExample(TpmCouponCodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCode selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmCouponCode.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    TpmCouponCode selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCode selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmCouponCode record, @Param("example") TpmCouponCodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmCouponCode record, @Param("example") TpmCouponCodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmCouponCode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmCouponCode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCouponCodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}