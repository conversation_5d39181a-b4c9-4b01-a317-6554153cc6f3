package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmCouponCodeUseLog;
import com.pioneer.mall.db.domain.TpmCouponCodeUseLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmCouponCodeUseLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    long countByExample(TpmCouponCodeUseLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCouponCodeUseLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int insert(TpmCouponCodeUseLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int insertSelective(TpmCouponCodeUseLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCodeUseLog selectOneByExample(TpmCouponCodeUseLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCodeUseLog selectOneByExampleSelective(@Param("example") TpmCouponCodeUseLogExample example, @Param("selective") TpmCouponCodeUseLog.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmCouponCodeUseLog> selectByExampleSelective(@Param("example") TpmCouponCodeUseLogExample example, @Param("selective") TpmCouponCodeUseLog.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    List<TpmCouponCodeUseLog> selectByExample(TpmCouponCodeUseLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCodeUseLog selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmCouponCodeUseLog.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    TpmCouponCodeUseLog selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponCodeUseLog selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmCouponCodeUseLog record, @Param("example") TpmCouponCodeUseLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmCouponCodeUseLog record, @Param("example") TpmCouponCodeUseLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmCouponCodeUseLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmCouponCodeUseLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCouponCodeUseLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_code_use_log
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}