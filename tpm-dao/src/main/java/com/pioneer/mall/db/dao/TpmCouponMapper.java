package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCouponExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmCouponMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    long countByExample(TpmCouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int insert(TpmCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int insertSelective(TpmCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCoupon selectOneByExample(TpmCouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCoupon selectOneByExampleSelective(@Param("example") TpmCouponExample example, @Param("selective") TpmCoupon.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmCoupon> selectByExampleSelective(@Param("example") TpmCouponExample example, @Param("selective") TpmCoupon.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    List<TpmCoupon> selectByExample(TpmCouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCoupon selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmCoupon.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    TpmCoupon selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCoupon selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmCoupon record, @Param("example") TpmCouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmCoupon record, @Param("example") TpmCouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}