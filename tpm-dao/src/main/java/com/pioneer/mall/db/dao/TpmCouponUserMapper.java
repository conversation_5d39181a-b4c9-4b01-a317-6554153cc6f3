package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmCouponUser;
import com.pioneer.mall.db.domain.TpmCouponUserExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmCouponUserMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    long countByExample(TpmCouponUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int deleteByExample(TpmCouponUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int insert(TpmCouponUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int insertSelective(TpmCouponUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponUser selectOneByExample(TpmCouponUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponUser selectOneByExampleSelective(@Param("example") TpmCouponUserExample example, @Param("selective") TpmCouponUser.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmCouponUser> selectByExampleSelective(@Param("example") TpmCouponUserExample example, @Param("selective") TpmCouponUser.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    List<TpmCouponUser> selectByExample(TpmCouponUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponUser selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmCouponUser.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    TpmCouponUser selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmCouponUser selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmCouponUser record, @Param("example") TpmCouponUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmCouponUser record, @Param("example") TpmCouponUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmCouponUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmCouponUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmCouponUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_coupon_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}