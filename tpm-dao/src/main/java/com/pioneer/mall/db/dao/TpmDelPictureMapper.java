package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmDelPicture;
import com.pioneer.mall.db.domain.TpmDelPictureExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmDelPictureMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    long countByExample(TpmDelPictureExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int deleteByExample(TpmDelPictureExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int insert(TpmDelPicture record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int insertSelective(TpmDelPicture record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmDelPicture selectOneByExample(TpmDelPictureExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmDelPicture selectOneByExampleSelective(@Param("example") TpmDelPictureExample example, @Param("selective") TpmDelPicture.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmDelPicture> selectByExampleSelective(@Param("example") TpmDelPictureExample example, @Param("selective") TpmDelPicture.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    List<TpmDelPicture> selectByExample(TpmDelPictureExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmDelPicture selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmDelPicture.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    TpmDelPicture selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmDelPicture record, @Param("example") TpmDelPictureExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmDelPicture record, @Param("example") TpmDelPictureExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmDelPicture record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_del_picture
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmDelPicture record);
}