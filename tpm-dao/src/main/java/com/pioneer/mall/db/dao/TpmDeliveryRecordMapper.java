package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmDeliveryRecord;
import com.pioneer.mall.db.domain.TpmDeliveryRecordExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmDeliveryRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    long countByExample(TpmDeliveryRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int deleteByExample(TpmDeliveryRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int insert(TpmDeliveryRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int insertSelective(TpmDeliveryRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmDeliveryRecord selectOneByExample(TpmDeliveryRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmDeliveryRecord selectOneByExampleSelective(@Param("example") TpmDeliveryRecordExample example, @Param("selective") TpmDeliveryRecord.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmDeliveryRecord> selectByExampleSelective(@Param("example") TpmDeliveryRecordExample example, @Param("selective") TpmDeliveryRecord.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    List<TpmDeliveryRecord> selectByExample(TpmDeliveryRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmDeliveryRecord selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmDeliveryRecord.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    TpmDeliveryRecord selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmDeliveryRecord selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmDeliveryRecord record, @Param("example") TpmDeliveryRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmDeliveryRecord record, @Param("example") TpmDeliveryRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmDeliveryRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmDeliveryRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmDeliveryRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}