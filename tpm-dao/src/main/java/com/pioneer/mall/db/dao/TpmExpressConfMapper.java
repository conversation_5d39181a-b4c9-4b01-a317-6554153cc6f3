package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmExpressConf;
import com.pioneer.mall.db.domain.TpmExpressConfExample;
import com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmExpressConfMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    long countByExample(TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int deleteByExample(TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int insert(TpmExpressConfWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int insertSelective(TpmExpressConfWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmExpressConf selectOneByExample(TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmExpressConfWithBLOBs selectOneByExampleSelective(@Param("example") TpmExpressConfExample example, @Param("selective") TpmExpressConfWithBLOBs.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmExpressConfWithBLOBs selectOneByExampleWithBLOBs(TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmExpressConfWithBLOBs> selectByExampleSelective(@Param("example") TpmExpressConfExample example, @Param("selective") TpmExpressConfWithBLOBs.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    List<TpmExpressConfWithBLOBs> selectByExampleWithBLOBs(TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    List<TpmExpressConf> selectByExample(TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmExpressConfWithBLOBs selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") TpmExpressConfWithBLOBs.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    TpmExpressConfWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmExpressConfWithBLOBs selectByPrimaryKeyWithLogicalDelete(@Param("id") Long id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmExpressConfWithBLOBs record, @Param("example") TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TpmExpressConfWithBLOBs record, @Param("example") TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmExpressConf record, @Param("example") TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmExpressConfWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TpmExpressConfWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmExpressConf record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmExpressConfExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Long id);
}