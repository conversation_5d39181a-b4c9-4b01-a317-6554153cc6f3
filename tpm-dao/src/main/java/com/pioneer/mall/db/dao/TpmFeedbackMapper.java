package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmFeedback;
import com.pioneer.mall.db.domain.TpmFeedbackExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmFeedbackMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    long countByExample(TpmFeedbackExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int deleteByExample(TpmFeedbackExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int insert(TpmFeedback record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int insertSelective(TpmFeedback record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFeedback selectOneByExample(TpmFeedbackExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFeedback selectOneByExampleSelective(@Param("example") TpmFeedbackExample example, @Param("selective") TpmFeedback.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmFeedback> selectByExampleSelective(@Param("example") TpmFeedbackExample example, @Param("selective") TpmFeedback.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    List<TpmFeedback> selectByExample(TpmFeedbackExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFeedback selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmFeedback.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    TpmFeedback selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFeedback selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmFeedback record, @Param("example") TpmFeedbackExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmFeedback record, @Param("example") TpmFeedbackExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmFeedback record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmFeedback record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmFeedbackExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_feedback
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}