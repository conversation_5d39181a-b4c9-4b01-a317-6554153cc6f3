package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmFootprint;
import com.pioneer.mall.db.domain.TpmFootprintExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmFootprintMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    long countByExample(TpmFootprintExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int deleteByExample(TpmFootprintExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int insert(TpmFootprint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int insertSelective(TpmFootprint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFootprint selectOneByExample(TpmFootprintExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFootprint selectOneByExampleSelective(@Param("example") TpmFootprintExample example, @Param("selective") TpmFootprint.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmFootprint> selectByExampleSelective(@Param("example") TpmFootprintExample example, @Param("selective") TpmFootprint.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    List<TpmFootprint> selectByExample(TpmFootprintExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFootprint selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmFootprint.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    TpmFootprint selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFootprint selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmFootprint record, @Param("example") TpmFootprintExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmFootprint record, @Param("example") TpmFootprintExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmFootprint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmFootprint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmFootprintExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_footprint
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}