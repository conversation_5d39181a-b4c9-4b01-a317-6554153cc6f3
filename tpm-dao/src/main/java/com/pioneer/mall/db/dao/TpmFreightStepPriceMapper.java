package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmFreightStepPrice;
import com.pioneer.mall.db.domain.TpmFreightStepPriceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmFreightStepPriceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    long countByExample(TpmFreightStepPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int deleteByExample(TpmFreightStepPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int insert(TpmFreightStepPrice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int insertSelective(TpmFreightStepPrice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFreightStepPrice selectOneByExample(TpmFreightStepPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFreightStepPrice selectOneByExampleSelective(@Param("example") TpmFreightStepPriceExample example, @Param("selective") TpmFreightStepPrice.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmFreightStepPrice> selectByExampleSelective(@Param("example") TpmFreightStepPriceExample example, @Param("selective") TpmFreightStepPrice.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    List<TpmFreightStepPrice> selectByExample(TpmFreightStepPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFreightStepPrice selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") TpmFreightStepPrice.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    TpmFreightStepPrice selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmFreightStepPrice selectByPrimaryKeyWithLogicalDelete(@Param("id") Long id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmFreightStepPrice record, @Param("example") TpmFreightStepPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmFreightStepPrice record, @Param("example") TpmFreightStepPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmFreightStepPrice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmFreightStepPrice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmFreightStepPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_freight_step_price
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Long id);
}