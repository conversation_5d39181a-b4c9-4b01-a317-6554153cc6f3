package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmGoodsAttribute;
import com.pioneer.mall.db.domain.TpmGoodsAttributeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmGoodsAttributeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    long countByExample(TpmGoodsAttributeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int deleteByExample(TpmGoodsAttributeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int insert(TpmGoodsAttribute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int insertSelective(TpmGoodsAttribute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsAttribute selectOneByExample(TpmGoodsAttributeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsAttribute selectOneByExampleSelective(@Param("example") TpmGoodsAttributeExample example, @Param("selective") TpmGoodsAttribute.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmGoodsAttribute> selectByExampleSelective(@Param("example") TpmGoodsAttributeExample example, @Param("selective") TpmGoodsAttribute.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    List<TpmGoodsAttribute> selectByExample(TpmGoodsAttributeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsAttribute selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmGoodsAttribute.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    TpmGoodsAttribute selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsAttribute selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmGoodsAttribute record, @Param("example") TpmGoodsAttributeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmGoodsAttribute record, @Param("example") TpmGoodsAttributeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmGoodsAttribute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmGoodsAttribute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmGoodsAttributeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_attribute
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}