package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.domain.TpmGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmGoodsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    long countByExample(TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int deleteByExample(TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int insert(TpmGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int insertSelective(TpmGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoods selectOneByExample(TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoods selectOneByExampleSelective(@Param("example") TpmGoodsExample example, @Param("selective") TpmGoods.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoods selectOneByExampleWithBLOBs(TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmGoods> selectByExampleSelective(@Param("example") TpmGoodsExample example, @Param("selective") TpmGoods.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    List<TpmGoods> selectByExampleWithBLOBs(TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    List<TpmGoods> selectByExample(TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoods selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmGoods.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    TpmGoods selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoods selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmGoods record, @Param("example") TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TpmGoods record, @Param("example") TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmGoods record, @Param("example") TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TpmGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}