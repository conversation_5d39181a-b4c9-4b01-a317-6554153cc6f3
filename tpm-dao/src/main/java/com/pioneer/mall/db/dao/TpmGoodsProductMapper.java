package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmGoodsProduct;
import com.pioneer.mall.db.domain.TpmGoodsProductExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmGoodsProductMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    long countByExample(TpmGoodsProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int deleteByExample(TpmGoodsProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int insert(TpmGoodsProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int insertSelective(TpmGoodsProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsProduct selectOneByExample(TpmGoodsProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsProduct selectOneByExampleSelective(@Param("example") TpmGoodsProductExample example, @Param("selective") TpmGoodsProduct.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmGoodsProduct> selectByExampleSelective(@Param("example") TpmGoodsProductExample example, @Param("selective") TpmGoodsProduct.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    List<TpmGoodsProduct> selectByExample(TpmGoodsProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsProduct selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmGoodsProduct.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    TpmGoodsProduct selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsProduct selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmGoodsProduct record, @Param("example") TpmGoodsProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmGoodsProduct record, @Param("example") TpmGoodsProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmGoodsProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmGoodsProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmGoodsProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_product
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}