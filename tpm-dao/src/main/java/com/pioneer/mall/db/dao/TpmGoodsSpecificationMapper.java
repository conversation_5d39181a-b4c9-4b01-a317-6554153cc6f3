package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmGoodsSpecification;
import com.pioneer.mall.db.domain.TpmGoodsSpecificationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmGoodsSpecificationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    long countByExample(TpmGoodsSpecificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int deleteByExample(TpmGoodsSpecificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int insert(TpmGoodsSpecification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int insertSelective(TpmGoodsSpecification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsSpecification selectOneByExample(TpmGoodsSpecificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsSpecification selectOneByExampleSelective(@Param("example") TpmGoodsSpecificationExample example, @Param("selective") TpmGoodsSpecification.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmGoodsSpecification> selectByExampleSelective(@Param("example") TpmGoodsSpecificationExample example, @Param("selective") TpmGoodsSpecification.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    List<TpmGoodsSpecification> selectByExample(TpmGoodsSpecificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsSpecification selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmGoodsSpecification.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    TpmGoodsSpecification selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGoodsSpecification selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmGoodsSpecification record, @Param("example") TpmGoodsSpecificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmGoodsSpecification record, @Param("example") TpmGoodsSpecificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmGoodsSpecification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmGoodsSpecification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmGoodsSpecificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods_specification
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}