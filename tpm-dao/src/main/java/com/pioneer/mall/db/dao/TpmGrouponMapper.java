package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmGroupon;
import com.pioneer.mall.db.domain.TpmGrouponExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmGrouponMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    long countByExample(TpmGrouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int deleteByExample(TpmGrouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int insert(TpmGroupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int insertSelective(TpmGroupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGroupon selectOneByExample(TpmGrouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGroupon selectOneByExampleSelective(@Param("example") TpmGrouponExample example, @Param("selective") TpmGroupon.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmGroupon> selectByExampleSelective(@Param("example") TpmGrouponExample example, @Param("selective") TpmGroupon.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    List<TpmGroupon> selectByExample(TpmGrouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGroupon selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmGroupon.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    TpmGroupon selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGroupon selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmGroupon record, @Param("example") TpmGrouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmGroupon record, @Param("example") TpmGrouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmGroupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmGroupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmGrouponExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}