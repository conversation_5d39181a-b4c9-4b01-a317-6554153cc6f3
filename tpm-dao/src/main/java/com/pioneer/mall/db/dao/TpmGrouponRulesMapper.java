package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmGrouponRules;
import com.pioneer.mall.db.domain.TpmGrouponRulesExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmGrouponRulesMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    long countByExample(TpmGrouponRulesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int deleteByExample(TpmGrouponRulesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int insert(TpmGrouponRules record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int insertSelective(TpmGrouponRules record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGrouponRules selectOneByExample(TpmGrouponRulesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGrouponRules selectOneByExampleSelective(@Param("example") TpmGrouponRulesExample example, @Param("selective") TpmGrouponRules.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmGrouponRules> selectByExampleSelective(@Param("example") TpmGrouponRulesExample example, @Param("selective") TpmGrouponRules.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    List<TpmGrouponRules> selectByExample(TpmGrouponRulesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGrouponRules selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmGrouponRules.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    TpmGrouponRules selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmGrouponRules selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmGrouponRules record, @Param("example") TpmGrouponRulesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmGrouponRules record, @Param("example") TpmGrouponRulesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmGrouponRules record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmGrouponRules record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmGrouponRulesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_groupon_rules
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}