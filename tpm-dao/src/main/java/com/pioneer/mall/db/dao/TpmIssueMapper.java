package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmIssue;
import com.pioneer.mall.db.domain.TpmIssueExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmIssueMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    long countByExample(TpmIssueExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int deleteByExample(TpmIssueExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int insert(TpmIssue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int insertSelective(TpmIssue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmIssue selectOneByExample(TpmIssueExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmIssue selectOneByExampleSelective(@Param("example") TpmIssueExample example, @Param("selective") TpmIssue.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmIssue> selectByExampleSelective(@Param("example") TpmIssueExample example, @Param("selective") TpmIssue.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    List<TpmIssue> selectByExample(TpmIssueExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmIssue selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmIssue.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    TpmIssue selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmIssue selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmIssue record, @Param("example") TpmIssueExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmIssue record, @Param("example") TpmIssueExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmIssue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmIssue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmIssueExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_issue
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}