package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmKeyword;
import com.pioneer.mall.db.domain.TpmKeywordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmKeywordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    long countByExample(TpmKeywordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int deleteByExample(TpmKeywordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int insert(TpmKeyword record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int insertSelective(TpmKeyword record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmKeyword selectOneByExample(TpmKeywordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmKeyword selectOneByExampleSelective(@Param("example") TpmKeywordExample example, @Param("selective") TpmKeyword.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmKeyword> selectByExampleSelective(@Param("example") TpmKeywordExample example, @Param("selective") TpmKeyword.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    List<TpmKeyword> selectByExample(TpmKeywordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmKeyword selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmKeyword.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    TpmKeyword selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmKeyword selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmKeyword record, @Param("example") TpmKeywordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmKeyword record, @Param("example") TpmKeywordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmKeyword record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmKeyword record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmKeywordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_keyword
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}