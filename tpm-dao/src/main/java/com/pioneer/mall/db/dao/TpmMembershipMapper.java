package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmMembership;
import com.pioneer.mall.db.domain.TpmMembershipExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmMembershipMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    long countByExample(TpmMembershipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int deleteByExample(TpmMembershipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int insert(TpmMembership record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int insertSelective(TpmMembership record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmMembership selectOneByExample(TpmMembershipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmMembership selectOneByExampleSelective(@Param("example") TpmMembershipExample example, @Param("selective") TpmMembership.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmMembership> selectByExampleSelective(@Param("example") TpmMembershipExample example, @Param("selective") TpmMembership.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    List<TpmMembership> selectByExample(TpmMembershipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmMembership selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmMembership.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    TpmMembership selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmMembership selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmMembership record, @Param("example") TpmMembershipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmMembership record, @Param("example") TpmMembershipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmMembership record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmMembership record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmMembershipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_membership
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}