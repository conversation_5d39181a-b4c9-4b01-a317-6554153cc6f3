package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.domain.TpmOrderGoodsExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmOrderGoodsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    long countByExample(TpmOrderGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int deleteByExample(TpmOrderGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int insert(TpmOrderGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int insertSelective(TpmOrderGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrderGoods selectOneByExample(TpmOrderGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrderGoods selectOneByExampleSelective(@Param("example") TpmOrderGoodsExample example, @Param("selective") TpmOrderGoods.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmOrderGoods> selectByExampleSelective(@Param("example") TpmOrderGoodsExample example, @Param("selective") TpmOrderGoods.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    List<TpmOrderGoods> selectByExample(TpmOrderGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrderGoods selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmOrderGoods.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    TpmOrderGoods selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrderGoods selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmOrderGoods record, @Param("example") TpmOrderGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmOrderGoods record, @Param("example") TpmOrderGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmOrderGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmOrderGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmOrderGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}