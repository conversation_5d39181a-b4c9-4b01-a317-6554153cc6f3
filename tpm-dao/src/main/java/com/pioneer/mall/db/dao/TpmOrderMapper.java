package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmOrderMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    long countByExample(TpmOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int deleteByExample(TpmOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int insert(TpmOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int insertSelective(TpmOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrder selectOneByExample(TpmOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrder selectOneByExampleSelective(@Param("example") TpmOrderExample example, @Param("selective") TpmOrder.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmOrder> selectByExampleSelective(@Param("example") TpmOrderExample example, @Param("selective") TpmOrder.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    List<TpmOrder> selectByExample(TpmOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrder selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmOrder.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    TpmOrder selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmOrder selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmOrder record, @Param("example") TpmOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmOrder record, @Param("example") TpmOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}