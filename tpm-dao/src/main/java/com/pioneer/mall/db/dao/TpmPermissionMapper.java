package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmPermission;
import com.pioneer.mall.db.domain.TpmPermissionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmPermissionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    long countByExample(TpmPermissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int deleteByExample(TpmPermissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int insert(TpmPermission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int insertSelective(TpmPermission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPermission selectOneByExample(TpmPermissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPermission selectOneByExampleSelective(@Param("example") TpmPermissionExample example, @Param("selective") TpmPermission.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmPermission> selectByExampleSelective(@Param("example") TpmPermissionExample example, @Param("selective") TpmPermission.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    List<TpmPermission> selectByExample(TpmPermissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPermission selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmPermission.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    TpmPermission selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPermission selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmPermission record, @Param("example") TpmPermissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmPermission record, @Param("example") TpmPermissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmPermission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmPermission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmPermissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_permission
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}