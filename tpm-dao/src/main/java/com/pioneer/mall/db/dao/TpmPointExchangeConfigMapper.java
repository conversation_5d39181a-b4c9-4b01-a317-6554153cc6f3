package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmPointExchangeConfig;
import com.pioneer.mall.db.domain.TpmPointExchangeConfigExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmPointExchangeConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    long countByExample(TpmPointExchangeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int deleteByExample(TpmPointExchangeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int insert(TpmPointExchangeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int insertSelective(TpmPointExchangeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPointExchangeConfig selectOneByExample(TpmPointExchangeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPointExchangeConfig selectOneByExampleSelective(@Param("example") TpmPointExchangeConfigExample example, @Param("selective") TpmPointExchangeConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmPointExchangeConfig> selectByExampleSelective(@Param("example") TpmPointExchangeConfigExample example, @Param("selective") TpmPointExchangeConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    List<TpmPointExchangeConfig> selectByExample(TpmPointExchangeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPointExchangeConfig selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmPointExchangeConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    TpmPointExchangeConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPointExchangeConfig selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmPointExchangeConfig record, @Param("example") TpmPointExchangeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmPointExchangeConfig record, @Param("example") TpmPointExchangeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmPointExchangeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmPointExchangeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmPointExchangeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point_exchange_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}