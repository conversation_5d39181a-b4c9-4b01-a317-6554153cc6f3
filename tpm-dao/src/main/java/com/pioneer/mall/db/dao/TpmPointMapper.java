package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmPoint;
import com.pioneer.mall.db.domain.TpmPointExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmPointMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    long countByExample(TpmPointExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int deleteByExample(TpmPointExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int insert(TpmPoint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int insertSelective(TpmPoint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPoint selectOneByExample(TpmPointExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPoint selectOneByExampleSelective(@Param("example") TpmPointExample example, @Param("selective") TpmPoint.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmPoint> selectByExampleSelective(@Param("example") TpmPointExample example, @Param("selective") TpmPoint.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    List<TpmPoint> selectByExample(TpmPointExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPoint selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmPoint.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    TpmPoint selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmPoint selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmPoint record, @Param("example") TpmPointExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmPoint record, @Param("example") TpmPointExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmPoint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmPoint record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmPointExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_point
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}