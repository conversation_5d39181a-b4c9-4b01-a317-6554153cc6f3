package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmRechargeConfig;
import com.pioneer.mall.db.domain.TpmRechargeConfigExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmRechargeConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    long countByExample(TpmRechargeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    int deleteByExample(TpmRechargeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    Long insert(TpmRechargeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    Long insertSelective(TpmRechargeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRechargeConfig selectOneByExample(TpmRechargeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRechargeConfig selectOneByExampleSelective(@Param("example") TpmRechargeConfigExample example, @Param("selective") TpmRechargeConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmRechargeConfig> selectByExampleSelective(@Param("example") TpmRechargeConfigExample example, @Param("selective") TpmRechargeConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    List<TpmRechargeConfig> selectByExample(TpmRechargeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRechargeConfig selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmRechargeConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    TpmRechargeConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRechargeConfig selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmRechargeConfig record, @Param("example") TpmRechargeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmRechargeConfig record, @Param("example") TpmRechargeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmRechargeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmRechargeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmRechargeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}