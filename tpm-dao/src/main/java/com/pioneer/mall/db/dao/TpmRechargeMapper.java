package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmRecharge;
import com.pioneer.mall.db.domain.TpmRechargeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmRechargeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    long countByExample(TpmRechargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int deleteByExample(TpmRechargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int insert(TpmRecharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int insertSelective(TpmRecharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRecharge selectOneByExample(TpmRechargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRecharge selectOneByExampleSelective(@Param("example") TpmRechargeExample example, @Param("selective") TpmRecharge.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmRecharge> selectByExampleSelective(@Param("example") TpmRechargeExample example, @Param("selective") TpmRecharge.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    List<TpmRecharge> selectByExample(TpmRechargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRecharge selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmRecharge.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    TpmRecharge selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRecharge selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmRecharge record, @Param("example") TpmRechargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmRecharge record, @Param("example") TpmRechargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmRecharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmRecharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmRechargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_recharge
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}