package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmRegion;
import com.pioneer.mall.db.domain.TpmRegionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmRegionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    long countByExample(TpmRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int deleteByExample(TpmRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int insert(TpmRegion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int insertSelective(TpmRegion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRegion selectOneByExample(TpmRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRegion selectOneByExampleSelective(@Param("example") TpmRegionExample example, @Param("selective") TpmRegion.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmRegion> selectByExampleSelective(@Param("example") TpmRegionExample example, @Param("selective") TpmRegion.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    List<TpmRegion> selectByExample(TpmRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRegion selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmRegion.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    TpmRegion selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmRegion record, @Param("example") TpmRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmRegion record, @Param("example") TpmRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmRegion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_region
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmRegion record);
}