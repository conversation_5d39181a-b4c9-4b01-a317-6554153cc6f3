package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmRole;
import com.pioneer.mall.db.domain.TpmRoleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmRoleMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    long countByExample(TpmRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int deleteByExample(TpmRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int insert(TpmRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int insertSelective(TpmRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRole selectOneByExample(TpmRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRole selectOneByExampleSelective(@Param("example") TpmRoleExample example, @Param("selective") TpmRole.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmRole> selectByExampleSelective(@Param("example") TpmRoleExample example, @Param("selective") TpmRole.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    List<TpmRole> selectByExample(TpmRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRole selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmRole.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    TpmRole selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmRole selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmRole record, @Param("example") TpmRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmRole record, @Param("example") TpmRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_role
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}