package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmSearchHistory;
import com.pioneer.mall.db.domain.TpmSearchHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmSearchHistoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    long countByExample(TpmSearchHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int deleteByExample(TpmSearchHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int insert(TpmSearchHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int insertSelective(TpmSearchHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSearchHistory selectOneByExample(TpmSearchHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSearchHistory selectOneByExampleSelective(@Param("example") TpmSearchHistoryExample example, @Param("selective") TpmSearchHistory.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmSearchHistory> selectByExampleSelective(@Param("example") TpmSearchHistoryExample example, @Param("selective") TpmSearchHistory.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    List<TpmSearchHistory> selectByExample(TpmSearchHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSearchHistory selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmSearchHistory.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    TpmSearchHistory selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSearchHistory selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmSearchHistory record, @Param("example") TpmSearchHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmSearchHistory record, @Param("example") TpmSearchHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmSearchHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmSearchHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmSearchHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_search_history
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}