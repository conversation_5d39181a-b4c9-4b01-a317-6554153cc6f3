package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmShopHours;
import com.pioneer.mall.db.domain.TpmShopHoursExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmShopHoursMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    long countByExample(TpmShopHoursExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int deleteByExample(TpmShopHoursExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int insert(TpmShopHours record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int insertSelective(TpmShopHours record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopHours selectOneByExample(TpmShopHoursExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopHours selectOneByExampleSelective(@Param("example") TpmShopHoursExample example, @Param("selective") TpmShopHours.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmShopHours> selectByExampleSelective(@Param("example") TpmShopHoursExample example, @Param("selective") TpmShopHours.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    List<TpmShopHours> selectByExample(TpmShopHoursExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopHours selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmShopHours.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    TpmShopHours selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopHours selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmShopHours record, @Param("example") TpmShopHoursExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmShopHours record, @Param("example") TpmShopHoursExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmShopHours record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmShopHours record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmShopHoursExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}