package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.domain.TpmShopInfoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmShopInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    long countByExample(TpmShopInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int deleteByExample(TpmShopInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int insert(TpmShopInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int insertSelective(TpmShopInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopInfo selectOneByExample(TpmShopInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopInfo selectOneByExampleSelective(@Param("example") TpmShopInfoExample example, @Param("selective") TpmShopInfo.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmShopInfo> selectByExampleSelective(@Param("example") TpmShopInfoExample example, @Param("selective") TpmShopInfo.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    List<TpmShopInfo> selectByExample(TpmShopInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopInfo selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmShopInfo.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    TpmShopInfo selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmShopInfo selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmShopInfo record, @Param("example") TpmShopInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmShopInfo record, @Param("example") TpmShopInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmShopInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmShopInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmShopInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_info
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}