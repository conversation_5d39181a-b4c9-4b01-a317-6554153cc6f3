package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmStorage;
import com.pioneer.mall.db.domain.TpmStorageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmStorageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    long countByExample(TpmStorageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int deleteByExample(TpmStorageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int insert(TpmStorage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int insertSelective(TpmStorage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmStorage selectOneByExample(TpmStorageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmStorage selectOneByExampleSelective(@Param("example") TpmStorageExample example, @Param("selective") TpmStorage.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmStorage> selectByExampleSelective(@Param("example") TpmStorageExample example, @Param("selective") TpmStorage.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    List<TpmStorage> selectByExample(TpmStorageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmStorage selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmStorage.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    TpmStorage selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmStorage selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmStorage record, @Param("example") TpmStorageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmStorage record, @Param("example") TpmStorageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmStorage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmStorage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmStorageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_storage
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}