package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmSystem;
import com.pioneer.mall.db.domain.TpmSystemExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmSystemMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    long countByExample(TpmSystemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int deleteByExample(TpmSystemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int insert(TpmSystem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int insertSelective(TpmSystem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSystem selectOneByExample(TpmSystemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSystem selectOneByExampleSelective(@Param("example") TpmSystemExample example, @Param("selective") TpmSystem.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmSystem> selectByExampleSelective(@Param("example") TpmSystemExample example, @Param("selective") TpmSystem.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    List<TpmSystem> selectByExample(TpmSystemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSystem selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmSystem.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    TpmSystem selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmSystem selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmSystem record, @Param("example") TpmSystemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmSystem record, @Param("example") TpmSystemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmSystem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmSystem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmSystemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_system
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}