package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmTopic;
import com.pioneer.mall.db.domain.TpmTopicExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmTopicMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    long countByExample(TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int deleteByExample(TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int insert(TpmTopic record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int insertSelective(TpmTopic record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmTopic selectOneByExample(TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmTopic selectOneByExampleSelective(@Param("example") TpmTopicExample example, @Param("selective") TpmTopic.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmTopic selectOneByExampleWithBLOBs(TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmTopic> selectByExampleSelective(@Param("example") TpmTopicExample example, @Param("selective") TpmTopic.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    List<TpmTopic> selectByExampleWithBLOBs(TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    List<TpmTopic> selectByExample(TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmTopic selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmTopic.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    TpmTopic selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmTopic selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmTopic record, @Param("example") TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TpmTopic record, @Param("example") TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmTopic record, @Param("example") TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmTopic record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TpmTopic record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmTopic record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmTopicExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_topic
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}