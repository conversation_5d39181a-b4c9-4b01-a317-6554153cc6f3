package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmUserAccount;
import com.pioneer.mall.db.domain.TpmUserAccountExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmUserAccountMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    long countByExample(TpmUserAccountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int deleteByExample(TpmUserAccountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int insert(TpmUserAccount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int insertSelective(TpmUserAccount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUserAccount selectOneByExample(TpmUserAccountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUserAccount selectOneByExampleSelective(@Param("example") TpmUserAccountExample example, @Param("selective") TpmUserAccount.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmUserAccount> selectByExampleSelective(@Param("example") TpmUserAccountExample example, @Param("selective") TpmUserAccount.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    List<TpmUserAccount> selectByExample(TpmUserAccountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUserAccount selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmUserAccount.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    TpmUserAccount selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmUserAccount record, @Param("example") TpmUserAccountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmUserAccount record, @Param("example") TpmUserAccountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmUserAccount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_account
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmUserAccount record);
}