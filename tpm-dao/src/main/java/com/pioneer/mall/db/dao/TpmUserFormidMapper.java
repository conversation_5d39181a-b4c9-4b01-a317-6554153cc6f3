package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmUserFormid;
import com.pioneer.mall.db.domain.TpmUserFormidExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmUserFormidMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    long countByExample(TpmUserFormidExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int deleteByExample(TpmUserFormidExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int insert(TpmUserFormid record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int insertSelective(TpmUserFormid record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUserFormid selectOneByExample(TpmUserFormidExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUserFormid selectOneByExampleSelective(@Param("example") TpmUserFormidExample example, @Param("selective") TpmUserFormid.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmUserFormid> selectByExampleSelective(@Param("example") TpmUserFormidExample example, @Param("selective") TpmUserFormid.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    List<TpmUserFormid> selectByExample(TpmUserFormidExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUserFormid selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmUserFormid.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    TpmUserFormid selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUserFormid selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmUserFormid record, @Param("example") TpmUserFormidExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmUserFormid record, @Param("example") TpmUserFormidExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmUserFormid record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmUserFormid record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmUserFormidExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user_formid
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}