package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.domain.TpmUserExample;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface TpmUserMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    long countByExample(TpmUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int deleteByExample(TpmUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int insert(TpmUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int insertSelective(TpmUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUser selectOneByExample(TpmUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUser selectOneByExampleSelective(@Param("example") TpmUserExample example, @Param("selective") TpmUser.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmUser> selectByExampleSelective(@Param("example") TpmUserExample example, @Param("selective") TpmUser.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    List<TpmUser> selectByExample(TpmUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUser selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmUser.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    TpmUser selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmUser selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmUser record, @Param("example") TpmUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmUser record, @Param("example") TpmUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);

    void updateUserBalance(List<Integer> userIdArr, BigDecimal balanceAmount);
}