package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmVip;
import com.pioneer.mall.db.domain.TpmVipExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmVipMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    long countByExample(TpmVipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int deleteByExample(TpmVipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int insert(TpmVip record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int insertSelective(TpmVip record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmVip selectOneByExample(TpmVipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmVip selectOneByExampleSelective(@Param("example") TpmVipExample example, @Param("selective") TpmVip.Column... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmVip> selectByExampleSelective(@Param("example") TpmVipExample example, @Param("selective") TpmVip.Column... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    List<TpmVip> selectByExample(TpmVipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmVip selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmVip.Column... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    TpmVip selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmVip selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmVip record, @Param("example") TpmVipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmVip record, @Param("example") TpmVipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmVip record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmVip record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmVipExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_vip
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}