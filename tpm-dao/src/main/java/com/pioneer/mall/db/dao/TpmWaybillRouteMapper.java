package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmWaybillRoute;
import com.pioneer.mall.db.domain.TpmWaybillRouteExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TpmWaybillRouteMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    long countByExample(TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int deleteByExample(TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int insert(TpmWaybillRoute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int insertSelective(TpmWaybillRoute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWaybillRoute selectOneByExample(TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWaybillRoute selectOneByExampleSelective(@Param("example") TpmWaybillRouteExample example, @Param("selective") TpmWaybillRoute.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWaybillRoute selectOneByExampleWithBLOBs(TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmWaybillRoute> selectByExampleSelective(@Param("example") TpmWaybillRouteExample example, @Param("selective") TpmWaybillRoute.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    List<TpmWaybillRoute> selectByExampleWithBLOBs(TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    List<TpmWaybillRoute> selectByExample(TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWaybillRoute selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmWaybillRoute.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    TpmWaybillRoute selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWaybillRoute selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmWaybillRoute record, @Param("example") TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TpmWaybillRoute record, @Param("example") TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmWaybillRoute record, @Param("example") TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmWaybillRoute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TpmWaybillRoute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmWaybillRoute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmWaybillRouteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}