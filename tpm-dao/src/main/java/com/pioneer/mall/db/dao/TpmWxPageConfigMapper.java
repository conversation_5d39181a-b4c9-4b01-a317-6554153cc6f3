package com.pioneer.mall.db.dao;

import com.pioneer.mall.db.domain.TpmWxPageConfig;
import com.pioneer.mall.db.domain.TpmWxPageConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TpmWxPageConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    long countByExample(TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int deleteByExample(TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int insert(TpmWxPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int insertSelective(TpmWxPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWxPageConfig selectOneByExample(TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWxPageConfig selectOneByExampleSelective(@Param("example") TpmWxPageConfigExample example, @Param("selective") TpmWxPageConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWxPageConfig selectOneByExampleWithBLOBs(TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    List<TpmWxPageConfig> selectByExampleSelective(@Param("example") TpmWxPageConfigExample example, @Param("selective") TpmWxPageConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    List<TpmWxPageConfig> selectByExampleWithBLOBs(TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    List<TpmWxPageConfig> selectByExample(TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWxPageConfig selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") TpmWxPageConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    TpmWxPageConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    TpmWxPageConfig selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TpmWxPageConfig record, @Param("example") TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TpmWxPageConfig record, @Param("example") TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TpmWxPageConfig record, @Param("example") TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TpmWxPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TpmWxPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TpmWxPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByExample(@Param("example") TpmWxPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_wx_page_config
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    int logicalDeleteByPrimaryKey(Integer id);
}