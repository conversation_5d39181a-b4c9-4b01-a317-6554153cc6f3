package com.pioneer.mall.db.dao.ex;

import java.util.List;

import com.pioneer.mall.db.domain.TpmComment;
import org.apache.ibatis.annotations.Param;

/**
 * 评论管理DAO层接口
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CommentMapperEx {

	/**
	 * 按入驻店铺查询归属的评论信息
	 * @param userId
	 * @param valueId
	 * @param orderBySql
	 * @param brandIdsSql
	 * @return
	 */
	List<TpmComment> queryBrandComment(@Param("type") Byte type, @Param("userId") String userId, @Param("valueId") String valueId, @Param("orderBySql") String orderBySql, @Param("brandIdsSql") String brandIdsSql);

}
