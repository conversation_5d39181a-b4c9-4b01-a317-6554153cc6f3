package com.pioneer.mall.db.dao.ex;

import java.util.List;

import com.pioneer.mall.db.domain.TpmGroupon;
import com.pioneer.mall.db.domain.TpmGrouponRules;
import org.apache.ibatis.annotations.Param;

/**
 * 团购管理
 * <AUTHOR>
 * @since 1.0.0
 */
public interface GrouponMapperEx {

	/**
	 * 按入驻店铺查询归属的团购规则信息
	 * @param goodsId
	 * @param orderBySql
	 * @param brandIdsSql
	 * @return
	 */
	List<TpmGrouponRules> queryBrandGrouponRules(@Param("goodsId") String goodsId, @Param("orderBySql") String orderBySql, @Param("brandIdsSql") String brandIdsSql);

	/**
	 * 按入驻店铺查询归属的团购记录信息
	 * @param goodsId
	 * @param orderBySql
	 * @param brandIdsSql
	 * @return
	 */
	List<TpmGroupon> queryBrandGroupons(@Param("rulesId") String rulesId, @Param("orderBySql") String orderBySql, @Param("brandIdsSql") String brandIdsSql);

}
