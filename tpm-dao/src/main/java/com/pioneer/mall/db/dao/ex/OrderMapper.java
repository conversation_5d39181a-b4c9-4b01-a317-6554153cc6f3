package com.pioneer.mall.db.dao.ex;

import java.time.LocalDateTime;
import java.util.List;

import com.pioneer.mall.db.domain.TpmOrder;
import org.apache.ibatis.annotations.Param;

public interface OrderMapper {

	int updateWithOptimisticLocker(@Param("lastUpdateTime") LocalDateTime lastUpdateTime,
			@Param("order") TpmOrder order);

	/**
	 * 根据条件获取入驻店铺的订单
	 * @param userId
	 * @param orderSn
	 * @param orderStatusSql
	 * @param orderBySql
	 * @param brandIdsSql
	 * @return
	 */
	List<TpmOrder> selectBrandOrdersByExample(@Param("userId") Integer userId, @Param("orderSn") String orderSn, @Param("orderStatusSql") String orderStatusSql, @Param("orderBySql") String orderBySql,
			@Param("brandIdsSql") String brandIdsSql,@Param("businessType") Integer businessType,@Param("freightType") Integer freightType,
											  @Param("shopIdList") List<Integer> shopIdList);

    int countByTypeToday(@Param("freightType")Byte freightType);
}