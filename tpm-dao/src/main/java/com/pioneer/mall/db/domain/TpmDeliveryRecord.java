package com.pioneer.mall.db.domain;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

public class TpmDeliveryRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean NOT_DELETED = false;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean IS_DELETED = true;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.tpm_order_id
     *
     * @mbg.generated
     */
    private Integer tpmOrderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.order_id
     *
     * @mbg.generated
     */
    private String orderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.platform
     *
     * @mbg.generated
     */
    private String platform;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.delivery_status
     *
     * @mbg.generated
     */
    private String deliveryStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.tip_fee
     *
     * @mbg.generated
     */
    private Integer tipFee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.freight_fee
     *
     * @mbg.generated
     */
    private Integer freightFee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.courier_name
     *
     * @mbg.generated
     */
    private String courierName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.courier_phone
     *
     * @mbg.generated
     */
    private String courierPhone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.delivery_brand
     *
     * @mbg.generated
     */
    private String deliveryBrand;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.delivery_distance
     *
     * @mbg.generated
     */
    private String deliveryDistance;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.delivery_order_no
     *
     * @mbg.generated
     */
    private String deliveryOrderNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.reason
     *
     * @mbg.generated
     */
    private String reason;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.from
     *
     * @mbg.generated
     */
    private Integer from;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.add_time
     *
     * @mbg.generated
     */
    private LocalDateTime addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_delivery_record.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.id
     *
     * @return the value of tpm_delivery_record.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.id
     *
     * @param id the value for tpm_delivery_record.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.tpm_order_id
     *
     * @return the value of tpm_delivery_record.tpm_order_id
     *
     * @mbg.generated
     */
    public Integer getTpmOrderId() {
        return tpmOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.tpm_order_id
     *
     * @param tpmOrderId the value for tpm_delivery_record.tpm_order_id
     *
     * @mbg.generated
     */
    public void setTpmOrderId(Integer tpmOrderId) {
        this.tpmOrderId = tpmOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.order_id
     *
     * @return the value of tpm_delivery_record.order_id
     *
     * @mbg.generated
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.order_id
     *
     * @param orderId the value for tpm_delivery_record.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.platform
     *
     * @return the value of tpm_delivery_record.platform
     *
     * @mbg.generated
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.platform
     *
     * @param platform the value for tpm_delivery_record.platform
     *
     * @mbg.generated
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.delivery_status
     *
     * @return the value of tpm_delivery_record.delivery_status
     *
     * @mbg.generated
     */
    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.delivery_status
     *
     * @param deliveryStatus the value for tpm_delivery_record.delivery_status
     *
     * @mbg.generated
     */
    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.tip_fee
     *
     * @return the value of tpm_delivery_record.tip_fee
     *
     * @mbg.generated
     */
    public Integer getTipFee() {
        return tipFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.tip_fee
     *
     * @param tipFee the value for tpm_delivery_record.tip_fee
     *
     * @mbg.generated
     */
    public void setTipFee(Integer tipFee) {
        this.tipFee = tipFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.freight_fee
     *
     * @return the value of tpm_delivery_record.freight_fee
     *
     * @mbg.generated
     */
    public Integer getFreightFee() {
        return freightFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.freight_fee
     *
     * @param freightFee the value for tpm_delivery_record.freight_fee
     *
     * @mbg.generated
     */
    public void setFreightFee(Integer freightFee) {
        this.freightFee = freightFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.courier_name
     *
     * @return the value of tpm_delivery_record.courier_name
     *
     * @mbg.generated
     */
    public String getCourierName() {
        return courierName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.courier_name
     *
     * @param courierName the value for tpm_delivery_record.courier_name
     *
     * @mbg.generated
     */
    public void setCourierName(String courierName) {
        this.courierName = courierName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.courier_phone
     *
     * @return the value of tpm_delivery_record.courier_phone
     *
     * @mbg.generated
     */
    public String getCourierPhone() {
        return courierPhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.courier_phone
     *
     * @param courierPhone the value for tpm_delivery_record.courier_phone
     *
     * @mbg.generated
     */
    public void setCourierPhone(String courierPhone) {
        this.courierPhone = courierPhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.delivery_brand
     *
     * @return the value of tpm_delivery_record.delivery_brand
     *
     * @mbg.generated
     */
    public String getDeliveryBrand() {
        return deliveryBrand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.delivery_brand
     *
     * @param deliveryBrand the value for tpm_delivery_record.delivery_brand
     *
     * @mbg.generated
     */
    public void setDeliveryBrand(String deliveryBrand) {
        this.deliveryBrand = deliveryBrand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.delivery_distance
     *
     * @return the value of tpm_delivery_record.delivery_distance
     *
     * @mbg.generated
     */
    public String getDeliveryDistance() {
        return deliveryDistance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.delivery_distance
     *
     * @param deliveryDistance the value for tpm_delivery_record.delivery_distance
     *
     * @mbg.generated
     */
    public void setDeliveryDistance(String deliveryDistance) {
        this.deliveryDistance = deliveryDistance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.delivery_order_no
     *
     * @return the value of tpm_delivery_record.delivery_order_no
     *
     * @mbg.generated
     */
    public String getDeliveryOrderNo() {
        return deliveryOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.delivery_order_no
     *
     * @param deliveryOrderNo the value for tpm_delivery_record.delivery_order_no
     *
     * @mbg.generated
     */
    public void setDeliveryOrderNo(String deliveryOrderNo) {
        this.deliveryOrderNo = deliveryOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.reason
     *
     * @return the value of tpm_delivery_record.reason
     *
     * @mbg.generated
     */
    public String getReason() {
        return reason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.reason
     *
     * @param reason the value for tpm_delivery_record.reason
     *
     * @mbg.generated
     */
    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.from
     *
     * @return the value of tpm_delivery_record.from
     *
     * @mbg.generated
     */
    public Integer getFrom() {
        return from;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.from
     *
     * @param from the value for tpm_delivery_record.from
     *
     * @mbg.generated
     */
    public void setFrom(Integer from) {
        this.from = from;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.add_time
     *
     * @return the value of tpm_delivery_record.add_time
     *
     * @mbg.generated
     */
    public LocalDateTime getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.add_time
     *
     * @param addTime the value for tpm_delivery_record.add_time
     *
     * @mbg.generated
     */
    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.update_time
     *
     * @return the value of tpm_delivery_record.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.update_time
     *
     * @param updateTime the value for tpm_delivery_record.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_delivery_record.deleted
     *
     * @return the value of tpm_delivery_record.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_delivery_record.deleted
     *
     * @param deleted the value for tpm_delivery_record.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", tpmOrderId=").append(tpmOrderId);
        sb.append(", orderId=").append(orderId);
        sb.append(", platform=").append(platform);
        sb.append(", deliveryStatus=").append(deliveryStatus);
        sb.append(", tipFee=").append(tipFee);
        sb.append(", freightFee=").append(freightFee);
        sb.append(", courierName=").append(courierName);
        sb.append(", courierPhone=").append(courierPhone);
        sb.append(", deliveryBrand=").append(deliveryBrand);
        sb.append(", deliveryDistance=").append(deliveryDistance);
        sb.append(", deliveryOrderNo=").append(deliveryOrderNo);
        sb.append(", reason=").append(reason);
        sb.append(", from=").append(from);
        sb.append(", addTime=").append(addTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmDeliveryRecord other = (TpmDeliveryRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTpmOrderId() == null ? other.getTpmOrderId() == null : this.getTpmOrderId().equals(other.getTpmOrderId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getPlatform() == null ? other.getPlatform() == null : this.getPlatform().equals(other.getPlatform()))
            && (this.getDeliveryStatus() == null ? other.getDeliveryStatus() == null : this.getDeliveryStatus().equals(other.getDeliveryStatus()))
            && (this.getTipFee() == null ? other.getTipFee() == null : this.getTipFee().equals(other.getTipFee()))
            && (this.getFreightFee() == null ? other.getFreightFee() == null : this.getFreightFee().equals(other.getFreightFee()))
            && (this.getCourierName() == null ? other.getCourierName() == null : this.getCourierName().equals(other.getCourierName()))
            && (this.getCourierPhone() == null ? other.getCourierPhone() == null : this.getCourierPhone().equals(other.getCourierPhone()))
            && (this.getDeliveryBrand() == null ? other.getDeliveryBrand() == null : this.getDeliveryBrand().equals(other.getDeliveryBrand()))
            && (this.getDeliveryDistance() == null ? other.getDeliveryDistance() == null : this.getDeliveryDistance().equals(other.getDeliveryDistance()))
            && (this.getDeliveryOrderNo() == null ? other.getDeliveryOrderNo() == null : this.getDeliveryOrderNo().equals(other.getDeliveryOrderNo()))
            && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()))
            && (this.getFrom() == null ? other.getFrom() == null : this.getFrom().equals(other.getFrom()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTpmOrderId() == null) ? 0 : getTpmOrderId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getPlatform() == null) ? 0 : getPlatform().hashCode());
        result = prime * result + ((getDeliveryStatus() == null) ? 0 : getDeliveryStatus().hashCode());
        result = prime * result + ((getTipFee() == null) ? 0 : getTipFee().hashCode());
        result = prime * result + ((getFreightFee() == null) ? 0 : getFreightFee().hashCode());
        result = prime * result + ((getCourierName() == null) ? 0 : getCourierName().hashCode());
        result = prime * result + ((getCourierPhone() == null) ? 0 : getCourierPhone().hashCode());
        result = prime * result + ((getDeliveryBrand() == null) ? 0 : getDeliveryBrand().hashCode());
        result = prime * result + ((getDeliveryDistance() == null) ? 0 : getDeliveryDistance().hashCode());
        result = prime * result + ((getDeliveryOrderNo() == null) ? 0 : getDeliveryOrderNo().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getFrom() == null) ? 0 : getFrom().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? IS_DELETED : NOT_DELETED);
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_delivery_record
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        tpmOrderId("tpm_order_id", "tpmOrderId", "INTEGER", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        platform("platform", "platform", "VARCHAR", false),
        deliveryStatus("delivery_status", "deliveryStatus", "VARCHAR", false),
        tipFee("tip_fee", "tipFee", "INTEGER", false),
        freightFee("freight_fee", "freightFee", "INTEGER", false),
        courierName("courier_name", "courierName", "VARCHAR", false),
        courierPhone("courier_phone", "courierPhone", "VARCHAR", false),
        deliveryBrand("delivery_brand", "deliveryBrand", "VARCHAR", false),
        deliveryDistance("delivery_distance", "deliveryDistance", "VARCHAR", false),
        deliveryOrderNo("delivery_order_no", "deliveryOrderNo", "VARCHAR", false),
        reason("reason", "reason", "VARCHAR", false),
        from("from", "from", "INTEGER", true),
        addTime("add_time", "addTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleted("deleted", "deleted", "BIT", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_delivery_record
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}