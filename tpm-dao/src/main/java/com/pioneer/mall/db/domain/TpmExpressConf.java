package com.pioneer.mall.db.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

public class TpmExpressConf {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean NOT_DELETED = false;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean IS_DELETED = true;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.first_weight
     *
     * @mbg.generated
     */
    private BigDecimal firstWeight;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.shipping_fee
     *
     * @mbg.generated
     */
    private BigDecimal shippingFee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.continued_weight
     *
     * @mbg.generated
     */
    private BigDecimal continuedWeight;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.continued_fee
     *
     * @mbg.generated
     */
    private BigDecimal continuedFee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.create_by
     *
     * @mbg.generated
     */
    private Long createBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.update_by
     *
     * @mbg.generated
     */
    private Long updateBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.express_fee_type
     *
     * @mbg.generated
     */
    private Integer expressFeeType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.id
     *
     * @return the value of tpm_express_conf.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.id
     *
     * @param id the value for tpm_express_conf.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.first_weight
     *
     * @return the value of tpm_express_conf.first_weight
     *
     * @mbg.generated
     */
    public BigDecimal getFirstWeight() {
        return firstWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.first_weight
     *
     * @param firstWeight the value for tpm_express_conf.first_weight
     *
     * @mbg.generated
     */
    public void setFirstWeight(BigDecimal firstWeight) {
        this.firstWeight = firstWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.shipping_fee
     *
     * @return the value of tpm_express_conf.shipping_fee
     *
     * @mbg.generated
     */
    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.shipping_fee
     *
     * @param shippingFee the value for tpm_express_conf.shipping_fee
     *
     * @mbg.generated
     */
    public void setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.continued_weight
     *
     * @return the value of tpm_express_conf.continued_weight
     *
     * @mbg.generated
     */
    public BigDecimal getContinuedWeight() {
        return continuedWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.continued_weight
     *
     * @param continuedWeight the value for tpm_express_conf.continued_weight
     *
     * @mbg.generated
     */
    public void setContinuedWeight(BigDecimal continuedWeight) {
        this.continuedWeight = continuedWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.continued_fee
     *
     * @return the value of tpm_express_conf.continued_fee
     *
     * @mbg.generated
     */
    public BigDecimal getContinuedFee() {
        return continuedFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.continued_fee
     *
     * @param continuedFee the value for tpm_express_conf.continued_fee
     *
     * @mbg.generated
     */
    public void setContinuedFee(BigDecimal continuedFee) {
        this.continuedFee = continuedFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.create_by
     *
     * @return the value of tpm_express_conf.create_by
     *
     * @mbg.generated
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.create_by
     *
     * @param createBy the value for tpm_express_conf.create_by
     *
     * @mbg.generated
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.create_time
     *
     * @return the value of tpm_express_conf.create_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.create_time
     *
     * @param createTime the value for tpm_express_conf.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.update_by
     *
     * @return the value of tpm_express_conf.update_by
     *
     * @mbg.generated
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.update_by
     *
     * @param updateBy the value for tpm_express_conf.update_by
     *
     * @mbg.generated
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.update_time
     *
     * @return the value of tpm_express_conf.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.update_time
     *
     * @param updateTime the value for tpm_express_conf.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.deleted
     *
     * @return the value of tpm_express_conf.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.deleted
     *
     * @param deleted the value for tpm_express_conf.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.express_fee_type
     *
     * @return the value of tpm_express_conf.express_fee_type
     *
     * @mbg.generated
     */
    public Integer getExpressFeeType() {
        return expressFeeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.express_fee_type
     *
     * @param expressFeeType the value for tpm_express_conf.express_fee_type
     *
     * @mbg.generated
     */
    public void setExpressFeeType(Integer expressFeeType) {
        this.expressFeeType = expressFeeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", firstWeight=").append(firstWeight);
        sb.append(", shippingFee=").append(shippingFee);
        sb.append(", continuedWeight=").append(continuedWeight);
        sb.append(", continuedFee=").append(continuedFee);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", expressFeeType=").append(expressFeeType);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmExpressConf other = (TpmExpressConf) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFirstWeight() == null ? other.getFirstWeight() == null : this.getFirstWeight().equals(other.getFirstWeight()))
            && (this.getShippingFee() == null ? other.getShippingFee() == null : this.getShippingFee().equals(other.getShippingFee()))
            && (this.getContinuedWeight() == null ? other.getContinuedWeight() == null : this.getContinuedWeight().equals(other.getContinuedWeight()))
            && (this.getContinuedFee() == null ? other.getContinuedFee() == null : this.getContinuedFee().equals(other.getContinuedFee()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getExpressFeeType() == null ? other.getExpressFeeType() == null : this.getExpressFeeType().equals(other.getExpressFeeType()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFirstWeight() == null) ? 0 : getFirstWeight().hashCode());
        result = prime * result + ((getShippingFee() == null) ? 0 : getShippingFee().hashCode());
        result = prime * result + ((getContinuedWeight() == null) ? 0 : getContinuedWeight().hashCode());
        result = prime * result + ((getContinuedFee() == null) ? 0 : getContinuedFee().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getExpressFeeType() == null) ? 0 : getExpressFeeType().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? IS_DELETED : NOT_DELETED);
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        firstWeight("first_weight", "firstWeight", "DECIMAL", false),
        shippingFee("shipping_fee", "shippingFee", "DECIMAL", false),
        continuedWeight("continued_weight", "continuedWeight", "DECIMAL", false),
        continuedFee("continued_fee", "continuedFee", "DECIMAL", false),
        createBy("create_by", "createBy", "BIGINT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateBy("update_by", "updateBy", "BIGINT", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleted("deleted", "deleted", "BIT", false),
        expressFeeType("express_fee_type", "expressFeeType", "INTEGER", false),
        deliveryArea("delivery_area", "deliveryArea", "LONGVARCHAR", false),
        deliveryAreaCode("delivery_area_code", "deliveryAreaCode", "LONGVARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}