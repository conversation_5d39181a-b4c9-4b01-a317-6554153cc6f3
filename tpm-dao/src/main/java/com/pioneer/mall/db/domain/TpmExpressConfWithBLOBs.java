package com.pioneer.mall.db.domain;

import java.util.ArrayList;
import java.util.Arrays;

public class TpmExpressConfWithBLOBs extends TpmExpressConf {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.delivery_area
     *
     * @mbg.generated
     */
    private String deliveryArea;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_express_conf.delivery_area_code
     *
     * @mbg.generated
     */
    private String deliveryAreaCode;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.delivery_area
     *
     * @return the value of tpm_express_conf.delivery_area
     *
     * @mbg.generated
     */
    public String getDeliveryArea() {
        return deliveryArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.delivery_area
     *
     * @param deliveryArea the value for tpm_express_conf.delivery_area
     *
     * @mbg.generated
     */
    public void setDeliveryArea(String deliveryArea) {
        this.deliveryArea = deliveryArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_express_conf.delivery_area_code
     *
     * @return the value of tpm_express_conf.delivery_area_code
     *
     * @mbg.generated
     */
    public String getDeliveryAreaCode() {
        return deliveryAreaCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_express_conf.delivery_area_code
     *
     * @param deliveryAreaCode the value for tpm_express_conf.delivery_area_code
     *
     * @mbg.generated
     */
    public void setDeliveryAreaCode(String deliveryAreaCode) {
        this.deliveryAreaCode = deliveryAreaCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", deliveryArea=").append(deliveryArea);
        sb.append(", deliveryAreaCode=").append(deliveryAreaCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmExpressConfWithBLOBs other = (TpmExpressConfWithBLOBs) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFirstWeight() == null ? other.getFirstWeight() == null : this.getFirstWeight().equals(other.getFirstWeight()))
            && (this.getShippingFee() == null ? other.getShippingFee() == null : this.getShippingFee().equals(other.getShippingFee()))
            && (this.getContinuedWeight() == null ? other.getContinuedWeight() == null : this.getContinuedWeight().equals(other.getContinuedWeight()))
            && (this.getContinuedFee() == null ? other.getContinuedFee() == null : this.getContinuedFee().equals(other.getContinuedFee()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getExpressFeeType() == null ? other.getExpressFeeType() == null : this.getExpressFeeType().equals(other.getExpressFeeType()))
            && (this.getDeliveryArea() == null ? other.getDeliveryArea() == null : this.getDeliveryArea().equals(other.getDeliveryArea()))
            && (this.getDeliveryAreaCode() == null ? other.getDeliveryAreaCode() == null : this.getDeliveryAreaCode().equals(other.getDeliveryAreaCode()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFirstWeight() == null) ? 0 : getFirstWeight().hashCode());
        result = prime * result + ((getShippingFee() == null) ? 0 : getShippingFee().hashCode());
        result = prime * result + ((getContinuedWeight() == null) ? 0 : getContinuedWeight().hashCode());
        result = prime * result + ((getContinuedFee() == null) ? 0 : getContinuedFee().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getExpressFeeType() == null) ? 0 : getExpressFeeType().hashCode());
        result = prime * result + ((getDeliveryArea() == null) ? 0 : getDeliveryArea().hashCode());
        result = prime * result + ((getDeliveryAreaCode() == null) ? 0 : getDeliveryAreaCode().hashCode());
        return result;
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_express_conf
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        firstWeight("first_weight", "firstWeight", "DECIMAL", false),
        shippingFee("shipping_fee", "shippingFee", "DECIMAL", false),
        continuedWeight("continued_weight", "continuedWeight", "DECIMAL", false),
        continuedFee("continued_fee", "continuedFee", "DECIMAL", false),
        createBy("create_by", "createBy", "BIGINT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateBy("update_by", "updateBy", "BIGINT", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleted("deleted", "deleted", "BIT", false),
        expressFeeType("express_fee_type", "expressFeeType", "VARCHAR", false),
        deliveryArea("delivery_area", "deliveryArea", "LONGVARCHAR", false),
        deliveryAreaCode("delivery_area_code", "deliveryAreaCode", "LONGVARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_express_conf
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}