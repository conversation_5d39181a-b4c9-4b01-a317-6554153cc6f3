package com.pioneer.mall.db.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

public class TpmGoods {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean NOT_DELETED = false;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean IS_DELETED = true;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.goods_sn
     *
     * @mbg.generated
     */
    private String goodsSn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.category_id
     *
     * @mbg.generated
     */
    private Integer categoryId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.shop_id
     *
     * @mbg.generated
     */
    private Integer shopId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.brand_id
     *
     * @mbg.generated
     */
    private Integer brandId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.gallery
     *
     * @mbg.generated
     */
    private String[] gallery;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.keywords
     *
     * @mbg.generated
     */
    private String keywords;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.brief
     *
     * @mbg.generated
     */
    private String brief;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.is_on_sale
     *
     * @mbg.generated
     */
    private Boolean isOnSale;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.is_sell_out
     *
     * @mbg.generated
     */
    private Boolean isSellOut;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.sort_order
     *
     * @mbg.generated
     */
    private Short sortOrder;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.pic_url
     *
     * @mbg.generated
     */
    private String picUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.share_url
     *
     * @mbg.generated
     */
    private String shareUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.is_new
     *
     * @mbg.generated
     */
    private Boolean isNew;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.is_hot
     *
     * @mbg.generated
     */
    private Boolean isHot;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.unit
     *
     * @mbg.generated
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.counter_price
     *
     * @mbg.generated
     */
    private BigDecimal counterPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.retail_price
     *
     * @mbg.generated
     */
    private BigDecimal retailPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.inventory_num
     *
     * @mbg.generated
     */
    private Integer inventoryNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.add_time
     *
     * @mbg.generated
     */
    private LocalDateTime addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.browse
     *
     * @mbg.generated
     */
    private Integer browse;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.sales
     *
     * @mbg.generated
     */
    private Integer sales;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.commpany
     *
     * @mbg.generated
     */
    private String commpany;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.wholesale_price
     *
     * @mbg.generated
     */
    private BigDecimal wholesalePrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.approve_status
     *
     * @mbg.generated
     */
    private Byte approveStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.approve_msg
     *
     * @mbg.generated
     */
    private String approveMsg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.brokerage_type
     *
     * @mbg.generated
     */
    private Byte brokerageType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.brokerage_price
     *
     * @mbg.generated
     */
    private BigDecimal brokeragePrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.sale_range
     *
     * @mbg.generated
     */
    private Integer saleRange;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.business_type
     *
     * @mbg.generated
     */
    private Integer businessType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.transport_type
     *
     * @mbg.generated
     */
    private Integer transportType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_goods.detail
     *
     * @mbg.generated
     */
    private String detail;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.id
     *
     * @return the value of tpm_goods.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.id
     *
     * @param id the value for tpm_goods.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.goods_sn
     *
     * @return the value of tpm_goods.goods_sn
     *
     * @mbg.generated
     */
    public String getGoodsSn() {
        return goodsSn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.goods_sn
     *
     * @param goodsSn the value for tpm_goods.goods_sn
     *
     * @mbg.generated
     */
    public void setGoodsSn(String goodsSn) {
        this.goodsSn = goodsSn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.name
     *
     * @return the value of tpm_goods.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.name
     *
     * @param name the value for tpm_goods.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.category_id
     *
     * @return the value of tpm_goods.category_id
     *
     * @mbg.generated
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.category_id
     *
     * @param categoryId the value for tpm_goods.category_id
     *
     * @mbg.generated
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.shop_id
     *
     * @return the value of tpm_goods.shop_id
     *
     * @mbg.generated
     */
    public Integer getShopId() {
        return shopId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.shop_id
     *
     * @param shopId the value for tpm_goods.shop_id
     *
     * @mbg.generated
     */
    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.brand_id
     *
     * @return the value of tpm_goods.brand_id
     *
     * @mbg.generated
     */
    public Integer getBrandId() {
        return brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.brand_id
     *
     * @param brandId the value for tpm_goods.brand_id
     *
     * @mbg.generated
     */
    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.gallery
     *
     * @return the value of tpm_goods.gallery
     *
     * @mbg.generated
     */
    public String[] getGallery() {
        return gallery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.gallery
     *
     * @param gallery the value for tpm_goods.gallery
     *
     * @mbg.generated
     */
    public void setGallery(String[] gallery) {
        this.gallery = gallery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.keywords
     *
     * @return the value of tpm_goods.keywords
     *
     * @mbg.generated
     */
    public String getKeywords() {
        return keywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.keywords
     *
     * @param keywords the value for tpm_goods.keywords
     *
     * @mbg.generated
     */
    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.brief
     *
     * @return the value of tpm_goods.brief
     *
     * @mbg.generated
     */
    public String getBrief() {
        return brief;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.brief
     *
     * @param brief the value for tpm_goods.brief
     *
     * @mbg.generated
     */
    public void setBrief(String brief) {
        this.brief = brief;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.is_on_sale
     *
     * @return the value of tpm_goods.is_on_sale
     *
     * @mbg.generated
     */
    public Boolean getIsOnSale() {
        return isOnSale;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.is_on_sale
     *
     * @param isOnSale the value for tpm_goods.is_on_sale
     *
     * @mbg.generated
     */
    public void setIsOnSale(Boolean isOnSale) {
        this.isOnSale = isOnSale;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.is_sell_out
     *
     * @return the value of tpm_goods.is_sell_out
     *
     * @mbg.generated
     */
    public Boolean getIsSellOut() {
        return isSellOut;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.is_sell_out
     *
     * @param isSellOut the value for tpm_goods.is_sell_out
     *
     * @mbg.generated
     */
    public void setIsSellOut(Boolean isSellOut) {
        this.isSellOut = isSellOut;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.sort_order
     *
     * @return the value of tpm_goods.sort_order
     *
     * @mbg.generated
     */
    public Short getSortOrder() {
        return sortOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.sort_order
     *
     * @param sortOrder the value for tpm_goods.sort_order
     *
     * @mbg.generated
     */
    public void setSortOrder(Short sortOrder) {
        this.sortOrder = sortOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.pic_url
     *
     * @return the value of tpm_goods.pic_url
     *
     * @mbg.generated
     */
    public String getPicUrl() {
        return picUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.pic_url
     *
     * @param picUrl the value for tpm_goods.pic_url
     *
     * @mbg.generated
     */
    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.share_url
     *
     * @return the value of tpm_goods.share_url
     *
     * @mbg.generated
     */
    public String getShareUrl() {
        return shareUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.share_url
     *
     * @param shareUrl the value for tpm_goods.share_url
     *
     * @mbg.generated
     */
    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.is_new
     *
     * @return the value of tpm_goods.is_new
     *
     * @mbg.generated
     */
    public Boolean getIsNew() {
        return isNew;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.is_new
     *
     * @param isNew the value for tpm_goods.is_new
     *
     * @mbg.generated
     */
    public void setIsNew(Boolean isNew) {
        this.isNew = isNew;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.is_hot
     *
     * @return the value of tpm_goods.is_hot
     *
     * @mbg.generated
     */
    public Boolean getIsHot() {
        return isHot;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.is_hot
     *
     * @param isHot the value for tpm_goods.is_hot
     *
     * @mbg.generated
     */
    public void setIsHot(Boolean isHot) {
        this.isHot = isHot;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.unit
     *
     * @return the value of tpm_goods.unit
     *
     * @mbg.generated
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.unit
     *
     * @param unit the value for tpm_goods.unit
     *
     * @mbg.generated
     */
    public void setUnit(String unit) {
        this.unit = unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.counter_price
     *
     * @return the value of tpm_goods.counter_price
     *
     * @mbg.generated
     */
    public BigDecimal getCounterPrice() {
        return counterPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.counter_price
     *
     * @param counterPrice the value for tpm_goods.counter_price
     *
     * @mbg.generated
     */
    public void setCounterPrice(BigDecimal counterPrice) {
        this.counterPrice = counterPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.retail_price
     *
     * @return the value of tpm_goods.retail_price
     *
     * @mbg.generated
     */
    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.retail_price
     *
     * @param retailPrice the value for tpm_goods.retail_price
     *
     * @mbg.generated
     */
    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.inventory_num
     *
     * @return the value of tpm_goods.inventory_num
     *
     * @mbg.generated
     */
    public Integer getInventoryNum() {
        return inventoryNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.inventory_num
     *
     * @param inventoryNum the value for tpm_goods.inventory_num
     *
     * @mbg.generated
     */
    public void setInventoryNum(Integer inventoryNum) {
        this.inventoryNum = inventoryNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.add_time
     *
     * @return the value of tpm_goods.add_time
     *
     * @mbg.generated
     */
    public LocalDateTime getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.add_time
     *
     * @param addTime the value for tpm_goods.add_time
     *
     * @mbg.generated
     */
    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.update_time
     *
     * @return the value of tpm_goods.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.update_time
     *
     * @param updateTime the value for tpm_goods.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.browse
     *
     * @return the value of tpm_goods.browse
     *
     * @mbg.generated
     */
    public Integer getBrowse() {
        return browse;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.browse
     *
     * @param browse the value for tpm_goods.browse
     *
     * @mbg.generated
     */
    public void setBrowse(Integer browse) {
        this.browse = browse;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.sales
     *
     * @return the value of tpm_goods.sales
     *
     * @mbg.generated
     */
    public Integer getSales() {
        return sales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.sales
     *
     * @param sales the value for tpm_goods.sales
     *
     * @mbg.generated
     */
    public void setSales(Integer sales) {
        this.sales = sales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.deleted
     *
     * @return the value of tpm_goods.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.deleted
     *
     * @param deleted the value for tpm_goods.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.commpany
     *
     * @return the value of tpm_goods.commpany
     *
     * @mbg.generated
     */
    public String getCommpany() {
        return commpany;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.commpany
     *
     * @param commpany the value for tpm_goods.commpany
     *
     * @mbg.generated
     */
    public void setCommpany(String commpany) {
        this.commpany = commpany;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.wholesale_price
     *
     * @return the value of tpm_goods.wholesale_price
     *
     * @mbg.generated
     */
    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.wholesale_price
     *
     * @param wholesalePrice the value for tpm_goods.wholesale_price
     *
     * @mbg.generated
     */
    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.approve_status
     *
     * @return the value of tpm_goods.approve_status
     *
     * @mbg.generated
     */
    public Byte getApproveStatus() {
        return approveStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.approve_status
     *
     * @param approveStatus the value for tpm_goods.approve_status
     *
     * @mbg.generated
     */
    public void setApproveStatus(Byte approveStatus) {
        this.approveStatus = approveStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.approve_msg
     *
     * @return the value of tpm_goods.approve_msg
     *
     * @mbg.generated
     */
    public String getApproveMsg() {
        return approveMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.approve_msg
     *
     * @param approveMsg the value for tpm_goods.approve_msg
     *
     * @mbg.generated
     */
    public void setApproveMsg(String approveMsg) {
        this.approveMsg = approveMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.brokerage_type
     *
     * @return the value of tpm_goods.brokerage_type
     *
     * @mbg.generated
     */
    public Byte getBrokerageType() {
        return brokerageType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.brokerage_type
     *
     * @param brokerageType the value for tpm_goods.brokerage_type
     *
     * @mbg.generated
     */
    public void setBrokerageType(Byte brokerageType) {
        this.brokerageType = brokerageType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.brokerage_price
     *
     * @return the value of tpm_goods.brokerage_price
     *
     * @mbg.generated
     */
    public BigDecimal getBrokeragePrice() {
        return brokeragePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.brokerage_price
     *
     * @param brokeragePrice the value for tpm_goods.brokerage_price
     *
     * @mbg.generated
     */
    public void setBrokeragePrice(BigDecimal brokeragePrice) {
        this.brokeragePrice = brokeragePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.sale_range
     *
     * @return the value of tpm_goods.sale_range
     *
     * @mbg.generated
     */
    public Integer getSaleRange() {
        return saleRange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.sale_range
     *
     * @param saleRange the value for tpm_goods.sale_range
     *
     * @mbg.generated
     */
    public void setSaleRange(Integer saleRange) {
        this.saleRange = saleRange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.business_type
     *
     * @return the value of tpm_goods.business_type
     *
     * @mbg.generated
     */
    public Integer getBusinessType() {
        return businessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.business_type
     *
     * @param businessType the value for tpm_goods.business_type
     *
     * @mbg.generated
     */
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.transport_type
     *
     * @return the value of tpm_goods.transport_type
     *
     * @mbg.generated
     */
    public Integer getTransportType() {
        return transportType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.transport_type
     *
     * @param transportType the value for tpm_goods.transport_type
     *
     * @mbg.generated
     */
    public void setTransportType(Integer transportType) {
        this.transportType = transportType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_goods.detail
     *
     * @return the value of tpm_goods.detail
     *
     * @mbg.generated
     */
    public String getDetail() {
        return detail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_goods.detail
     *
     * @param detail the value for tpm_goods.detail
     *
     * @mbg.generated
     */
    public void setDetail(String detail) {
        this.detail = detail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", goodsSn=").append(goodsSn);
        sb.append(", name=").append(name);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", shopId=").append(shopId);
        sb.append(", brandId=").append(brandId);
        sb.append(", gallery=").append(gallery);
        sb.append(", keywords=").append(keywords);
        sb.append(", brief=").append(brief);
        sb.append(", isOnSale=").append(isOnSale);
        sb.append(", isSellOut=").append(isSellOut);
        sb.append(", sortOrder=").append(sortOrder);
        sb.append(", picUrl=").append(picUrl);
        sb.append(", shareUrl=").append(shareUrl);
        sb.append(", isNew=").append(isNew);
        sb.append(", isHot=").append(isHot);
        sb.append(", unit=").append(unit);
        sb.append(", counterPrice=").append(counterPrice);
        sb.append(", retailPrice=").append(retailPrice);
        sb.append(", inventoryNum=").append(inventoryNum);
        sb.append(", addTime=").append(addTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", browse=").append(browse);
        sb.append(", sales=").append(sales);
        sb.append(", deleted=").append(deleted);
        sb.append(", commpany=").append(commpany);
        sb.append(", wholesalePrice=").append(wholesalePrice);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", approveMsg=").append(approveMsg);
        sb.append(", brokerageType=").append(brokerageType);
        sb.append(", brokeragePrice=").append(brokeragePrice);
        sb.append(", saleRange=").append(saleRange);
        sb.append(", businessType=").append(businessType);
        sb.append(", transportType=").append(transportType);
        sb.append(", detail=").append(detail);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmGoods other = (TpmGoods) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGoodsSn() == null ? other.getGoodsSn() == null : this.getGoodsSn().equals(other.getGoodsSn()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getCategoryId() == null ? other.getCategoryId() == null : this.getCategoryId().equals(other.getCategoryId()))
            && (this.getBrandId() == null ? other.getBrandId() == null : this.getBrandId().equals(other.getBrandId()))
            && (this.getGallery() == null ? other.getGallery() == null : this.getGallery().equals(other.getGallery()))
            && (this.getKeywords() == null ? other.getKeywords() == null : this.getKeywords().equals(other.getKeywords()))
            && (this.getBrief() == null ? other.getBrief() == null : this.getBrief().equals(other.getBrief()))
            && (this.getIsOnSale() == null ? other.getIsOnSale() == null : this.getIsOnSale().equals(other.getIsOnSale()))
            && (this.getIsSellOut() == null ? other.getIsSellOut() == null : this.getIsSellOut().equals(other.getIsSellOut()))
            && (this.getSortOrder() == null ? other.getSortOrder() == null : this.getSortOrder().equals(other.getSortOrder()))
            && (this.getPicUrl() == null ? other.getPicUrl() == null : this.getPicUrl().equals(other.getPicUrl()))
            && (this.getShareUrl() == null ? other.getShareUrl() == null : this.getShareUrl().equals(other.getShareUrl()))
            && (this.getIsNew() == null ? other.getIsNew() == null : this.getIsNew().equals(other.getIsNew()))
            && (this.getIsHot() == null ? other.getIsHot() == null : this.getIsHot().equals(other.getIsHot()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getCounterPrice() == null ? other.getCounterPrice() == null : this.getCounterPrice().equals(other.getCounterPrice()))
            && (this.getRetailPrice() == null ? other.getRetailPrice() == null : this.getRetailPrice().equals(other.getRetailPrice()))
            && (this.getInventoryNum() == null ? other.getInventoryNum() == null : this.getInventoryNum().equals(other.getInventoryNum()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getBrowse() == null ? other.getBrowse() == null : this.getBrowse().equals(other.getBrowse()))
            && (this.getSales() == null ? other.getSales() == null : this.getSales().equals(other.getSales()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getCommpany() == null ? other.getCommpany() == null : this.getCommpany().equals(other.getCommpany()))
            && (this.getWholesalePrice() == null ? other.getWholesalePrice() == null : this.getWholesalePrice().equals(other.getWholesalePrice()))
            && (this.getApproveStatus() == null ? other.getApproveStatus() == null : this.getApproveStatus().equals(other.getApproveStatus()))
            && (this.getApproveMsg() == null ? other.getApproveMsg() == null : this.getApproveMsg().equals(other.getApproveMsg()))
            && (this.getBrokerageType() == null ? other.getBrokerageType() == null : this.getBrokerageType().equals(other.getBrokerageType()))
            && (this.getBrokeragePrice() == null ? other.getBrokeragePrice() == null : this.getBrokeragePrice().equals(other.getBrokeragePrice()))
            && (this.getSaleRange() == null ? other.getSaleRange() == null : this.getSaleRange().equals(other.getSaleRange()))
            && (this.getBusinessType() == null ? other.getBusinessType() == null : this.getBusinessType().equals(other.getBusinessType()))
            && (this.getTransportType() == null ? other.getTransportType() == null : this.getTransportType().equals(other.getTransportType()))
            && (this.getDetail() == null ? other.getDetail() == null : this.getDetail().equals(other.getDetail()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGoodsSn() == null) ? 0 : getGoodsSn().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getBrandId() == null) ? 0 : getBrandId().hashCode());
        result = prime * result + (Arrays.hashCode(getGallery()));
        result = prime * result + ((getKeywords() == null) ? 0 : getKeywords().hashCode());
        result = prime * result + ((getBrief() == null) ? 0 : getBrief().hashCode());
        result = prime * result + ((getIsOnSale() == null) ? 0 : getIsOnSale().hashCode());
        result = prime * result + ((getIsSellOut() == null) ? 0 : getIsSellOut().hashCode());
        result = prime * result + ((getSortOrder() == null) ? 0 : getSortOrder().hashCode());
        result = prime * result + ((getPicUrl() == null) ? 0 : getPicUrl().hashCode());
        result = prime * result + ((getShareUrl() == null) ? 0 : getShareUrl().hashCode());
        result = prime * result + ((getIsNew() == null) ? 0 : getIsNew().hashCode());
        result = prime * result + ((getIsHot() == null) ? 0 : getIsHot().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getCounterPrice() == null) ? 0 : getCounterPrice().hashCode());
        result = prime * result + ((getRetailPrice() == null) ? 0 : getRetailPrice().hashCode());
        result = prime * result + ((getInventoryNum() == null) ? 0 : getInventoryNum().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getBrowse() == null) ? 0 : getBrowse().hashCode());
        result = prime * result + ((getSales() == null) ? 0 : getSales().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getCommpany() == null) ? 0 : getCommpany().hashCode());
        result = prime * result + ((getWholesalePrice() == null) ? 0 : getWholesalePrice().hashCode());
        result = prime * result + ((getApproveStatus() == null) ? 0 : getApproveStatus().hashCode());
        result = prime * result + ((getApproveMsg() == null) ? 0 : getApproveMsg().hashCode());
        result = prime * result + ((getBrokerageType() == null) ? 0 : getBrokerageType().hashCode());
        result = prime * result + ((getBrokeragePrice() == null) ? 0 : getBrokeragePrice().hashCode());
        result = prime * result + ((getSaleRange() == null) ? 0 : getSaleRange().hashCode());
        result = prime * result + ((getBusinessType() == null) ? 0 : getBusinessType().hashCode());
        result = prime * result + ((getTransportType() == null) ? 0 : getTransportType().hashCode());
        result = prime * result + ((getDetail() == null) ? 0 : getDetail().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? IS_DELETED : NOT_DELETED);
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_goods
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        goodsSn("goods_sn", "goodsSn", "VARCHAR", false),
        name("name", "name", "VARCHAR", true),
        categoryId("category_id", "categoryId", "INTEGER", false),
        shopId("shop_id", "shopId", "INTEGER", false),
        brandId("brand_id", "brandId", "INTEGER", false),
        gallery("gallery", "gallery", "VARCHAR", false),
        keywords("keywords", "keywords", "VARCHAR", false),
        brief("brief", "brief", "VARCHAR", false),
        isOnSale("is_on_sale", "isOnSale", "BIT", false),
        isSellOut("is_sell_out", "isSellOut", "BIT", false),
        sortOrder("sort_order", "sortOrder", "SMALLINT", false),
        picUrl("pic_url", "picUrl", "VARCHAR", false),
        shareUrl("share_url", "shareUrl", "VARCHAR", false),
        isNew("is_new", "isNew", "BIT", false),
        isHot("is_hot", "isHot", "BIT", false),
        unit("unit", "unit", "VARCHAR", false),
        counterPrice("counter_price", "counterPrice", "DECIMAL", false),
        retailPrice("retail_price", "retailPrice", "DECIMAL", false),
        inventoryNum("inventory_num", "inventoryNum", "INTEGER", false),
        addTime("add_time", "addTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        browse("browse", "browse", "INTEGER", true),
        sales("sales", "sales", "INTEGER", false),
        deleted("deleted", "deleted", "BIT", false),
        commpany("commpany", "commpany", "VARCHAR", false),
        wholesalePrice("wholesale_price", "wholesalePrice", "DECIMAL", false),
        approveStatus("approve_status", "approveStatus", "TINYINT", false),
        approveMsg("approve_msg", "approveMsg", "VARCHAR", false),
        brokerageType("brokerage_type", "brokerageType", "TINYINT", false),
        brokeragePrice("brokerage_price", "brokeragePrice", "DECIMAL", false),
        saleRange("sale_range", "saleRange", "INTEGER", false),
        businessType("business_type", "businessType", "INTEGER", false),
        transportType("transport_type", "transportType", "INTEGER", false),
        detail("detail", "detail", "LONGVARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_goods
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}