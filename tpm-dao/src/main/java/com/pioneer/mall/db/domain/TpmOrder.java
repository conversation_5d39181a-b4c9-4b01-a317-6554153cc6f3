package com.pioneer.mall.db.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

public class TpmOrder {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean NOT_DELETED = false;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean IS_DELETED = true;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.shop_id
     *
     * @mbg.generated
     */
    private Integer shopId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.user_id
     *
     * @mbg.generated
     */
    private Integer userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.order_sn
     *
     * @mbg.generated
     */
    private String orderSn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.order_status
     *
     * @mbg.generated
     */
    private Short orderStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.meal_code
     *
     * @mbg.generated
     */
    private String mealCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.meal_qr_code
     *
     * @mbg.generated
     */
    private String mealQrCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.meal_pickup_status
     *
     * @mbg.generated
     */
    private Boolean mealPickupStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.meal_pickup_time
     *
     * @mbg.generated
     */
    private LocalDateTime mealPickupTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.consignee
     *
     * @mbg.generated
     */
    private String consignee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.mobile
     *
     * @mbg.generated
     */
    private String mobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.address
     *
     * @mbg.generated
     */
    private String address;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.message
     *
     * @mbg.generated
     */
    private String message;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.goods_price
     *
     * @mbg.generated
     */
    private BigDecimal goodsPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.freight_price
     *
     * @mbg.generated
     */
    private BigDecimal freightPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.packing_fee
     *
     * @mbg.generated
     */
    private BigDecimal packingFee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.coupon_price
     *
     * @mbg.generated
     */
    private BigDecimal couponPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.integral_price
     *
     * @mbg.generated
     */
    private BigDecimal integralPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.groupon_price
     *
     * @mbg.generated
     */
    private BigDecimal grouponPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.order_price
     *
     * @mbg.generated
     */
    private BigDecimal orderPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.actual_price
     *
     * @mbg.generated
     */
    private BigDecimal actualPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.pay_id
     *
     * @mbg.generated
     */
    private String payId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.pay_time
     *
     * @mbg.generated
     */
    private LocalDateTime payTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.ship_sn
     *
     * @mbg.generated
     */
    private String shipSn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.ship_channel
     *
     * @mbg.generated
     */
    private String shipChannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.ship_time
     *
     * @mbg.generated
     */
    private LocalDateTime shipTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.confirm_time
     *
     * @mbg.generated
     */
    private LocalDateTime confirmTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.comments
     *
     * @mbg.generated
     */
    private Short comments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.end_time
     *
     * @mbg.generated
     */
    private LocalDateTime endTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.add_time
     *
     * @mbg.generated
     */
    private LocalDateTime addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.settlement_money
     *
     * @mbg.generated
     */
    private BigDecimal settlementMoney;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.settlement_status
     *
     * @mbg.generated
     */
    private Boolean settlementStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.freight_type
     *
     * @mbg.generated
     */
    private Byte freightType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.share_user_id
     *
     * @mbg.generated
     */
    private Integer shareUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.fetch_code
     *
     * @mbg.generated
     */
    private String fetchCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.create_user_id
     *
     * @mbg.generated
     */
    private Integer createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.gift_send_time
     *
     * @mbg.generated
     */
    private LocalDateTime giftSendTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.gift_receive_time
     *
     * @mbg.generated
     */
    private LocalDateTime giftReceiveTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.pay_type
     *
     * @mbg.generated
     */
    private String payType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.refund_status
     *
     * @mbg.generated
     */
    private String refundStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.lng_and_Lat
     *
     * @mbg.generated
     */
    private String lngAndLat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.delivery_status
     *
     * @mbg.generated
     */
    private String deliveryStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.delivery_order_id
     *
     * @mbg.generated
     */
    private String deliveryOrderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_order.business_type
     *
     * @mbg.generated
     */
    private Integer businessType;

    /**
     * 预约时间字符串（HH:mm格式）
     * 用于保存用户输入的原始预约时间格式
     * 例如："20:00" 表示晚上8点
     */
    private String scheduledTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.id
     *
     * @return the value of tpm_order.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.id
     *
     * @param id the value for tpm_order.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.shop_id
     *
     * @return the value of tpm_order.shop_id
     *
     * @mbg.generated
     */
    public Integer getShopId() {
        return shopId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.shop_id
     *
     * @param shopId the value for tpm_order.shop_id
     *
     * @mbg.generated
     */
    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.user_id
     *
     * @return the value of tpm_order.user_id
     *
     * @mbg.generated
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.user_id
     *
     * @param userId the value for tpm_order.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.order_sn
     *
     * @return the value of tpm_order.order_sn
     *
     * @mbg.generated
     */
    public String getOrderSn() {
        return orderSn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.order_sn
     *
     * @param orderSn the value for tpm_order.order_sn
     *
     * @mbg.generated
     */
    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.order_status
     *
     * @return the value of tpm_order.order_status
     *
     * @mbg.generated
     */
    public Short getOrderStatus() {
        return orderStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.order_status
     *
     * @param orderStatus the value for tpm_order.order_status
     *
     * @mbg.generated
     */
    public void setOrderStatus(Short orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.meal_code
     *
     * @return the value of tpm_order.meal_code
     *
     * @mbg.generated
     */
    public String getMealCode() {
        return mealCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.meal_code
     *
     * @param mealCode the value for tpm_order.meal_code
     *
     * @mbg.generated
     */
    public void setMealCode(String mealCode) {
        this.mealCode = mealCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.meal_qr_code
     *
     * @return the value of tpm_order.meal_qr_code
     *
     * @mbg.generated
     */
    public String getMealQrCode() {
        return mealQrCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.meal_qr_code
     *
     * @param mealQrCode the value for tpm_order.meal_qr_code
     *
     * @mbg.generated
     */
    public void setMealQrCode(String mealQrCode) {
        this.mealQrCode = mealQrCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.meal_pickup_status
     *
     * @return the value of tpm_order.meal_pickup_status
     *
     * @mbg.generated
     */
    public Boolean getMealPickupStatus() {
        return mealPickupStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.meal_pickup_status
     *
     * @param mealPickupStatus the value for tpm_order.meal_pickup_status
     *
     * @mbg.generated
     */
    public void setMealPickupStatus(Boolean mealPickupStatus) {
        this.mealPickupStatus = mealPickupStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.meal_pickup_time
     *
     * @return the value of tpm_order.meal_pickup_time
     *
     * @mbg.generated
     */
    public LocalDateTime getMealPickupTime() {
        return mealPickupTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.meal_pickup_time
     *
     * @param mealPickupTime the value for tpm_order.meal_pickup_time
     *
     * @mbg.generated
     */
    public void setMealPickupTime(LocalDateTime mealPickupTime) {
        this.mealPickupTime = mealPickupTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.consignee
     *
     * @return the value of tpm_order.consignee
     *
     * @mbg.generated
     */
    public String getConsignee() {
        return consignee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.consignee
     *
     * @param consignee the value for tpm_order.consignee
     *
     * @mbg.generated
     */
    public void setConsignee(String consignee) {
        this.consignee = consignee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.mobile
     *
     * @return the value of tpm_order.mobile
     *
     * @mbg.generated
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.mobile
     *
     * @param mobile the value for tpm_order.mobile
     *
     * @mbg.generated
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.address
     *
     * @return the value of tpm_order.address
     *
     * @mbg.generated
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.address
     *
     * @param address the value for tpm_order.address
     *
     * @mbg.generated
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.message
     *
     * @return the value of tpm_order.message
     *
     * @mbg.generated
     */
    public String getMessage() {
        return message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.message
     *
     * @param message the value for tpm_order.message
     *
     * @mbg.generated
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.goods_price
     *
     * @return the value of tpm_order.goods_price
     *
     * @mbg.generated
     */
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.goods_price
     *
     * @param goodsPrice the value for tpm_order.goods_price
     *
     * @mbg.generated
     */
    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.freight_price
     *
     * @return the value of tpm_order.freight_price
     *
     * @mbg.generated
     */
    public BigDecimal getFreightPrice() {
        return freightPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.freight_price
     *
     * @param freightPrice the value for tpm_order.freight_price
     *
     * @mbg.generated
     */
    public void setFreightPrice(BigDecimal freightPrice) {
        this.freightPrice = freightPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.packing_fee
     *
     * @return the value of tpm_order.packing_fee
     *
     * @mbg.generated
     */
    public BigDecimal getPackingFee() {
        return packingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.packing_fee
     *
     * @param packingFee the value for tpm_order.packing_fee
     *
     * @mbg.generated
     */
    public void setPackingFee(BigDecimal packingFee) {
        this.packingFee = packingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.coupon_price
     *
     * @return the value of tpm_order.coupon_price
     *
     * @mbg.generated
     */
    public BigDecimal getCouponPrice() {
        return couponPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.coupon_price
     *
     * @param couponPrice the value for tpm_order.coupon_price
     *
     * @mbg.generated
     */
    public void setCouponPrice(BigDecimal couponPrice) {
        this.couponPrice = couponPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.integral_price
     *
     * @return the value of tpm_order.integral_price
     *
     * @mbg.generated
     */
    public BigDecimal getIntegralPrice() {
        return integralPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.integral_price
     *
     * @param integralPrice the value for tpm_order.integral_price
     *
     * @mbg.generated
     */
    public void setIntegralPrice(BigDecimal integralPrice) {
        this.integralPrice = integralPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.groupon_price
     *
     * @return the value of tpm_order.groupon_price
     *
     * @mbg.generated
     */
    public BigDecimal getGrouponPrice() {
        return grouponPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.groupon_price
     *
     * @param grouponPrice the value for tpm_order.groupon_price
     *
     * @mbg.generated
     */
    public void setGrouponPrice(BigDecimal grouponPrice) {
        this.grouponPrice = grouponPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.order_price
     *
     * @return the value of tpm_order.order_price
     *
     * @mbg.generated
     */
    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.order_price
     *
     * @param orderPrice the value for tpm_order.order_price
     *
     * @mbg.generated
     */
    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.actual_price
     *
     * @return the value of tpm_order.actual_price
     *
     * @mbg.generated
     */
    public BigDecimal getActualPrice() {
        return actualPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.actual_price
     *
     * @param actualPrice the value for tpm_order.actual_price
     *
     * @mbg.generated
     */
    public void setActualPrice(BigDecimal actualPrice) {
        this.actualPrice = actualPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.pay_id
     *
     * @return the value of tpm_order.pay_id
     *
     * @mbg.generated
     */
    public String getPayId() {
        return payId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.pay_id
     *
     * @param payId the value for tpm_order.pay_id
     *
     * @mbg.generated
     */
    public void setPayId(String payId) {
        this.payId = payId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.pay_time
     *
     * @return the value of tpm_order.pay_time
     *
     * @mbg.generated
     */
    public LocalDateTime getPayTime() {
        return payTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.pay_time
     *
     * @param payTime the value for tpm_order.pay_time
     *
     * @mbg.generated
     */
    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.ship_sn
     *
     * @return the value of tpm_order.ship_sn
     *
     * @mbg.generated
     */
    public String getShipSn() {
        return shipSn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.ship_sn
     *
     * @param shipSn the value for tpm_order.ship_sn
     *
     * @mbg.generated
     */
    public void setShipSn(String shipSn) {
        this.shipSn = shipSn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.ship_channel
     *
     * @return the value of tpm_order.ship_channel
     *
     * @mbg.generated
     */
    public String getShipChannel() {
        return shipChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.ship_channel
     *
     * @param shipChannel the value for tpm_order.ship_channel
     *
     * @mbg.generated
     */
    public void setShipChannel(String shipChannel) {
        this.shipChannel = shipChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.ship_time
     *
     * @return the value of tpm_order.ship_time
     *
     * @mbg.generated
     */
    public LocalDateTime getShipTime() {
        return shipTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.ship_time
     *
     * @param shipTime the value for tpm_order.ship_time
     *
     * @mbg.generated
     */
    public void setShipTime(LocalDateTime shipTime) {
        this.shipTime = shipTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.confirm_time
     *
     * @return the value of tpm_order.confirm_time
     *
     * @mbg.generated
     */
    public LocalDateTime getConfirmTime() {
        return confirmTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.confirm_time
     *
     * @param confirmTime the value for tpm_order.confirm_time
     *
     * @mbg.generated
     */
    public void setConfirmTime(LocalDateTime confirmTime) {
        this.confirmTime = confirmTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.comments
     *
     * @return the value of tpm_order.comments
     *
     * @mbg.generated
     */
    public Short getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.comments
     *
     * @param comments the value for tpm_order.comments
     *
     * @mbg.generated
     */
    public void setComments(Short comments) {
        this.comments = comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.end_time
     *
     * @return the value of tpm_order.end_time
     *
     * @mbg.generated
     */
    public LocalDateTime getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.end_time
     *
     * @param endTime the value for tpm_order.end_time
     *
     * @mbg.generated
     */
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.add_time
     *
     * @return the value of tpm_order.add_time
     *
     * @mbg.generated
     */
    public LocalDateTime getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.add_time
     *
     * @param addTime the value for tpm_order.add_time
     *
     * @mbg.generated
     */
    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.update_time
     *
     * @return the value of tpm_order.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.update_time
     *
     * @param updateTime the value for tpm_order.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.deleted
     *
     * @return the value of tpm_order.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.deleted
     *
     * @param deleted the value for tpm_order.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.settlement_money
     *
     * @return the value of tpm_order.settlement_money
     *
     * @mbg.generated
     */
    public BigDecimal getSettlementMoney() {
        return settlementMoney;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.settlement_money
     *
     * @param settlementMoney the value for tpm_order.settlement_money
     *
     * @mbg.generated
     */
    public void setSettlementMoney(BigDecimal settlementMoney) {
        this.settlementMoney = settlementMoney;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.settlement_status
     *
     * @return the value of tpm_order.settlement_status
     *
     * @mbg.generated
     */
    public Boolean getSettlementStatus() {
        return settlementStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.settlement_status
     *
     * @param settlementStatus the value for tpm_order.settlement_status
     *
     * @mbg.generated
     */
    public void setSettlementStatus(Boolean settlementStatus) {
        this.settlementStatus = settlementStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.freight_type
     *
     * @return the value of tpm_order.freight_type
     *
     * @mbg.generated
     */
    public Byte getFreightType() {
        return freightType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.freight_type
     *
     * @param freightType the value for tpm_order.freight_type
     *
     * @mbg.generated
     */
    public void setFreightType(Byte freightType) {
        this.freightType = freightType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.share_user_id
     *
     * @return the value of tpm_order.share_user_id
     *
     * @mbg.generated
     */
    public Integer getShareUserId() {
        return shareUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.share_user_id
     *
     * @param shareUserId the value for tpm_order.share_user_id
     *
     * @mbg.generated
     */
    public void setShareUserId(Integer shareUserId) {
        this.shareUserId = shareUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.fetch_code
     *
     * @return the value of tpm_order.fetch_code
     *
     * @mbg.generated
     */
    public String getFetchCode() {
        return fetchCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.fetch_code
     *
     * @param fetchCode the value for tpm_order.fetch_code
     *
     * @mbg.generated
     */
    public void setFetchCode(String fetchCode) {
        this.fetchCode = fetchCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.create_user_id
     *
     * @return the value of tpm_order.create_user_id
     *
     * @mbg.generated
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.create_user_id
     *
     * @param createUserId the value for tpm_order.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.gift_send_time
     *
     * @return the value of tpm_order.gift_send_time
     *
     * @mbg.generated
     */
    public LocalDateTime getGiftSendTime() {
        return giftSendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.gift_send_time
     *
     * @param giftSendTime the value for tpm_order.gift_send_time
     *
     * @mbg.generated
     */
    public void setGiftSendTime(LocalDateTime giftSendTime) {
        this.giftSendTime = giftSendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.gift_receive_time
     *
     * @return the value of tpm_order.gift_receive_time
     *
     * @mbg.generated
     */
    public LocalDateTime getGiftReceiveTime() {
        return giftReceiveTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.gift_receive_time
     *
     * @param giftReceiveTime the value for tpm_order.gift_receive_time
     *
     * @mbg.generated
     */
    public void setGiftReceiveTime(LocalDateTime giftReceiveTime) {
        this.giftReceiveTime = giftReceiveTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.pay_type
     *
     * @return the value of tpm_order.pay_type
     *
     * @mbg.generated
     */
    public String getPayType() {
        return payType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.pay_type
     *
     * @param payType the value for tpm_order.pay_type
     *
     * @mbg.generated
     */
    public void setPayType(String payType) {
        this.payType = payType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.refund_status
     *
     * @return the value of tpm_order.refund_status
     *
     * @mbg.generated
     */
    public String getRefundStatus() {
        return refundStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.refund_status
     *
     * @param refundStatus the value for tpm_order.refund_status
     *
     * @mbg.generated
     */
    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.lng_and_Lat
     *
     * @return the value of tpm_order.lng_and_Lat
     *
     * @mbg.generated
     */
    public String getLngAndLat() {
        return lngAndLat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.lng_and_Lat
     *
     * @param lngAndLat the value for tpm_order.lng_and_Lat
     *
     * @mbg.generated
     */
    public void setLngAndLat(String lngAndLat) {
        this.lngAndLat = lngAndLat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.delivery_status
     *
     * @return the value of tpm_order.delivery_status
     *
     * @mbg.generated
     */
    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.delivery_status
     *
     * @param deliveryStatus the value for tpm_order.delivery_status
     *
     * @mbg.generated
     */
    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.delivery_order_id
     *
     * @return the value of tpm_order.delivery_order_id
     *
     * @mbg.generated
     */
    public String getDeliveryOrderId() {
        return deliveryOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.delivery_order_id
     *
     * @param deliveryOrderId the value for tpm_order.delivery_order_id
     *
     * @mbg.generated
     */
    public void setDeliveryOrderId(String deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_order.business_type
     *
     * @return the value of tpm_order.business_type
     *
     * @mbg.generated
     */
    public Integer getBusinessType() {
        return businessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_order.business_type
     *
     * @param businessType the value for tpm_order.business_type
     *
     * @mbg.generated
     */
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取预约时间字符串
     *
     * @return 预约时间字符串（HH:mm格式）
     */
    public String getScheduledTime() {
        return scheduledTime;
    }

    /**
     * 设置预约时间字符串
     *
     * @param scheduledTime 预约时间字符串（HH:mm格式）
     */
    public void setScheduledTime(String scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", shopId=").append(shopId);
        sb.append(", userId=").append(userId);
        sb.append(", orderSn=").append(orderSn);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", mealCode=").append(mealCode);
        sb.append(", mealQrCode=").append(mealQrCode);
        sb.append(", mealPickupStatus=").append(mealPickupStatus);
        sb.append(", mealPickupTime=").append(mealPickupTime);
        sb.append(", consignee=").append(consignee);
        sb.append(", mobile=").append(mobile);
        sb.append(", address=").append(address);
        sb.append(", message=").append(message);
        sb.append(", goodsPrice=").append(goodsPrice);
        sb.append(", freightPrice=").append(freightPrice);
        sb.append(", packingFee=").append(packingFee);
        sb.append(", couponPrice=").append(couponPrice);
        sb.append(", integralPrice=").append(integralPrice);
        sb.append(", grouponPrice=").append(grouponPrice);
        sb.append(", orderPrice=").append(orderPrice);
        sb.append(", actualPrice=").append(actualPrice);
        sb.append(", payId=").append(payId);
        sb.append(", payTime=").append(payTime);
        sb.append(", shipSn=").append(shipSn);
        sb.append(", shipChannel=").append(shipChannel);
        sb.append(", shipTime=").append(shipTime);
        sb.append(", confirmTime=").append(confirmTime);
        sb.append(", comments=").append(comments);
        sb.append(", endTime=").append(endTime);
        sb.append(", addTime=").append(addTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", settlementMoney=").append(settlementMoney);
        sb.append(", settlementStatus=").append(settlementStatus);
        sb.append(", freightType=").append(freightType);
        sb.append(", shareUserId=").append(shareUserId);
        sb.append(", fetchCode=").append(fetchCode);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", giftSendTime=").append(giftSendTime);
        sb.append(", giftReceiveTime=").append(giftReceiveTime);
        sb.append(", payType=").append(payType);
        sb.append(", refundStatus=").append(refundStatus);
        sb.append(", lngAndLat=").append(lngAndLat);
        sb.append(", deliveryStatus=").append(deliveryStatus);
        sb.append(", deliveryOrderId=").append(deliveryOrderId);
        sb.append(", businessType=").append(businessType);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmOrder other = (TpmOrder) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getOrderSn() == null ? other.getOrderSn() == null : this.getOrderSn().equals(other.getOrderSn()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getMealCode() == null ? other.getMealCode() == null : this.getMealCode().equals(other.getMealCode()))
            && (this.getMealQrCode() == null ? other.getMealQrCode() == null : this.getMealQrCode().equals(other.getMealQrCode()))
            && (this.getMealPickupStatus() == null ? other.getMealPickupStatus() == null : this.getMealPickupStatus().equals(other.getMealPickupStatus()))
            && (this.getMealPickupTime() == null ? other.getMealPickupTime() == null : this.getMealPickupTime().equals(other.getMealPickupTime()))
            && (this.getConsignee() == null ? other.getConsignee() == null : this.getConsignee().equals(other.getConsignee()))
            && (this.getMobile() == null ? other.getMobile() == null : this.getMobile().equals(other.getMobile()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getMessage() == null ? other.getMessage() == null : this.getMessage().equals(other.getMessage()))
            && (this.getGoodsPrice() == null ? other.getGoodsPrice() == null : this.getGoodsPrice().equals(other.getGoodsPrice()))
            && (this.getFreightPrice() == null ? other.getFreightPrice() == null : this.getFreightPrice().equals(other.getFreightPrice()))
            && (this.getPackingFee() == null ? other.getPackingFee() == null : this.getPackingFee().equals(other.getPackingFee()))
            && (this.getCouponPrice() == null ? other.getCouponPrice() == null : this.getCouponPrice().equals(other.getCouponPrice()))
            && (this.getIntegralPrice() == null ? other.getIntegralPrice() == null : this.getIntegralPrice().equals(other.getIntegralPrice()))
            && (this.getGrouponPrice() == null ? other.getGrouponPrice() == null : this.getGrouponPrice().equals(other.getGrouponPrice()))
            && (this.getOrderPrice() == null ? other.getOrderPrice() == null : this.getOrderPrice().equals(other.getOrderPrice()))
            && (this.getActualPrice() == null ? other.getActualPrice() == null : this.getActualPrice().equals(other.getActualPrice()))
            && (this.getPayId() == null ? other.getPayId() == null : this.getPayId().equals(other.getPayId()))
            && (this.getPayTime() == null ? other.getPayTime() == null : this.getPayTime().equals(other.getPayTime()))
            && (this.getShipSn() == null ? other.getShipSn() == null : this.getShipSn().equals(other.getShipSn()))
            && (this.getShipChannel() == null ? other.getShipChannel() == null : this.getShipChannel().equals(other.getShipChannel()))
            && (this.getShipTime() == null ? other.getShipTime() == null : this.getShipTime().equals(other.getShipTime()))
            && (this.getConfirmTime() == null ? other.getConfirmTime() == null : this.getConfirmTime().equals(other.getConfirmTime()))
            && (this.getComments() == null ? other.getComments() == null : this.getComments().equals(other.getComments()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getSettlementMoney() == null ? other.getSettlementMoney() == null : this.getSettlementMoney().equals(other.getSettlementMoney()))
            && (this.getSettlementStatus() == null ? other.getSettlementStatus() == null : this.getSettlementStatus().equals(other.getSettlementStatus()))
            && (this.getFreightType() == null ? other.getFreightType() == null : this.getFreightType().equals(other.getFreightType()))
            && (this.getShareUserId() == null ? other.getShareUserId() == null : this.getShareUserId().equals(other.getShareUserId()))
            && (this.getFetchCode() == null ? other.getFetchCode() == null : this.getFetchCode().equals(other.getFetchCode()))
            && (this.getCreateUserId() == null ? other.getCreateUserId() == null : this.getCreateUserId().equals(other.getCreateUserId()))
            && (this.getGiftSendTime() == null ? other.getGiftSendTime() == null : this.getGiftSendTime().equals(other.getGiftSendTime()))
            && (this.getGiftReceiveTime() == null ? other.getGiftReceiveTime() == null : this.getGiftReceiveTime().equals(other.getGiftReceiveTime()))
            && (this.getPayType() == null ? other.getPayType() == null : this.getPayType().equals(other.getPayType()))
            && (this.getRefundStatus() == null ? other.getRefundStatus() == null : this.getRefundStatus().equals(other.getRefundStatus()))
            && (this.getLngAndLat() == null ? other.getLngAndLat() == null : this.getLngAndLat().equals(other.getLngAndLat()))
            && (this.getDeliveryStatus() == null ? other.getDeliveryStatus() == null : this.getDeliveryStatus().equals(other.getDeliveryStatus()))
            && (this.getDeliveryOrderId() == null ? other.getDeliveryOrderId() == null : this.getDeliveryOrderId().equals(other.getDeliveryOrderId()))
            && (this.getBusinessType() == null ? other.getBusinessType() == null : this.getBusinessType().equals(other.getBusinessType()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getOrderSn() == null) ? 0 : getOrderSn().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getMealCode() == null) ? 0 : getMealCode().hashCode());
        result = prime * result + ((getMealQrCode() == null) ? 0 : getMealQrCode().hashCode());
        result = prime * result + ((getMealPickupStatus() == null) ? 0 : getMealPickupStatus().hashCode());
        result = prime * result + ((getMealPickupTime() == null) ? 0 : getMealPickupTime().hashCode());
        result = prime * result + ((getConsignee() == null) ? 0 : getConsignee().hashCode());
        result = prime * result + ((getMobile() == null) ? 0 : getMobile().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getMessage() == null) ? 0 : getMessage().hashCode());
        result = prime * result + ((getGoodsPrice() == null) ? 0 : getGoodsPrice().hashCode());
        result = prime * result + ((getFreightPrice() == null) ? 0 : getFreightPrice().hashCode());
        result = prime * result + ((getPackingFee() == null) ? 0 : getPackingFee().hashCode());
        result = prime * result + ((getCouponPrice() == null) ? 0 : getCouponPrice().hashCode());
        result = prime * result + ((getIntegralPrice() == null) ? 0 : getIntegralPrice().hashCode());
        result = prime * result + ((getGrouponPrice() == null) ? 0 : getGrouponPrice().hashCode());
        result = prime * result + ((getOrderPrice() == null) ? 0 : getOrderPrice().hashCode());
        result = prime * result + ((getActualPrice() == null) ? 0 : getActualPrice().hashCode());
        result = prime * result + ((getPayId() == null) ? 0 : getPayId().hashCode());
        result = prime * result + ((getPayTime() == null) ? 0 : getPayTime().hashCode());
        result = prime * result + ((getShipSn() == null) ? 0 : getShipSn().hashCode());
        result = prime * result + ((getShipChannel() == null) ? 0 : getShipChannel().hashCode());
        result = prime * result + ((getShipTime() == null) ? 0 : getShipTime().hashCode());
        result = prime * result + ((getConfirmTime() == null) ? 0 : getConfirmTime().hashCode());
        result = prime * result + ((getComments() == null) ? 0 : getComments().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getSettlementMoney() == null) ? 0 : getSettlementMoney().hashCode());
        result = prime * result + ((getSettlementStatus() == null) ? 0 : getSettlementStatus().hashCode());
        result = prime * result + ((getFreightType() == null) ? 0 : getFreightType().hashCode());
        result = prime * result + ((getShareUserId() == null) ? 0 : getShareUserId().hashCode());
        result = prime * result + ((getFetchCode() == null) ? 0 : getFetchCode().hashCode());
        result = prime * result + ((getCreateUserId() == null) ? 0 : getCreateUserId().hashCode());
        result = prime * result + ((getGiftSendTime() == null) ? 0 : getGiftSendTime().hashCode());
        result = prime * result + ((getGiftReceiveTime() == null) ? 0 : getGiftReceiveTime().hashCode());
        result = prime * result + ((getPayType() == null) ? 0 : getPayType().hashCode());
        result = prime * result + ((getRefundStatus() == null) ? 0 : getRefundStatus().hashCode());
        result = prime * result + ((getLngAndLat() == null) ? 0 : getLngAndLat().hashCode());
        result = prime * result + ((getDeliveryStatus() == null) ? 0 : getDeliveryStatus().hashCode());
        result = prime * result + ((getDeliveryOrderId() == null) ? 0 : getDeliveryOrderId().hashCode());
        result = prime * result + ((getBusinessType() == null) ? 0 : getBusinessType().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? IS_DELETED : NOT_DELETED);
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_order
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        shopId("shop_id", "shopId", "INTEGER", false),
        userId("user_id", "userId", "INTEGER", false),
        orderSn("order_sn", "orderSn", "VARCHAR", false),
        orderStatus("order_status", "orderStatus", "SMALLINT", false),
        mealCode("meal_code", "mealCode", "VARCHAR", false),
        mealQrCode("meal_qr_code", "mealQrCode", "VARCHAR", false),
        mealPickupStatus("meal_pickup_status", "mealPickupStatus", "BIT", false),
        mealPickupTime("meal_pickup_time", "mealPickupTime", "TIMESTAMP", false),
        consignee("consignee", "consignee", "VARCHAR", false),
        mobile("mobile", "mobile", "VARCHAR", false),
        address("address", "address", "VARCHAR", false),
        message("message", "message", "VARCHAR", false),
        goodsPrice("goods_price", "goodsPrice", "DECIMAL", false),
        freightPrice("freight_price", "freightPrice", "DECIMAL", false),
        packingFee("packing_fee", "packingFee", "DECIMAL", false),
        couponPrice("coupon_price", "couponPrice", "DECIMAL", false),
        integralPrice("integral_price", "integralPrice", "DECIMAL", false),
        grouponPrice("groupon_price", "grouponPrice", "DECIMAL", false),
        orderPrice("order_price", "orderPrice", "DECIMAL", false),
        actualPrice("actual_price", "actualPrice", "DECIMAL", false),
        payId("pay_id", "payId", "VARCHAR", false),
        payTime("pay_time", "payTime", "TIMESTAMP", false),
        shipSn("ship_sn", "shipSn", "VARCHAR", false),
        shipChannel("ship_channel", "shipChannel", "VARCHAR", false),
        shipTime("ship_time", "shipTime", "TIMESTAMP", false),
        confirmTime("confirm_time", "confirmTime", "TIMESTAMP", false),
        comments("comments", "comments", "SMALLINT", false),
        endTime("end_time", "endTime", "TIMESTAMP", false),
        addTime("add_time", "addTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleted("deleted", "deleted", "BIT", false),
        settlementMoney("settlement_money", "settlementMoney", "DECIMAL", false),
        settlementStatus("settlement_status", "settlementStatus", "BIT", false),
        freightType("freight_type", "freightType", "TINYINT", false),
        shareUserId("share_user_id", "shareUserId", "INTEGER", false),
        fetchCode("fetch_code", "fetchCode", "VARCHAR", false),
        createUserId("create_user_id", "createUserId", "INTEGER", false),
        giftSendTime("gift_send_time", "giftSendTime", "TIMESTAMP", false),
        giftReceiveTime("gift_receive_time", "giftReceiveTime", "TIMESTAMP", false),
        payType("pay_type", "payType", "VARCHAR", false),
        refundStatus("refund_status", "refundStatus", "VARCHAR", false),
        lngAndLat("lng_and_Lat", "lngAndLat", "VARCHAR", false),
        deliveryStatus("delivery_status", "deliveryStatus", "VARCHAR", false),
        deliveryOrderId("delivery_order_id", "deliveryOrderId", "VARCHAR", false),
        businessType("business_type", "businessType", "INTEGER", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_order
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}