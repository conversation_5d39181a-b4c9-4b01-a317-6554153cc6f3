package com.pioneer.mall.db.domain;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;

public class TpmShopHours {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean NOT_DELETED = false;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean IS_DELETED = true;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.shop_id
     *
     * @mbg.generated
     */
    private Integer shopId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.day_of_week
     *
     * @mbg.generated
     */
    private Integer dayOfWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.is_open
     *
     * @mbg.generated
     */
    private Boolean isOpen;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.opening_time
     *
     * @mbg.generated
     */
    private LocalTime openingTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.closing_time
     *
     * @mbg.generated
     */
    private LocalTime closingTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.status_desc
     *
     * @mbg.generated
     */
    private String statusDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_shop_hours.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.id
     *
     * @return the value of tpm_shop_hours.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.id
     *
     * @param id the value for tpm_shop_hours.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.shop_id
     *
     * @return the value of tpm_shop_hours.shop_id
     *
     * @mbg.generated
     */
    public Integer getShopId() {
        return shopId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.shop_id
     *
     * @param shopId the value for tpm_shop_hours.shop_id
     *
     * @mbg.generated
     */
    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.day_of_week
     *
     * @return the value of tpm_shop_hours.day_of_week
     *
     * @mbg.generated
     */
    public Integer getDayOfWeek() {
        return dayOfWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.day_of_week
     *
     * @param dayOfWeek the value for tpm_shop_hours.day_of_week
     *
     * @mbg.generated
     */
    public void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.is_open
     *
     * @return the value of tpm_shop_hours.is_open
     *
     * @mbg.generated
     */
    public Boolean getIsOpen() {
        return isOpen;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.is_open
     *
     * @param isOpen the value for tpm_shop_hours.is_open
     *
     * @mbg.generated
     */
    public void setIsOpen(Boolean isOpen) {
        this.isOpen = isOpen;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.opening_time
     *
     * @return the value of tpm_shop_hours.opening_time
     *
     * @mbg.generated
     */
    public LocalTime getOpeningTime() {
        return openingTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.opening_time
     *
     * @param openingTime the value for tpm_shop_hours.opening_time
     *
     * @mbg.generated
     */
    public void setOpeningTime(LocalTime openingTime) {
        this.openingTime = openingTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.closing_time
     *
     * @return the value of tpm_shop_hours.closing_time
     *
     * @mbg.generated
     */
    public LocalTime getClosingTime() {
        return closingTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.closing_time
     *
     * @param closingTime the value for tpm_shop_hours.closing_time
     *
     * @mbg.generated
     */
    public void setClosingTime(LocalTime closingTime) {
        this.closingTime = closingTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.status_desc
     *
     * @return the value of tpm_shop_hours.status_desc
     *
     * @mbg.generated
     */
    public String getStatusDesc() {
        return statusDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.status_desc
     *
     * @param statusDesc the value for tpm_shop_hours.status_desc
     *
     * @mbg.generated
     */
    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_shop_hours.deleted
     *
     * @return the value of tpm_shop_hours.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_shop_hours.deleted
     *
     * @param deleted the value for tpm_shop_hours.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", shopId=").append(shopId);
        sb.append(", dayOfWeek=").append(dayOfWeek);
        sb.append(", isOpen=").append(isOpen);
        sb.append(", openingTime=").append(openingTime);
        sb.append(", closingTime=").append(closingTime);
        sb.append(", statusDesc=").append(statusDesc);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmShopHours other = (TpmShopHours) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getDayOfWeek() == null ? other.getDayOfWeek() == null : this.getDayOfWeek().equals(other.getDayOfWeek()))
            && (this.getIsOpen() == null ? other.getIsOpen() == null : this.getIsOpen().equals(other.getIsOpen()))
            && (this.getOpeningTime() == null ? other.getOpeningTime() == null : this.getOpeningTime().equals(other.getOpeningTime()))
            && (this.getClosingTime() == null ? other.getClosingTime() == null : this.getClosingTime().equals(other.getClosingTime()))
            && (this.getStatusDesc() == null ? other.getStatusDesc() == null : this.getStatusDesc().equals(other.getStatusDesc()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getDayOfWeek() == null) ? 0 : getDayOfWeek().hashCode());
        result = prime * result + ((getIsOpen() == null) ? 0 : getIsOpen().hashCode());
        result = prime * result + ((getOpeningTime() == null) ? 0 : getOpeningTime().hashCode());
        result = prime * result + ((getClosingTime() == null) ? 0 : getClosingTime().hashCode());
        result = prime * result + ((getStatusDesc() == null) ? 0 : getStatusDesc().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? IS_DELETED : NOT_DELETED);
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_shop_hours
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        shopId("shop_id", "shopId", "INTEGER", false),
        dayOfWeek("day_of_week", "dayOfWeek", "INTEGER", false),
        isOpen("is_open", "isOpen", "BIT", false),
        openingTime("opening_time", "openingTime", "TIME", false),
        closingTime("closing_time", "closingTime", "TIME", false),
        statusDesc("status_desc", "statusDesc", "VARCHAR", false),
        deleted("deleted", "deleted", "BIT", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_shop_hours
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}