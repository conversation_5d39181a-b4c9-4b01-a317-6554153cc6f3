package com.pioneer.mall.db.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

public class TpmUser {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean NOT_DELETED = false;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean IS_DELETED = true;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.username
     *
     * @mbg.generated
     */
    private String username;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.password
     *
     * @mbg.generated
     */
    private String password;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.gender
     *
     * @mbg.generated
     */
    private Byte gender;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.birthday
     *
     * @mbg.generated
     */
    private LocalDate birthday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.last_login_time
     *
     * @mbg.generated
     */
    private LocalDateTime lastLoginTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.last_login_ip
     *
     * @mbg.generated
     */
    private String lastLoginIp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.user_level
     *
     * @mbg.generated
     */
    private Byte userLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.nickname
     *
     * @mbg.generated
     */
    private String nickname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.mobile
     *
     * @mbg.generated
     */
    private String mobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.avatar
     *
     * @mbg.generated
     */
    private String avatar;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.weixin_openid
     *
     * @mbg.generated
     */
    private String weixinOpenid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.points
     *
     * @mbg.generated
     */
    private BigDecimal points;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.balance
     *
     * @mbg.generated
     */
    private BigDecimal balance;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.membership_level
     *
     * @mbg.generated
     */
    private String membershipLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.membership_id
     *
     * @mbg.generated
     */
    private Integer membershipId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.membership_code
     *
     * @mbg.generated
     */
    private String membershipCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.accumulative_points
     *
     * @mbg.generated
     */
    private BigDecimal accumulativePoints;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.add_time
     *
     * @mbg.generated
     */
    private LocalDateTime addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_user.share_user_id
     *
     * @mbg.generated
     */
    private Integer shareUserId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.id
     *
     * @return the value of tpm_user.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.id
     *
     * @param id the value for tpm_user.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.username
     *
     * @return the value of tpm_user.username
     *
     * @mbg.generated
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.username
     *
     * @param username the value for tpm_user.username
     *
     * @mbg.generated
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.password
     *
     * @return the value of tpm_user.password
     *
     * @mbg.generated
     */
    public String getPassword() {
        return password;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.password
     *
     * @param password the value for tpm_user.password
     *
     * @mbg.generated
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.gender
     *
     * @return the value of tpm_user.gender
     *
     * @mbg.generated
     */
    public Byte getGender() {
        return gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.gender
     *
     * @param gender the value for tpm_user.gender
     *
     * @mbg.generated
     */
    public void setGender(Byte gender) {
        this.gender = gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.birthday
     *
     * @return the value of tpm_user.birthday
     *
     * @mbg.generated
     */
    public LocalDate getBirthday() {
        return birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.birthday
     *
     * @param birthday the value for tpm_user.birthday
     *
     * @mbg.generated
     */
    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.last_login_time
     *
     * @return the value of tpm_user.last_login_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.last_login_time
     *
     * @param lastLoginTime the value for tpm_user.last_login_time
     *
     * @mbg.generated
     */
    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.last_login_ip
     *
     * @return the value of tpm_user.last_login_ip
     *
     * @mbg.generated
     */
    public String getLastLoginIp() {
        return lastLoginIp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.last_login_ip
     *
     * @param lastLoginIp the value for tpm_user.last_login_ip
     *
     * @mbg.generated
     */
    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.user_level
     *
     * @return the value of tpm_user.user_level
     *
     * @mbg.generated
     */
    public Byte getUserLevel() {
        return userLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.user_level
     *
     * @param userLevel the value for tpm_user.user_level
     *
     * @mbg.generated
     */
    public void setUserLevel(Byte userLevel) {
        this.userLevel = userLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.nickname
     *
     * @return the value of tpm_user.nickname
     *
     * @mbg.generated
     */
    public String getNickname() {
        return nickname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.nickname
     *
     * @param nickname the value for tpm_user.nickname
     *
     * @mbg.generated
     */
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.mobile
     *
     * @return the value of tpm_user.mobile
     *
     * @mbg.generated
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.mobile
     *
     * @param mobile the value for tpm_user.mobile
     *
     * @mbg.generated
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.avatar
     *
     * @return the value of tpm_user.avatar
     *
     * @mbg.generated
     */
    public String getAvatar() {
        return avatar;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.avatar
     *
     * @param avatar the value for tpm_user.avatar
     *
     * @mbg.generated
     */
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.weixin_openid
     *
     * @return the value of tpm_user.weixin_openid
     *
     * @mbg.generated
     */
    public String getWeixinOpenid() {
        return weixinOpenid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.weixin_openid
     *
     * @param weixinOpenid the value for tpm_user.weixin_openid
     *
     * @mbg.generated
     */
    public void setWeixinOpenid(String weixinOpenid) {
        this.weixinOpenid = weixinOpenid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.status
     *
     * @return the value of tpm_user.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.status
     *
     * @param status the value for tpm_user.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.points
     *
     * @return the value of tpm_user.points
     *
     * @mbg.generated
     */
    public BigDecimal getPoints() {
        return points;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.points
     *
     * @param points the value for tpm_user.points
     *
     * @mbg.generated
     */
    public void setPoints(BigDecimal points) {
        this.points = points;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.balance
     *
     * @return the value of tpm_user.balance
     *
     * @mbg.generated
     */
    public BigDecimal getBalance() {
        return balance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.balance
     *
     * @param balance the value for tpm_user.balance
     *
     * @mbg.generated
     */
    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.membership_level
     *
     * @return the value of tpm_user.membership_level
     *
     * @mbg.generated
     */
    public String getMembershipLevel() {
        return membershipLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.membership_level
     *
     * @param membershipLevel the value for tpm_user.membership_level
     *
     * @mbg.generated
     */
    public void setMembershipLevel(String membershipLevel) {
        this.membershipLevel = membershipLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.membership_id
     *
     * @return the value of tpm_user.membership_id
     *
     * @mbg.generated
     */
    public Integer getMembershipId() {
        return membershipId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.membership_id
     *
     * @param membershipId the value for tpm_user.membership_id
     *
     * @mbg.generated
     */
    public void setMembershipId(Integer membershipId) {
        this.membershipId = membershipId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.membership_code
     *
     * @return the value of tpm_user.membership_code
     *
     * @mbg.generated
     */
    public String getMembershipCode() {
        return membershipCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.membership_code
     *
     * @param membershipCode the value for tpm_user.membership_code
     *
     * @mbg.generated
     */
    public void setMembershipCode(String membershipCode) {
        this.membershipCode = membershipCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.accumulative_points
     *
     * @return the value of tpm_user.accumulative_points
     *
     * @mbg.generated
     */
    public BigDecimal getAccumulativePoints() {
        return accumulativePoints;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.accumulative_points
     *
     * @param accumulativePoints the value for tpm_user.accumulative_points
     *
     * @mbg.generated
     */
    public void setAccumulativePoints(BigDecimal accumulativePoints) {
        this.accumulativePoints = accumulativePoints;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.add_time
     *
     * @return the value of tpm_user.add_time
     *
     * @mbg.generated
     */
    public LocalDateTime getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.add_time
     *
     * @param addTime the value for tpm_user.add_time
     *
     * @mbg.generated
     */
    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.update_time
     *
     * @return the value of tpm_user.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.update_time
     *
     * @param updateTime the value for tpm_user.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.deleted
     *
     * @return the value of tpm_user.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.deleted
     *
     * @param deleted the value for tpm_user.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_user.share_user_id
     *
     * @return the value of tpm_user.share_user_id
     *
     * @mbg.generated
     */
    public Integer getShareUserId() {
        return shareUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_user.share_user_id
     *
     * @param shareUserId the value for tpm_user.share_user_id
     *
     * @mbg.generated
     */
    public void setShareUserId(Integer shareUserId) {
        this.shareUserId = shareUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", username=").append(username);
        sb.append(", password=").append(password);
        sb.append(", gender=").append(gender);
        sb.append(", birthday=").append(birthday);
        sb.append(", lastLoginTime=").append(lastLoginTime);
        sb.append(", lastLoginIp=").append(lastLoginIp);
        sb.append(", userLevel=").append(userLevel);
        sb.append(", nickname=").append(nickname);
        sb.append(", mobile=").append(mobile);
        sb.append(", avatar=").append(avatar);
        sb.append(", weixinOpenid=").append(weixinOpenid);
        sb.append(", status=").append(status);
        sb.append(", points=").append(points);
        sb.append(", balance=").append(balance);
        sb.append(", membershipLevel=").append(membershipLevel);
        sb.append(", membershipId=").append(membershipId);
        sb.append(", membershipCode=").append(membershipCode);
        sb.append(", accumulativePoints=").append(accumulativePoints);
        sb.append(", addTime=").append(addTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", shareUserId=").append(shareUserId);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmUser other = (TpmUser) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUsername() == null ? other.getUsername() == null : this.getUsername().equals(other.getUsername()))
            && (this.getPassword() == null ? other.getPassword() == null : this.getPassword().equals(other.getPassword()))
            && (this.getGender() == null ? other.getGender() == null : this.getGender().equals(other.getGender()))
            && (this.getBirthday() == null ? other.getBirthday() == null : this.getBirthday().equals(other.getBirthday()))
            && (this.getLastLoginTime() == null ? other.getLastLoginTime() == null : this.getLastLoginTime().equals(other.getLastLoginTime()))
            && (this.getLastLoginIp() == null ? other.getLastLoginIp() == null : this.getLastLoginIp().equals(other.getLastLoginIp()))
            && (this.getUserLevel() == null ? other.getUserLevel() == null : this.getUserLevel().equals(other.getUserLevel()))
            && (this.getNickname() == null ? other.getNickname() == null : this.getNickname().equals(other.getNickname()))
            && (this.getMobile() == null ? other.getMobile() == null : this.getMobile().equals(other.getMobile()))
            && (this.getAvatar() == null ? other.getAvatar() == null : this.getAvatar().equals(other.getAvatar()))
            && (this.getWeixinOpenid() == null ? other.getWeixinOpenid() == null : this.getWeixinOpenid().equals(other.getWeixinOpenid()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getPoints() == null ? other.getPoints() == null : this.getPoints().equals(other.getPoints()))
            && (this.getBalance() == null ? other.getBalance() == null : this.getBalance().equals(other.getBalance()))
            && (this.getMembershipLevel() == null ? other.getMembershipLevel() == null : this.getMembershipLevel().equals(other.getMembershipLevel()))
            && (this.getMembershipId() == null ? other.getMembershipId() == null : this.getMembershipId().equals(other.getMembershipId()))
            && (this.getMembershipCode() == null ? other.getMembershipCode() == null : this.getMembershipCode().equals(other.getMembershipCode()))
            && (this.getAccumulativePoints() == null ? other.getAccumulativePoints() == null : this.getAccumulativePoints().equals(other.getAccumulativePoints()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getShareUserId() == null ? other.getShareUserId() == null : this.getShareUserId().equals(other.getShareUserId()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUsername() == null) ? 0 : getUsername().hashCode());
        result = prime * result + ((getPassword() == null) ? 0 : getPassword().hashCode());
        result = prime * result + ((getGender() == null) ? 0 : getGender().hashCode());
        result = prime * result + ((getBirthday() == null) ? 0 : getBirthday().hashCode());
        result = prime * result + ((getLastLoginTime() == null) ? 0 : getLastLoginTime().hashCode());
        result = prime * result + ((getLastLoginIp() == null) ? 0 : getLastLoginIp().hashCode());
        result = prime * result + ((getUserLevel() == null) ? 0 : getUserLevel().hashCode());
        result = prime * result + ((getNickname() == null) ? 0 : getNickname().hashCode());
        result = prime * result + ((getMobile() == null) ? 0 : getMobile().hashCode());
        result = prime * result + ((getAvatar() == null) ? 0 : getAvatar().hashCode());
        result = prime * result + ((getWeixinOpenid() == null) ? 0 : getWeixinOpenid().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getPoints() == null) ? 0 : getPoints().hashCode());
        result = prime * result + ((getBalance() == null) ? 0 : getBalance().hashCode());
        result = prime * result + ((getMembershipLevel() == null) ? 0 : getMembershipLevel().hashCode());
        result = prime * result + ((getMembershipId() == null) ? 0 : getMembershipId().hashCode());
        result = prime * result + ((getMembershipCode() == null) ? 0 : getMembershipCode().hashCode());
        result = prime * result + ((getAccumulativePoints() == null) ? 0 : getAccumulativePoints().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getShareUserId() == null) ? 0 : getShareUserId().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? IS_DELETED : NOT_DELETED);
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_user
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        username("username", "username", "VARCHAR", false),
        password("password", "password", "VARCHAR", true),
        gender("gender", "gender", "TINYINT", false),
        birthday("birthday", "birthday", "DATE", false),
        lastLoginTime("last_login_time", "lastLoginTime", "TIMESTAMP", false),
        lastLoginIp("last_login_ip", "lastLoginIp", "VARCHAR", false),
        userLevel("user_level", "userLevel", "TINYINT", false),
        nickname("nickname", "nickname", "VARCHAR", false),
        mobile("mobile", "mobile", "VARCHAR", false),
        avatar("avatar", "avatar", "VARCHAR", false),
        weixinOpenid("weixin_openid", "weixinOpenid", "VARCHAR", false),
        status("status", "status", "TINYINT", true),
        points("points", "points", "DECIMAL", false),
        balance("balance", "balance", "DECIMAL", false),
        membershipLevel("membership_level", "membershipLevel", "VARCHAR", false),
        membershipId("membership_id", "membershipId", "INTEGER", false),
        membershipCode("membership_code", "membershipCode", "VARCHAR", false),
        accumulativePoints("accumulative_points", "accumulativePoints", "DECIMAL", false),
        addTime("add_time", "addTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleted("deleted", "deleted", "BIT", false),
        shareUserId("share_user_id", "shareUserId", "INTEGER", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_user
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}