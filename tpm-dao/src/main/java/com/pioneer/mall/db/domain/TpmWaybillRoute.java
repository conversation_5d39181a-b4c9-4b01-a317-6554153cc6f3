package com.pioneer.mall.db.domain;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

public class TpmWaybillRoute {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean NOT_DELETED = false;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public static final Boolean IS_DELETED = true;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.tpm_order_id
     *
     * @mbg.generated
     */
    private Integer tpmOrderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.mail_no
     *
     * @mbg.generated
     */
    private String mailNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.reason_name
     *
     * @mbg.generated
     */
    private String reasonName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.order_id
     *
     * @mbg.generated
     */
    private String orderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.accept_time
     *
     * @mbg.generated
     */
    private String acceptTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.remark
     *
     * @mbg.generated
     */
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.op_code
     *
     * @mbg.generated
     */
    private String opCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.reason_code
     *
     * @mbg.generated
     */
    private String reasonCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.first_status_code
     *
     * @mbg.generated
     */
    private String firstStatusCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.first_status_name
     *
     * @mbg.generated
     */
    private String firstStatusName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.secondary_status_code
     *
     * @mbg.generated
     */
    private String secondaryStatusCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.secondary_status_name
     *
     * @mbg.generated
     */
    private String secondaryStatusName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.add_time
     *
     * @mbg.generated
     */
    private LocalDateTime addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_waybill_route.accept_address
     *
     * @mbg.generated
     */
    private String acceptAddress;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.id
     *
     * @return the value of tpm_waybill_route.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.id
     *
     * @param id the value for tpm_waybill_route.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.tpm_order_id
     *
     * @return the value of tpm_waybill_route.tpm_order_id
     *
     * @mbg.generated
     */
    public Integer getTpmOrderId() {
        return tpmOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.tpm_order_id
     *
     * @param tpmOrderId the value for tpm_waybill_route.tpm_order_id
     *
     * @mbg.generated
     */
    public void setTpmOrderId(Integer tpmOrderId) {
        this.tpmOrderId = tpmOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.mail_no
     *
     * @return the value of tpm_waybill_route.mail_no
     *
     * @mbg.generated
     */
    public String getMailNo() {
        return mailNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.mail_no
     *
     * @param mailNo the value for tpm_waybill_route.mail_no
     *
     * @mbg.generated
     */
    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.reason_name
     *
     * @return the value of tpm_waybill_route.reason_name
     *
     * @mbg.generated
     */
    public String getReasonName() {
        return reasonName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.reason_name
     *
     * @param reasonName the value for tpm_waybill_route.reason_name
     *
     * @mbg.generated
     */
    public void setReasonName(String reasonName) {
        this.reasonName = reasonName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.order_id
     *
     * @return the value of tpm_waybill_route.order_id
     *
     * @mbg.generated
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.order_id
     *
     * @param orderId the value for tpm_waybill_route.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.accept_time
     *
     * @return the value of tpm_waybill_route.accept_time
     *
     * @mbg.generated
     */
    public String getAcceptTime() {
        return acceptTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.accept_time
     *
     * @param acceptTime the value for tpm_waybill_route.accept_time
     *
     * @mbg.generated
     */
    public void setAcceptTime(String acceptTime) {
        this.acceptTime = acceptTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.remark
     *
     * @return the value of tpm_waybill_route.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.remark
     *
     * @param remark the value for tpm_waybill_route.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.op_code
     *
     * @return the value of tpm_waybill_route.op_code
     *
     * @mbg.generated
     */
    public String getOpCode() {
        return opCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.op_code
     *
     * @param opCode the value for tpm_waybill_route.op_code
     *
     * @mbg.generated
     */
    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.reason_code
     *
     * @return the value of tpm_waybill_route.reason_code
     *
     * @mbg.generated
     */
    public String getReasonCode() {
        return reasonCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.reason_code
     *
     * @param reasonCode the value for tpm_waybill_route.reason_code
     *
     * @mbg.generated
     */
    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.first_status_code
     *
     * @return the value of tpm_waybill_route.first_status_code
     *
     * @mbg.generated
     */
    public String getFirstStatusCode() {
        return firstStatusCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.first_status_code
     *
     * @param firstStatusCode the value for tpm_waybill_route.first_status_code
     *
     * @mbg.generated
     */
    public void setFirstStatusCode(String firstStatusCode) {
        this.firstStatusCode = firstStatusCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.first_status_name
     *
     * @return the value of tpm_waybill_route.first_status_name
     *
     * @mbg.generated
     */
    public String getFirstStatusName() {
        return firstStatusName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.first_status_name
     *
     * @param firstStatusName the value for tpm_waybill_route.first_status_name
     *
     * @mbg.generated
     */
    public void setFirstStatusName(String firstStatusName) {
        this.firstStatusName = firstStatusName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.secondary_status_code
     *
     * @return the value of tpm_waybill_route.secondary_status_code
     *
     * @mbg.generated
     */
    public String getSecondaryStatusCode() {
        return secondaryStatusCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.secondary_status_code
     *
     * @param secondaryStatusCode the value for tpm_waybill_route.secondary_status_code
     *
     * @mbg.generated
     */
    public void setSecondaryStatusCode(String secondaryStatusCode) {
        this.secondaryStatusCode = secondaryStatusCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.secondary_status_name
     *
     * @return the value of tpm_waybill_route.secondary_status_name
     *
     * @mbg.generated
     */
    public String getSecondaryStatusName() {
        return secondaryStatusName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.secondary_status_name
     *
     * @param secondaryStatusName the value for tpm_waybill_route.secondary_status_name
     *
     * @mbg.generated
     */
    public void setSecondaryStatusName(String secondaryStatusName) {
        this.secondaryStatusName = secondaryStatusName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.add_time
     *
     * @return the value of tpm_waybill_route.add_time
     *
     * @mbg.generated
     */
    public LocalDateTime getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.add_time
     *
     * @param addTime the value for tpm_waybill_route.add_time
     *
     * @mbg.generated
     */
    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.update_time
     *
     * @return the value of tpm_waybill_route.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.update_time
     *
     * @param updateTime the value for tpm_waybill_route.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.deleted
     *
     * @return the value of tpm_waybill_route.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.deleted
     *
     * @param deleted the value for tpm_waybill_route.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tpm_waybill_route.accept_address
     *
     * @return the value of tpm_waybill_route.accept_address
     *
     * @mbg.generated
     */
    public String getAcceptAddress() {
        return acceptAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tpm_waybill_route.accept_address
     *
     * @param acceptAddress the value for tpm_waybill_route.accept_address
     *
     * @mbg.generated
     */
    public void setAcceptAddress(String acceptAddress) {
        this.acceptAddress = acceptAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", tpmOrderId=").append(tpmOrderId);
        sb.append(", mailNo=").append(mailNo);
        sb.append(", reasonName=").append(reasonName);
        sb.append(", orderId=").append(orderId);
        sb.append(", acceptTime=").append(acceptTime);
        sb.append(", remark=").append(remark);
        sb.append(", opCode=").append(opCode);
        sb.append(", reasonCode=").append(reasonCode);
        sb.append(", firstStatusCode=").append(firstStatusCode);
        sb.append(", firstStatusName=").append(firstStatusName);
        sb.append(", secondaryStatusCode=").append(secondaryStatusCode);
        sb.append(", secondaryStatusName=").append(secondaryStatusName);
        sb.append(", addTime=").append(addTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", acceptAddress=").append(acceptAddress);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TpmWaybillRoute other = (TpmWaybillRoute) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTpmOrderId() == null ? other.getTpmOrderId() == null : this.getTpmOrderId().equals(other.getTpmOrderId()))
            && (this.getMailNo() == null ? other.getMailNo() == null : this.getMailNo().equals(other.getMailNo()))
            && (this.getReasonName() == null ? other.getReasonName() == null : this.getReasonName().equals(other.getReasonName()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getAcceptTime() == null ? other.getAcceptTime() == null : this.getAcceptTime().equals(other.getAcceptTime()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getOpCode() == null ? other.getOpCode() == null : this.getOpCode().equals(other.getOpCode()))
            && (this.getReasonCode() == null ? other.getReasonCode() == null : this.getReasonCode().equals(other.getReasonCode()))
            && (this.getFirstStatusCode() == null ? other.getFirstStatusCode() == null : this.getFirstStatusCode().equals(other.getFirstStatusCode()))
            && (this.getFirstStatusName() == null ? other.getFirstStatusName() == null : this.getFirstStatusName().equals(other.getFirstStatusName()))
            && (this.getSecondaryStatusCode() == null ? other.getSecondaryStatusCode() == null : this.getSecondaryStatusCode().equals(other.getSecondaryStatusCode()))
            && (this.getSecondaryStatusName() == null ? other.getSecondaryStatusName() == null : this.getSecondaryStatusName().equals(other.getSecondaryStatusName()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getAcceptAddress() == null ? other.getAcceptAddress() == null : this.getAcceptAddress().equals(other.getAcceptAddress()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTpmOrderId() == null) ? 0 : getTpmOrderId().hashCode());
        result = prime * result + ((getMailNo() == null) ? 0 : getMailNo().hashCode());
        result = prime * result + ((getReasonName() == null) ? 0 : getReasonName().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getAcceptTime() == null) ? 0 : getAcceptTime().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getOpCode() == null) ? 0 : getOpCode().hashCode());
        result = prime * result + ((getReasonCode() == null) ? 0 : getReasonCode().hashCode());
        result = prime * result + ((getFirstStatusCode() == null) ? 0 : getFirstStatusCode().hashCode());
        result = prime * result + ((getFirstStatusName() == null) ? 0 : getFirstStatusName().hashCode());
        result = prime * result + ((getSecondaryStatusCode() == null) ? 0 : getSecondaryStatusCode().hashCode());
        result = prime * result + ((getSecondaryStatusName() == null) ? 0 : getSecondaryStatusName().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getAcceptAddress() == null) ? 0 : getAcceptAddress().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? IS_DELETED : NOT_DELETED);
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table tpm_waybill_route
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        tpmOrderId("tpm_order_id", "tpmOrderId", "INTEGER", false),
        mailNo("mail_no", "mailNo", "VARCHAR", false),
        reasonName("reason_name", "reasonName", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        acceptTime("accept_time", "acceptTime", "VARCHAR", false),
        remark("remark", "remark", "VARCHAR", false),
        opCode("op_code", "opCode", "VARCHAR", false),
        reasonCode("reason_code", "reasonCode", "VARCHAR", false),
        firstStatusCode("first_status_code", "firstStatusCode", "VARCHAR", false),
        firstStatusName("first_status_name", "firstStatusName", "VARCHAR", false),
        secondaryStatusCode("secondary_status_code", "secondaryStatusCode", "VARCHAR", false),
        secondaryStatusName("secondary_status_name", "secondaryStatusName", "VARCHAR", false),
        addTime("add_time", "addTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleted("deleted", "deleted", "BIT", false),
        acceptAddress("accept_address", "acceptAddress", "LONGVARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table tpm_waybill_route
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}