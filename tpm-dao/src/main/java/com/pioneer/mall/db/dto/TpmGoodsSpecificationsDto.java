package com.pioneer.mall.db.dto;

import lombok.Data;

@Data
public class TpmGoodsSpecificationsDto {
    /**
     * {
     * "goodsAttributes": [
     * {
     * "id": 1,
     * "value": "属性名称",
     * "goodsSpecifications": [
     * {
     * "id": 2,
     * "value": "规格名称",
     * "price": "价格(元)，数值类型",
     * "required": "是否必须：true 必选；false 非必选",
     * "enable": "是否启用：true 启用；false 未启用"
     * }
     * ]
     * }
     * ]
     * }
     */
    private Integer id;
    private String value;
    private String price;
    //图片
    private String  picUrl;
    //件数
    private Integer number;
    private Boolean enable;
    private Boolean selected;
}
