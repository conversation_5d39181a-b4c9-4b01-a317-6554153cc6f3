package com.pioneer.mall.db.dto;

import com.pioneer.mall.db.domain.TpmUser;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 积分记录实体类
 *
 * Created by FSQ
 * CopyRight https://www.fuint.cn
 */
@Getter
@Setter
public class TpmPointDto implements Serializable {

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 会员ID
     */
    private Integer userId;

    /**
     * 会员信息
     */
    private TpmUser userInfo;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 积分变化数量
     */
    private BigDecimal amount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注说明
     */
    private String description;

    /**
     * 最后操作人
     */
    private String operator;

}

