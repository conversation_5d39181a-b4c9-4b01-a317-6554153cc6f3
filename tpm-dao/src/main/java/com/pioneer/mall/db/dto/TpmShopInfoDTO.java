package com.pioneer.mall.db.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;

/**
  * @description:
  * <AUTHOR>
  * @date 2024/9/5 23:45
  */
@Data
public class TpmShopInfoDTO {
    private Integer id;
    private String shopName;
    //纬度
    private BigDecimal latitude;
    //经度
    private BigDecimal longitude;
    private String address;
    private String telephone;
    //是否最近
    private Boolean nearest;
    private Integer distance;
    //营业时间描述
    private String openTimeDesc;
    //营业状态描述
    private String statusDesc;
    //距离描述
    private String distanceDesc;
    //是否营业
    private Boolean open;
    //开始时间
    private LocalTime openingTime;
    //结束时间
    private LocalTime closingTime;
}
