package com.pioneer.mall.db.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 会员等级
 */
@Getter
@AllArgsConstructor
public enum MembershipLevel {

    NORMAL("普通会员", "NORMAL", 0),
    BRONZE("青铜会员", "BRONZE", 100),
    SILVER("白银会员", "SILVER", 401),
    GOLD("黄金会员", "GOLD", 801),
    PLATINUM("白金会员", "PLATINUM", 1401),
    DIAMOND("钻石会员", "DIAMOND", 5001);

    final private String name;
    final private String value;
    final private Integer points;

    public static MembershipLevel getByValue(String value) {
        for (MembershipLevel level : MembershipLevel.values()) {
            if (level.getName().equals(value)) {
                return level;
            }
        }
        return null;
    }

    public static MembershipLevel getByPoints(Integer points) {
        if (Objects.isNull(points) || points < 0) {
            throw new IllegalArgumentException("参数异常");
        }
        MembershipLevel result = NORMAL;
        for (MembershipLevel value : MembershipLevel.values()) {
            if (points < value.getPoints()) {
                break;
            }
            result = value;
        }
        return result;
    }
}
