package com.pioneer.mall.db.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/11 21:13
 */
@Getter
@AllArgsConstructor
public enum TpmBusinessTypeEnums {
    PICKUP_DELIVERY(1, "自提/配送"),
    EXPRESS(2,"快递");

    private Integer code;
    private String desc;

    public static TpmBusinessTypeEnums getEnums(Integer code){
        for (TpmBusinessTypeEnums enums : TpmBusinessTypeEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
