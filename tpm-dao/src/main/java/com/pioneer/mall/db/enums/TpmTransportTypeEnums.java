package com.pioneer.mall.db.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 1=冷链、2=普快
 * @date 2025/2/24 21:15
 */
@Getter
@AllArgsConstructor
public enum TpmTransportTypeEnums {
    COLD_CHAIN(1, "冷链"),
    COMMON(2,"普快");

    private Integer code;
    private String desc;

    public static TpmTransportTypeEnums getEnums(Integer code){
        for (TpmTransportTypeEnums enums : TpmTransportTypeEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
