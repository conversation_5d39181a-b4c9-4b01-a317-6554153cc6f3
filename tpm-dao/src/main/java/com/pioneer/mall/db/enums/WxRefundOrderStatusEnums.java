package com.pioneer.mall.db.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WxRefundOrderStatusEnums {
    SUCCESS("SUCCESS", "退款成功"),
    CLOSED("CLOSED", "退款关闭"),
    PROCESSING("PROCESSING", "退款处理中"),
    ABNORMAL("ABNORMAL", "退款异常");

    private final String code;
    private final String desc;

    public static WxRefundOrderStatusEnums getByCode(String code) {
        for (WxRefundOrderStatusEnums wxRefundOrderStatusEnums : WxRefundOrderStatusEnums.values()) {
            if (wxRefundOrderStatusEnums.getCode().equals(code)) {
                return wxRefundOrderStatusEnums;
            }
        }
        return null;
    }
}
