package com.pioneer.mall.db.service;

import java.time.LocalDate;
import java.util.List;

import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCouponUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pioneer.mall.db.util.CouponConstant;

@Service
public class CouponAssignService {

	@Autowired
	private TpmCouponUserService couponUserService;
	@Autowired
	private TpmCouponService couponService;

	/**
	 * 分发注册优惠券
	 *
	 * @param userId
	 * @return
	 */
	public void assignForRegister(Integer userId) {
		List<TpmCoupon> couponList = couponService.queryRegister();
		for (TpmCoupon coupon : couponList) {
			Integer couponId = coupon.getId();

			Integer count = couponUserService.countUserAndCoupon(userId, couponId);
			if (count > 0) {
				continue;
			}

			Short limit = coupon.getLimit();
			while (limit > 0) {
				TpmCouponUser couponUser = new TpmCouponUser();
				couponUser.setCouponId(couponId);
				couponUser.setUserId(userId);
				Short timeType = coupon.getTimeType();
				if (timeType.equals(CouponConstant.TIME_TYPE_TIME)) {
					couponUser.setStartTime(coupon.getStartTime());
					couponUser.setEndTime(coupon.getEndTime());
				} else {
					LocalDate now = LocalDate.now();
					couponUser.setStartTime(now);
					couponUser.setEndTime(now.plusDays(coupon.getDays()));
				}
				couponUserService.add(couponUser);

				limit--;
			}
		}

	}

}