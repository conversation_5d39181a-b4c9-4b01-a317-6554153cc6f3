package com.pioneer.mall.db.service;

import com.pioneer.mall.db.bean.dto.CartCheckOutCouponInfoDTO;
import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCouponCode;
import com.pioneer.mall.db.domain.TpmCouponUser;
import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.util.CouponConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CouponVerifyService {
	private static final Logger logger = LoggerFactory.getLogger(CouponVerifyService.class);

	@Autowired
	private TpmCouponUserService couponUserService;
	@Autowired
	private TpmCouponService couponService;
	@Autowired
	private TpmCouponCodeService couponCodeService;

	/**
	 * 检测优惠券是否适合
	 *
	 * @param userId
	 * @param couponId
	 * @param checkedGoodsPrice
	 * @return
	 */
	public TpmCoupon checkCoupon(Integer userId, Integer couponId, BigDecimal checkedGoodsPrice) {
		TpmCoupon coupon = couponService.findById(couponId);
		TpmCouponUser couponUser = couponUserService.queryOne(userId, couponId);
		if (coupon == null || couponUser == null) {
			return null;
		}

		// 检查是否超期
		Short timeType = coupon.getTimeType();
		Short days = coupon.getDays();
		LocalDate now = LocalDate.now();
		if (timeType.equals(CouponConstant.TIME_TYPE_TIME)) {
			if (now.isBefore(coupon.getStartTime()) || now.isAfter(coupon.getEndTime())) {
				return null;
			}
		} else if (timeType.equals(CouponConstant.TIME_TYPE_DAYS)) {
			LocalDateTime expired = couponUser.getAddTime().plusDays(days);
			if (LocalDateTime.now().isAfter(expired)) {
				return null;
			}
		} else {
			return null;
		}

		// 检测商品是否符合
		// TODO 目前仅支持全平台商品，所以不需要检测
		Short goodType = coupon.getGoodsType();
		if (!goodType.equals(CouponConstant.GOODS_TYPE_ALL)) {
			return null;
		}

		// 检测订单状态
		Short status = coupon.getStatus();
		if (!status.equals(CouponConstant.STATUS_NORMAL)) {
			return null;
		}
		if (Objects.nonNull(checkedGoodsPrice)) {
			// 检测是否满足最低消费
			if (checkedGoodsPrice.compareTo(coupon.getMin()) < 0) {
				return null;
			}
		}
		return coupon;
	}


	public boolean CheckExpirationDate(TpmCoupon tpmCoupon, TpmCouponUser tpmCouponUser) {
		if (tpmCoupon == null || tpmCouponUser == null) {
			return false;
		}
//        if (!Objects.equals(tpmCouponUser.getStatus(), CouponConstant.STATUS_NORMAL)) {
//            return false;
//        }
        if (!Objects.equals(tpmCoupon.getStatus(), CouponConstant.STATUS_NORMAL)) {
            return false;
        }

		// 检查是否超期
		Short timeType = tpmCoupon.getTimeType();
		Short days = tpmCoupon.getDays();
		LocalDate now = LocalDate.now();
		if (timeType.equals(CouponConstant.TIME_TYPE_TIME)) {
			if (now.isBefore(tpmCoupon.getStartTime()) || now.isAfter(tpmCoupon.getEndTime())) {
				return false;
			}
		} else if (timeType.equals(CouponConstant.TIME_TYPE_DAYS)) {
			LocalDateTime expired = tpmCouponUser.getAddTime().plusDays(days);
			if (LocalDateTime.now().isAfter(expired)) {
				return false;
			}
		} else {
			return false;
		}
		return true;
	}

	public TpmCoupon checkCoupon(Integer userId, Integer couponId) {
		return checkCoupon(userId, couponId, null);
	}


	public CartCheckOutCouponInfoDTO checkCouponWithGoods(Integer userId, Map<Integer, TpmGoods> idTpmGoodsMap, BigDecimal goodsTotalPrice, Integer couponUserId) throws Exception {
		if (Objects.isNull(couponUserId) || Objects.equals(couponUserId, 0)) {
			return checkCouponWithGoods(userId, idTpmGoodsMap, goodsTotalPrice);
		} else {
			TpmCouponUser couponUser = couponUserService.findById(couponUserId);
			if (Objects.isNull(couponUser)) {
				throw new Exception("选中的优惠券不存在");
			}
			if (!Objects.equals(couponUser.getUserId(), userId)) {
				throw new Exception("选中的优惠券不属于当前用户");
			}
			Integer couponId = couponUser.getCouponId();
			TpmCoupon coupon = couponService.findById(couponId);
			if (Objects.isNull(coupon)) {
				throw new Exception("选中的优惠券不存在");
			}
			if (!CheckExpirationDate(coupon, couponUser)) {
				throw new Exception("选中的优惠券已过期");
			}
			CartCheckOutCouponInfoDTO cartCheckOutCouponInfoDTO = new CartCheckOutCouponInfoDTO();
			//0可以理解为满减的
			if (Objects.equals(coupon.getGoodsType().intValue(), 0)) {
				// 检测是否满足最低消费
				if (goodsTotalPrice.compareTo(coupon.getMin()) >= 0) {
					cartCheckOutCouponInfoDTO.setCouponPrice(coupon.getDiscount());
					cartCheckOutCouponInfoDTO.setCouponId(couponId);
					cartCheckOutCouponInfoDTO.setCouponName(coupon.getName());
					cartCheckOutCouponInfoDTO.setCouponUserId(couponUserId);
					cartCheckOutCouponInfoDTO.setGoodsType(0);
				}
			} else if (Objects.equals(coupon.getGoodsType().intValue(), 2)) {
				//2为商品的兑换
				List<Integer> goodsIdList = Arrays.asList(coupon.getGoodsValue());

				// 找到goodsIdList中在idTpmGoodsMap中存在且retailPrice最高的一个商品
				Optional<TpmGoods> maxRetailPriceGoods = goodsIdList.stream()
						.map(idTpmGoodsMap::get) // 根据ID从idTpmGoodsMap中获取TpmGoods对象
						.filter(Objects::nonNull) // 过滤掉不存在于idTpmGoodsMap中的ID
						.max(Comparator.comparing(TpmGoods::getRetailPrice)); // 找到retailPrice最高的TpmGoods对象

				maxRetailPriceGoods.ifPresent(goods ->
						System.out.println("最高零售价商品ID: " + goods.getId() + ", 零售价: " + goods.getRetailPrice())
				);
				if (maxRetailPriceGoods.isPresent()) {
					TpmGoods goods = maxRetailPriceGoods.get();
					cartCheckOutCouponInfoDTO.setCouponId(couponUser.getCouponId());
					cartCheckOutCouponInfoDTO.setCouponPrice(goods.getRetailPrice());
					cartCheckOutCouponInfoDTO.setCouponUserId(couponUserId);
					cartCheckOutCouponInfoDTO.setCouponName(coupon.getName());
					cartCheckOutCouponInfoDTO.setCategoryId(goods.getCategoryId());
					cartCheckOutCouponInfoDTO.setGoodsId(goods.getId());
					cartCheckOutCouponInfoDTO.setGoodsType(2);
				}
			}
			return cartCheckOutCouponInfoDTO;
		}

	}

	public CartCheckOutCouponInfoDTO checkCouponWithGoods(Integer userId, Map<Integer, TpmGoods> idTpmGoodsMap, BigDecimal goodsTotalPrice) {
		List<TpmCouponUser> couponUserList = couponUserService.queryAll(userId);
		List<Integer> couponIdList = couponUserList.stream().map(TpmCouponUser::getCouponId).collect(Collectors.toList());
		List<TpmCoupon> tpmCouponList = couponService.findById(couponIdList);
		Map<Integer, TpmCoupon> couponMap = tpmCouponList.stream().collect(Collectors.toMap(TpmCoupon::getId, coupon -> coupon));
		int tmpCouponLength = 0;
		BigDecimal tmpCouponPrice = BigDecimal.ZERO;
		int tmpCouponId = 0;
		int couponUserId = 0;
		String couponName = "";
		Integer categoryId = 0;
		Integer goodsId = null;
		Integer goodsType = null;
		for (TpmCouponUser couponUser : couponUserList) {
			TpmCoupon coupon = couponMap.get(couponUser.getCouponId());
			if (coupon == null) {
				continue;
			}

			if (!CheckExpirationDate(coupon, couponUser)) {
				continue;
			}

			//0可以理解为满减的
			if (Objects.equals(coupon.getGoodsType().intValue(), 0)) {
				// 检测是否满足最低消费
				if (goodsTotalPrice.compareTo(coupon.getMin()) >= 0) {
					tmpCouponLength++;
					if (coupon.getDiscount().compareTo(tmpCouponPrice) > 0) {
						tmpCouponId = couponUser.getCouponId();
						tmpCouponPrice = coupon.getDiscount();
						couponName = coupon.getName();
						couponUserId = couponUser.getId();
						goodsType = coupon.getGoodsType().intValue();
					}
				}
			} else if (Objects.equals(coupon.getGoodsType().intValue(), 2)) {
				//2为商品的兑换
				List<Integer> goodsIdList = Arrays.asList(coupon.getGoodsValue());

				// 找到goodsIdList中在idTpmGoodsMap中存在且retailPrice最高的一个商品
				Optional<TpmGoods> maxRetailPriceGoods = goodsIdList.stream()
						.map(idTpmGoodsMap::get) // 根据ID从idTpmGoodsMap中获取TpmGoods对象
						.filter(Objects::nonNull) // 过滤掉不存在于idTpmGoodsMap中的ID
						.max(Comparator.comparing(TpmGoods::getRetailPrice)); // 找到retailPrice最高的TpmGoods对象

				maxRetailPriceGoods.ifPresent(goods ->
						System.out.println("最高零售价商品ID: " + goods.getId() + ", 零售价: " + goods.getRetailPrice())
				);
				tmpCouponLength++;
				if (maxRetailPriceGoods.isPresent()&& maxRetailPriceGoods.get().getRetailPrice().compareTo(tmpCouponPrice)>0) {
					TpmGoods goods = maxRetailPriceGoods.get();
					tmpCouponId = couponUser.getCouponId();
					couponUserId = couponUser.getId();
					tmpCouponPrice = goods.getRetailPrice();
					couponName = coupon.getName();
					categoryId = goods.getCategoryId();
					goodsId = goods.getId();
					goodsType = coupon.getGoodsType().intValue();

				}
			}
		}
		logger.info("tmpCouponLength:{},tmpCouponId:{},tmpCouponPrice:{},couponName={}", tmpCouponLength, tmpCouponId, tmpCouponPrice, couponName);
		CartCheckOutCouponInfoDTO cartCheckOutCouponInfoDTO = new CartCheckOutCouponInfoDTO();
		cartCheckOutCouponInfoDTO.setCouponId(tmpCouponId);
		cartCheckOutCouponInfoDTO.setCouponLength(tmpCouponLength);
		cartCheckOutCouponInfoDTO.setCouponPrice(tmpCouponPrice);
		cartCheckOutCouponInfoDTO.setCouponName(couponName);
		cartCheckOutCouponInfoDTO.setCouponUserId(couponUserId);
		cartCheckOutCouponInfoDTO.setCategoryId(categoryId);
		cartCheckOutCouponInfoDTO.setGoodsId(goodsId);
		cartCheckOutCouponInfoDTO.setGoodsType(goodsType);
		return cartCheckOutCouponInfoDTO;
	}


}