package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmAdMapper;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.domain.TpmAdExample;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmAdService {
	@Resource
	private TpmAdMapper adMapper;

	public List<TpmAd> queryIndex() {
		TpmAdExample example = new TpmAdExample();
		example.or().andPositionEqualTo((byte) 1).andDeletedEqualTo(false).andEnabledEqualTo(true);
		return adMapper.selectByExample(example);
	}

	public List<TpmAd> querySelective(String name, String content, Integer page, Integer limit, String sort,
			String order) {
		TpmAdExample example = new TpmAdExample();
		TpmAdExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(name)) {
			criteria.andNameLike("%" + name + "%");
		}
		if (!StringUtils.isEmpty(content)) {
			criteria.andContentLike("%" + content + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, limit);
		return adMapper.selectByExample(example);
	}

	public int updateById(TpmAd ad) {
		ad.setUpdateTime(LocalDateTime.now());
		return adMapper.updateByPrimaryKeySelective(ad);
	}

	public void deleteById(Integer id) {
		adMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmAd ad) {
		ad.setAddTime(LocalDateTime.now());
		ad.setUpdateTime(LocalDateTime.now());
		adMapper.insertSelective(ad);
	}

	public TpmAd findById(Integer id) {
		return adMapper.selectByPrimaryKey(id);
	}
}
