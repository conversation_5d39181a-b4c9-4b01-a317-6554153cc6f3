package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmAddressMapper;
import com.pioneer.mall.db.domain.TpmAddress;
import com.pioneer.mall.db.domain.TpmAddressExample;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmAddressService {
	@Resource
	private TpmAddressMapper addressMapper;

	public List<TpmAddress> queryByUid(Integer uid, Integer businessType) {
		TpmAddressExample example = new TpmAddressExample();
		example.or().andUserIdEqualTo(uid)
				.andBusinessTypeEqualTo(businessType)
				.andDeletedEqualTo(false);
		return addressMapper.selectByExample(example);
	}

	public TpmAddress findById(Integer id) {
		return addressMapper.selectByPrimaryKey(id);
	}

	public int add(TpmAddress address) {
		address.setAddTime(LocalDateTime.now());
		address.setUpdateTime(LocalDateTime.now());
		return addressMapper.insertSelective(address);
	}

	public int update(TpmAddress address) {
		address.setUpdateTime(LocalDateTime.now());
		return addressMapper.updateByPrimaryKeySelective(address);
	}

	public void delete(Integer id) {
		addressMapper.logicalDeleteByPrimaryKey(id);
	}

	public TpmAddress findDefault(Integer userId) {
		TpmAddressExample example = new TpmAddressExample();
		example.or().andUserIdEqualTo(userId)
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andIsDefaultEqualTo(true).andDeletedEqualTo(false);
		return addressMapper.selectOneByExample(example);
	}

	/**
	 * 取消用户的默认地址配置
	 * 
	 * @param userId
	 */
	public void resetDefault(Integer userId) {
		TpmAddress address = new TpmAddress();
		address.setIsDefault(false);
		address.setUpdateTime(LocalDateTime.now());
		TpmAddressExample example = new TpmAddressExample();
		example.or().andUserIdEqualTo(userId)
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andDeletedEqualTo(false).andIsDefaultEqualTo(true);
		addressMapper.updateByExampleSelective(address, example);
	}

	public List<TpmAddress> querySelective(Integer userId, String name, Integer page, Integer limit, String sort,
										   String order) {
		TpmAddressExample example = new TpmAddressExample();
		TpmAddressExample.Criteria criteria = example.createCriteria();
		criteria.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode());
		if (userId != null) {
			criteria.andUserIdEqualTo(userId);
		}
		if (!StringUtils.isEmpty(name)) {
			criteria.andNameLike("%" + name + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, limit);
		return addressMapper.selectByExample(example);
	}
}
