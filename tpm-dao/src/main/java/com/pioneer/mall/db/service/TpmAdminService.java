package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmAdminMapper;
import com.pioneer.mall.db.domain.TpmAdmin;
import com.pioneer.mall.db.domain.TpmAdmin.Column;

import com.pioneer.mall.db.domain.TpmAdminExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmAdminService {
	private final Column[] result = new Column[] { Column.id, Column.username, Column.avatar, Column.roleIds };
	@Resource
	private TpmAdminMapper adminMapper;

	public List<TpmAdmin> findAdmin(String username) {
		TpmAdminExample example = new TpmAdminExample();
		example.or().andUsernameEqualTo(username).andDeletedEqualTo(false);
		return adminMapper.selectByExample(example);
	}

	public TpmAdmin findAdmin(Integer id) {
		return adminMapper.selectByPrimaryKey(id);
	}

	public List<TpmAdmin> querySelective(String username, Integer page, Integer limit, String sort, String order) {
		TpmAdminExample example = new TpmAdminExample();
		TpmAdminExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(username)) {
			criteria.andUsernameLike("%" + username + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, limit);
		return adminMapper.selectByExampleSelective(example, result);
	}

	public int updateById(TpmAdmin admin) {
		admin.setUpdateTime(LocalDateTime.now());
		return adminMapper.updateByPrimaryKeySelective(admin);
	}

	public void deleteById(Integer id) {
		adminMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmAdmin admin) {
		admin.setAddTime(LocalDateTime.now());
		admin.setUpdateTime(LocalDateTime.now());
		adminMapper.insertSelective(admin);
	}

	public TpmAdmin findById(Integer id) {
		return adminMapper.selectByPrimaryKeySelective(id, result);
	}

	public List<TpmAdmin> allAdmin() {
		TpmAdminExample example = new TpmAdminExample();
		example.or().andDeletedEqualTo(false);
		return adminMapper.selectByExample(example);
	}
}
