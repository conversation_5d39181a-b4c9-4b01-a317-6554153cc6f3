package com.pioneer.mall.db.service;

import java.time.LocalDateTime;

import javax.annotation.Resource;

import com.pioneer.mall.db.dao.TpmAgencyShareMapper;
import com.pioneer.mall.db.domain.TpmAgencyShare;
import com.pioneer.mall.db.domain.TpmAgencyShareExample;
import org.springframework.stereotype.Service;

@Service
public class TpmAgencyService {

	@Resource
	private TpmAgencyShareMapper agencyShareMapper;

	/**
	 * 获取代理用户的分享对象海报图
	 * @param userId
	 * @param type
	 * @param shareObjId
	 * @return
	 */
	public String getDtsAgencyShare(Integer userId, Integer type, Integer shareObjId) {
		String shareUrl = null;
		TpmAgencyShareExample example = new TpmAgencyShareExample();
		example.or().andUserIdEqualTo(userId).andTypeEqualTo(type).andShareObjIdEqualTo(shareObjId);
		TpmAgencyShare das = agencyShareMapper.selectOneByExample(example);
		if (das != null) {
			shareUrl = das.getShareUrl();
		}
		return shareUrl;
	}

	/**
	 * 存储代理用户的分享对象海报图
	 * @param userId
	 * @param type
	 * @param shareObjId
	 * @param shareUrl
	 */
	public void saveDtsAgencyShare(Integer userId, Integer type, Integer shareObjId, String shareUrl) {
		TpmAgencyShare record = new TpmAgencyShare();
		record.setUserId(userId);
		record.setType(type);
		record.setShareObjId(shareObjId);
		record.setShareUrl(shareUrl);
		record.setAddTime(LocalDateTime.now());
		record.setUpdateTime(LocalDateTime.now());
		agencyShareMapper.insert(record );
	}
}
