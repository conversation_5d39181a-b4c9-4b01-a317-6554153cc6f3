package com.pioneer.mall.db.service;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;

import com.pioneer.mall.db.dao.TpmArticleMapper;
import com.pioneer.mall.db.domain.TpmArticle;
import com.pioneer.mall.db.domain.TpmArticleExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.pioneer.mall.db.domain.TpmArticle.Column;

import com.github.pagehelper.PageHelper;

@Service
public class TpmArticleService {

	@Resource
	private TpmArticleMapper articleMapper;

	private TpmArticle.Column[] columns = new TpmArticle.Column[] { TpmArticle.Column.id, Column.title, Column.addTime, Column.type };

	public TpmArticle findById(Integer id) {
		return articleMapper.selectByPrimaryKey(id);
	}

	public List<TpmArticle> queryList(int offset, int limit, String sort, String order) {
		TpmArticleExample example = new TpmArticleExample();
		example.or().andDeletedEqualTo(false);
		example.setOrderByClause(sort + " " + order);
		PageHelper.startPage(offset, limit);
		return articleMapper.selectByExampleSelective(example, columns);
	}
	
	public boolean checkExistByTitle(String title) {
		TpmArticleExample example = new TpmArticleExample();
		example.or().andTitleEqualTo(title).andDeletedEqualTo(false);
		return articleMapper.countByExample(example) != 0;
	}

	public void add(TpmArticle article) {
		article.setAddTime(LocalDateTime.now());
		article.setUpdateTime(LocalDateTime.now());
		articleMapper.insertSelective(article);
	}

	public List<TpmArticle> querySelective(String title, Integer page, Integer size, String sort, String order) {
		TpmArticleExample example = new TpmArticleExample();
		TpmArticleExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(title)) {
			criteria.andTitleLike("%" + title + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return articleMapper.selectByExampleWithBLOBs(example);
	}

	public int updateById(TpmArticle article) {
		article.setUpdateTime(LocalDateTime.now());
		return articleMapper.updateByPrimaryKeySelective(article);
	}

	public void deleteById(Integer id) {
		articleMapper.logicalDeleteByPrimaryKey(id);
	}
}
