package com.pioneer.mall.db.service;

import com.alibaba.fastjson.JSON;
import com.pioneer.mall.db.domain.TpmGoodsAttribute;
import com.pioneer.mall.db.domain.TpmGoodsSpecification;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.dto.TpmGoodsSpecificationsDto;
import com.pioneer.mall.db.dto.TpmSpecificationsResultDto;
import com.pioneer.mall.db.util.SpecificationUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TpmAttributeSpecificationService {
    @Resource
    private TpmGoodsAttributeService goodsAttributeService;
    @Resource
    private TpmGoodsSpecificationService goodsSpecificationService;

    public String attributesSpecificationValid(List<TpmGoodsAttributesDto> oldGoodsAttributesList, List<TpmGoodsAttributesDto> newGoodsAttributesDtoList) {
        // 老的为空，新的为空
        if (CollectionUtils.isEmpty(oldGoodsAttributesList) && CollectionUtils.isEmpty(newGoodsAttributesDtoList)) {
            return null;
        }
        if (CollectionUtils.isEmpty(oldGoodsAttributesList) || CollectionUtils.isEmpty(newGoodsAttributesDtoList)) {
            return "商品属性已变更，请重新选择";
        }

        // 老的不为空，新的不为空。比较属性ID是否有变更，如果则认为需要重新提交
        List<Integer> newIds = newGoodsAttributesDtoList.stream().map(TpmGoodsAttributesDto::getId).collect(Collectors.toList());
        List<Integer> oldIds = oldGoodsAttributesList.stream().map(TpmGoodsAttributesDto::getId).collect(Collectors.toList());
        if (!new HashSet<>(newIds).containsAll(oldIds)) {
            return "商品属性已变更，请重新选择";
        }

        // 再次验证下，提交的数据是否和数据库一致
        for (TpmGoodsAttributesDto newAttributesDto : newGoodsAttributesDtoList) {
            for (TpmGoodsAttributesDto oldAttributesDto : oldGoodsAttributesList) {
                // 比较属性id
                if (Objects.equals(newAttributesDto.getId(), oldAttributesDto.getId())) {
                    // 比较规格id
                    List<TpmGoodsSpecificationsDto> newSpecifications = newAttributesDto.getGoodsSpecifications();
                    List<TpmGoodsSpecificationsDto> oldSpecifications = oldAttributesDto.getGoodsSpecifications();
                    if (!CollectionUtils.isEmpty(newSpecifications) && !CollectionUtils.isEmpty(oldSpecifications)) {
                        List<Integer> newSpecificationsIds = newSpecifications.stream().map(TpmGoodsSpecificationsDto::getId).collect(Collectors.toList());
                        List<Integer> oldSpecificationsIds = oldSpecifications.stream().map(TpmGoodsSpecificationsDto::getId).collect(Collectors.toList());
                        if (!new HashSet<>(newSpecificationsIds).containsAll(oldSpecificationsIds)) {
                            return "商品属性规格已变更，请重新选择";
                        }
                    }
                }
            }
        }

        for (TpmGoodsAttributesDto attributesDto : oldGoodsAttributesList) {
            if (Objects.nonNull(attributesDto.getSelectNumber())) {
                if (attributesDto.getSelectNumber() > 0) {
                    //任选多件逻辑
                    List<TpmGoodsSpecificationsDto> goodsSpecifications = attributesDto.getGoodsSpecifications();
                    int sum = goodsSpecifications.stream().filter(t -> Objects.nonNull(t.getNumber())).mapToInt(TpmGoodsSpecificationsDto::getNumber).sum();
                    if (!Objects.equals(sum, attributesDto.getSelectNumber())) {
                        return String.format("商品属性：%s 应选%s件，已选%s件，请重新选择！", attributesDto.getValue(), attributesDto.getSelectNumber(), sum);
                    }
                } else {
                    return String.format("商品属性：%s 套餐配置件数错误，请重新选择", attributesDto.getValue());
                }
            } else if (Objects.nonNull(attributesDto.getRequired()) && attributesDto.getRequired()) {
                String attrValue = attributesDto.getValue();
                List<TpmGoodsSpecificationsDto> goodsSpecifications = attributesDto.getGoodsSpecifications();
                if (CollectionUtils.isEmpty(goodsSpecifications)) {
                    return String.format("商品属性：%s 规格选项不能为空", attrValue);
                }
                // 获取 goodsSpecifications selected 为 true 的规格
                List<TpmGoodsSpecificationsDto> selectedGoodsSpecifications = goodsSpecifications.stream()
                        .map(z -> {
                            if (z.getSelected() == null) {
                                z.setSelected(false);
                            }
                            return z;
                        })
                        .filter(z -> z.getSelected()).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(selectedGoodsSpecifications)) {
                    return String.format("商品属性：%s 必选，请重新选择", attrValue);
                }
            }
        }
        return null;
    }

    public String attributesSpecificationValid(List<TpmGoodsAttributesDto> goodsAttributesList, Integer goodsId) {

        List<TpmGoodsAttributesDto> newGoodsAttributesDtoList = this.queryByGid(goodsId);
        return attributesSpecificationValid(goodsAttributesList, newGoodsAttributesDtoList);
    }

    public TpmSpecificationsResultDto getSelectedSpecificationsResult(List<TpmGoodsAttributesDto> goodsAttributesDtos) {
        if (CollectionUtils.isEmpty(goodsAttributesDtos)) {
            return null;
        }
        List<TpmGoodsSpecificationsDto> selectedSpecifications = SpecificationUtil.getSpecificationsDto(goodsAttributesDtos);
        if (CollectionUtils.isEmpty(selectedSpecifications)) {
            return null;
        }
        // 属性名称列表
        String specificationsJoin = SpecificationUtil.getSpecificationsValueJoinBySpecifications(selectedSpecifications);
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (TpmGoodsSpecificationsDto goodsSpecificationsDto : selectedSpecifications) {
            Integer id = goodsSpecificationsDto.getId();
            TpmGoodsSpecification goodsSpecification = goodsSpecificationService.findById(id);
            if (goodsSpecification != null) {
                if (Objects.nonNull(goodsSpecificationsDto.getNumber()) && goodsSpecificationsDto.getNumber() != 0) {
                    totalPrice = totalPrice.add(goodsSpecification.getPrice().multiply(new BigDecimal(goodsSpecificationsDto.getNumber())));
                } else {
                    totalPrice = totalPrice.add(goodsSpecification.getPrice());
                }
            }
        }

        TpmSpecificationsResultDto specificationsResultDto = new TpmSpecificationsResultDto();
        // Set<String> 转化成 String[]
        specificationsResultDto.setSpecificationsJoin(specificationsJoin);
        specificationsResultDto.setSpecificationTotalPrice(totalPrice);
        return specificationsResultDto;
    }

    public static void main(String[] args) {
        Set<String> valueSet = new HashSet() {{
            add("dfsdfds");
            add("dfsddddfds");
            add("dfsddddddddfds");
        }};
        TpmSpecificationsResultDto specificationsResultDto = new TpmSpecificationsResultDto();
        // Set<String> 转化成 String[]
        specificationsResultDto.setSpecificationsJoin(valueSet.toString());
        System.out.println(JSON.toJSONString(specificationsResultDto));
    }

    public List<TpmGoodsAttributesDto> queryByGid(Integer goodsId) {

        if (goodsId == null) {
            return null;
        }
        List<TpmGoodsSpecification> specifications = goodsSpecificationService.queryByGid(goodsId);
        List<TpmGoodsAttribute> attributes = goodsAttributeService.queryByGid(goodsId);
        // 拼接属性规格
        List<TpmGoodsAttributesDto> attributesDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(attributes)) {
            attributesDtoList = attributes.stream()
                    .map(z -> {
                        TpmGoodsAttributesDto attributesDto = new TpmGoodsAttributesDto();
                        attributesDto.setId(z.getId());
                        attributesDto.setGoodsId(z.getGoodsId());
                        attributesDto.setValue(z.getValue());
                        attributesDto.setRequired(z.getRequired());
                        attributesDto.setSelectNumber(z.getSelectNum());
                        attributesDto.setSelectRequired(z.getSelectNum() != null ? true : false);
                        if (!CollectionUtils.isEmpty(specifications)) {
                            List<TpmGoodsSpecificationsDto> goodsSpecifications = specifications.stream().filter(a -> {
                                return Objects.equals(a.getAttributeId(), z.getId());
                            }).map(b -> {
                                TpmGoodsSpecificationsDto specificationsDto = new TpmGoodsSpecificationsDto();
                                specificationsDto.setId(b.getId());
                                specificationsDto.setValue(b.getValue());
                                specificationsDto.setPrice(b.getPrice().toString());
                                specificationsDto.setPicUrl(b.getPicUrl());
                                specificationsDto.setEnable(b.getEnable());
                                return specificationsDto;
                            }).collect(Collectors.toList());
                            attributesDto.setGoodsSpecifications(goodsSpecifications);
                        }
                        return attributesDto;
                    }).collect(Collectors.toList());
        }
        return attributesDtoList;
    }

    public List<TpmGoodsAttributesDto> queryByGid(List<Integer> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return new ArrayList<>();
        }
        List<TpmGoodsAttribute> attributes = goodsAttributeService.queryByGid(goodsIdList);
        if (CollectionUtils.isEmpty(attributes)) {
            return new ArrayList<>();
        }
        List<TpmGoodsSpecification> specifications = goodsSpecificationService.queryByGid(goodsIdList);
        Map<Integer, List<TpmGoodsSpecification>> goodsIdSpecificationsMap = specifications.stream().collect(Collectors.groupingBy(TpmGoodsSpecification::getGoodsId));
        Map<Integer, List<TpmGoodsAttribute>> goodsIdAttributeMap = attributes.stream().collect(Collectors.groupingBy(TpmGoodsAttribute::getGoodsId));

        List<TpmGoodsAttributesDto> attributesDtoList = new ArrayList<>();
        for (Integer goodsId : goodsIdList) {
            List<TpmGoodsSpecification> specListForGoods = goodsIdSpecificationsMap.getOrDefault(goodsId, new ArrayList<>());
            List<TpmGoodsAttribute> attrListForGoods = goodsIdAttributeMap.getOrDefault(goodsId, new ArrayList<>());
            if (!CollectionUtils.isEmpty(attrListForGoods)) {
                for (TpmGoodsAttribute attribute : attrListForGoods) {
                    TpmGoodsAttributesDto attributesDto = new TpmGoodsAttributesDto();
                    attributesDto.setId(attribute.getId());
                    attributesDto.setGoodsId(attribute.getGoodsId());
                    attributesDto.setValue(attribute.getValue());
                    attributesDto.setRequired(attribute.getRequired());
                    List<TpmGoodsSpecificationsDto> goodsSpecifications = specListForGoods.stream().filter(a ->
                            Objects.equals(a.getAttributeId(), attribute.getId())
                    ).map(b -> {
                        TpmGoodsSpecificationsDto specificationsDto = new TpmGoodsSpecificationsDto();
                        specificationsDto.setId(b.getId());
                        specificationsDto.setValue(b.getValue());
                        specificationsDto.setPrice(b.getPrice().toString());
                        specificationsDto.setEnable(b.getEnable());
                        return specificationsDto;
                    }).collect(Collectors.toList());
                    attributesDto.setGoodsSpecifications(goodsSpecifications);
                    attributesDtoList.add(attributesDto);
                }
            }
        }

        return attributesDtoList;
    }
}
