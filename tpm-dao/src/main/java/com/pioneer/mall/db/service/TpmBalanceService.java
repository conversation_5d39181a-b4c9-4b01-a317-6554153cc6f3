package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.bean.search.TpmBalanceSearch;
import com.pioneer.mall.db.dao.TpmBalanceMapper;
import com.pioneer.mall.db.dao.TpmUserMapper;
import com.pioneer.mall.db.domain.TpmBalance;
import com.pioneer.mall.db.domain.TpmBalanceExample;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.enums.StatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 余额管理业务实现类
 * <p>
 * Created by FSQ
 * CopyRight https://www.fuint.cn
 */
@Service
public class TpmBalanceService {
    @Resource
    private TpmBalanceMapper tpmBalanceMapper;

    @Resource
    private TpmUserMapper tpmUserMapper;

    /**
     * 微信相关服务接口
     * */
//    private WeixinService weixinService;

    /**
     * 会员服务接口
     */
//    private MemberService memberService;
    public List<TpmBalance> querySelective(TpmBalanceSearch tpmBalanceSearch) {
        TpmBalanceExample example = new TpmBalanceExample();
        TpmBalanceExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(tpmBalanceSearch.getMobile())) {
            criteria.andMobileEqualTo(tpmBalanceSearch.getMobile());
        }
        if (Objects.nonNull(tpmBalanceSearch.getUserId())) {
            criteria.andUserIdEqualTo(tpmBalanceSearch.getUserId());
        }
        if (Objects.nonNull(tpmBalanceSearch.getOrderSn())) {
            criteria.andOrderSnEqualTo(tpmBalanceSearch.getOrderSn());
        }
        if (Objects.nonNull(tpmBalanceSearch.getStatus())) {
            criteria.andStatusEqualTo(tpmBalanceSearch.getStatus());
        }
        example.orderBy(TpmBalance.Column.addTime.desc());
        PageHelper.startPage(tpmBalanceSearch.getCurrentPage(), tpmBalanceSearch.getPageSize());
        //todo 补充完查询条件
        return tpmBalanceMapper.selectByExample(example);
    }

    /**
     * 添加余额记录
     *
     * @param tpmBalance
     * @param updateBalance
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
//    @OperationServiceLog(description = "会员余额变动")
    public Boolean addBalance(TpmBalance tpmBalance, Boolean updateBalance) throws Exception {
        if (tpmBalance.getUserId() < 0) {
            return false;
        }
        tpmBalance.setStatus(StatusEnum.ENABLED.getKey());
        tpmBalance.setAddTime(LocalDateTime.now());
        tpmBalance.setUpdateTime(LocalDateTime.now());

        TpmUser tpmUser = tpmUserMapper.selectByPrimaryKey(tpmBalance.getUserId());
        BigDecimal newAmount = tpmUser.getBalance().add(tpmBalance.getAmount());
        if (newAmount.compareTo(new BigDecimal("0")) < 0) {
            return false;
        }
        if (updateBalance) {
            tpmUser.setBalance(newAmount);
            tpmBalance.setLeftBalance(newAmount);
            tpmUserMapper.updateByPrimaryKeySelective(tpmUser);
        }
        tpmBalance.setMobile(tpmUser.getMobile());
        tpmBalance.setOperator(tpmUser.getNickname());
        tpmBalance.setDeleted(false);
        tpmBalanceMapper.insert(tpmBalance);

//        // 发送小程序订阅消息
//        Date nowTime = new Date();
//        Date sendTime = new Date(nowTime.getTime() + 60000);
//        Map<String, Object> params = new HashMap<>();
//        String dateTime = DateUtil.formatDate(Calendar.getInstance().getTime(), "yyyy-MM-dd HH:mm");
//        params.put("amount", tpmBalance.getAmount());
//        params.put("time", dateTime);
//        params.put("tips", "您的余额发生了变动，请留意~");
//        weixinService.sendSubscribeMessage(tpmBalance.getMerchantId(), tpmBalance.getUserId(), tpmUser.getOpenId(), WxMessageEnum.BALANCE_CHANGE.getKey(), "pages/user/index", params, sendTime);

        return true;
    }

    /**
     * 发放余额
     *
     * @param accountInfo
     * @param userIds
     * @param amount
     * @param remark
     * @return
     */
//    @Override
    @Transactional(rollbackFor = Exception.class)
//    @OperationServiceLog(description = "发放余额")
    public void distribute(String object, String userIds, String amount, String remark) throws Exception {
        if (!object.equals("all") && StringUtils.isEmpty(userIds)) {
            throw new Exception("请先选择会员");
        }
//        if (accountInfo.getMerchantId() == null || accountInfo.getMerchantId() < 1) {
//            throw new Exception("平台账号不能执行该操作");
//        }
        BigDecimal balanceAmount = new BigDecimal(amount);
        if (balanceAmount.compareTo(new BigDecimal(20000)) > 0) {
            throw new Exception("单次充值金额不能大于20000");
        }

        List<Integer> userIdArr = new ArrayList<>();
        List<String> userIdList = Arrays.asList(userIds.split(","));
        if (userIdList != null && userIdList.size() > 0) {
            for (String userId : userIdList) {
                if (!StringUtils.isEmpty(userId) && !userIdArr.contains(Integer.parseInt(userId))) {
                    userIdArr.add(Integer.parseInt(userId));
                }
            }
        }
        // 最多不能超过5000人
        if (userIdArr.size() > 5000) {
            throw new Exception("最多不能超过5000人");
        }
        tpmUserMapper.updateUserBalance(userIdArr, balanceAmount);

        if (userIdArr.size() > 0) {
            for (Integer userId : userIdArr) {
                TpmBalance tpmBalance = new TpmBalance();
                tpmBalance.setAmount(new BigDecimal(amount));
                tpmBalance.setUserId(userId);
//                tpmBalance.setMerchantId(accountInfo.getMerchantId());
                tpmBalance.setRemark(remark);
//                tpmBalance.setOperator(accountInfo.getAccountName());
                addBalance(tpmBalance, false);
            }
        } else {
//            TpmBalance tpmBalance = new TpmBalance();
//            tpmBalance.setAmount(new BigDecimal(amount));
//            tpmBalance.setUserId(0); // userId为0表示全体会员
////            tpmBalance.setMerchantId(accountInfo.getMerchantId());
////            tpmBalance.setDescription(remark);
////            tpmBalance.setOperator(accountInfo.getAccountName());
//            tpmBalance.setStatus(StatusEnum.ENABLED.getKey());
//            tpmBalance.setAddTime(LocalDateTime.now());
//            tpmBalance.setUpdateTime(LocalDateTime.now());
//            tpmBalanceMapper.insert(tpmBalance);
        }
    }

    /**
     * 获取订单余额记录
     *
     * @param orderSn
     * @return
     */
    public List<TpmBalance> getBalanceListByOrderSn(String orderSn) {
        return tpmBalanceMapper.getBalanceListByOrderSn(orderSn);
    }
}
