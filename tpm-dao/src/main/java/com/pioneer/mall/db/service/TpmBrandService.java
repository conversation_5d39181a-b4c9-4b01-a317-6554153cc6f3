package com.pioneer.mall.db.service;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;

import com.pioneer.mall.db.dao.TpmBrandMapper;
import com.pioneer.mall.db.dao.TpmCategoryMapper;
import com.pioneer.mall.db.domain.TpmBrand;
import com.pioneer.mall.db.domain.TpmBrand.Column;
import com.pioneer.mall.db.domain.TpmBrandExample;
import com.pioneer.mall.db.domain.TpmCategory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.github.pagehelper.PageHelper;

@Service
public class TpmBrandService {
	@Resource
	private TpmCategoryMapper categoryMapper;
	
	@Resource
	private TpmBrandMapper brandMapper;
	private TpmBrand.Column[] columns = new Column[] { Column.id, Column.name, Column.desc, Column.picUrl, Column.floorPrice };

	public List<TpmBrand> queryVO(int offset, int limit) {
		TpmBrandExample example = new TpmBrandExample();
		example.or().andDeletedEqualTo(false);
		example.setOrderByClause("add_time desc");
		PageHelper.startPage(offset, limit);
		return brandMapper.selectByExampleSelective(example, columns);
	}

	public int queryTotalCount() {
		TpmBrandExample example = new TpmBrandExample();
		example.or().andDeletedEqualTo(false);
		return (int) brandMapper.countByExample(example);
	}

	public TpmBrand findById(Integer id) {
		return brandMapper.selectByPrimaryKey(id);
	}

	public List<TpmBrand> querySelective(String id, String name, Integer page, Integer size, String sort,
			String order) {
		TpmBrandExample example = new TpmBrandExample();
		TpmBrandExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(id)) {
			criteria.andIdEqualTo(Integer.valueOf(id));
		}
		if (!StringUtils.isEmpty(name)) {
			criteria.andNameLike("%" + name + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return brandMapper.selectByExample(example);
	}

	public int updateById(TpmBrand brand) {
		brand.setUpdateTime(LocalDateTime.now());
		return brandMapper.updateByPrimaryKeySelective(brand);
	}

	public void deleteById(Integer id) {
		brandMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmBrand brand) {
		brand.setAddTime(LocalDateTime.now());
		brand.setUpdateTime(LocalDateTime.now());
		brandMapper.insertSelective(brand);
	}

	public List<TpmBrand> all() {
		TpmBrandExample example = new TpmBrandExample();
		example.or().andDeletedEqualTo(false);
		return brandMapper.selectByExample(example);
	}
	
	public List<TpmBrand> getAdminBrands(Integer adminId) {
		if (adminId == null) {
			return null;
		}
		TpmBrandExample example = new TpmBrandExample();
		example.or().andDeletedEqualTo(false).andAdminIdEqualTo(adminId);
		return brandMapper.selectByExample(example);
	}

	/**
	 * 根据分类id获取分类名
	 * @param categoryId
	 * @return
	 */
	public String getBrandCategory(Integer categoryId) {
		TpmCategory dtsCategory = categoryMapper.selectByPrimaryKey(categoryId);
		return dtsCategory == null ? "综合" : dtsCategory.getName();
	}
	
}
