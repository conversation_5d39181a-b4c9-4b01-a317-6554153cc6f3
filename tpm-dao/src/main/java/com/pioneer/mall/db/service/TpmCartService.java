package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmCartMapper;
import com.pioneer.mall.db.domain.TpmCart;
import com.pioneer.mall.db.domain.TpmCartExample;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.util.SpecificationUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
public class TpmCartService {
    @Resource
    private TpmCartMapper cartMapper;

    public TpmCart queryExist(Integer goodsId, Integer productId, Integer userId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andGoodsIdEqualTo(goodsId).andProductIdEqualTo(productId).andUserIdEqualTo(userId)
                .andDeletedEqualTo(false);
        return cartMapper.selectOneByExample(example);
    }

    /**
     * 判断购物车中是否存在此规格商品
     * 购物车有两种情况：
     * 1. 购物车已经存在商品，但是一个商品会只有一条记录，但是规格为空，此时
     *
     *
     * @param goodsId
     * @param userId
     * @param goodsAttributes
     * @return
     */
    public TpmCart queryExist(Integer goodsId, Integer userId, List<TpmGoodsAttributesDto> goodsAttributes,Integer businessType) {
        TpmCartExample example = new TpmCartExample();
        example.or().andGoodsIdEqualTo(goodsId).andUserIdEqualTo(userId)
                .andBusinessTypeEqualTo(businessType)
                .andDeletedEqualTo(false);
        List<TpmCart> cartList = cartMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(cartList)) {
            return null;
        }

        String pageSpecificationsJoin = SpecificationUtil.getSpecificationsValueJoinByAttributes(goodsAttributes);
        // 解析购物车规格看是否购物车已存在
        return cartList.stream()
                .filter(z -> {
                    String cartSpecifications = z.getSpecifications();
                    // 如果购物车为空，不论页面提交的是否有属性规格，直接返回true，后面直接用新的提交更新
                    if (StringUtils.isEmpty(cartSpecifications)) {
                        return true;
                    }
                    // specifications 存储的是用户提交时商品属性对应规格的相关属性
                    String cartSpecificationsValueJoin = SpecificationUtil.getSpecificationsValueJoinByAttributes(cartSpecifications);
                    return Objects.equals(cartSpecificationsValueJoin, pageSpecificationsJoin);
                }).findFirst().orElse(null);
    }

    /**
     * 判断购物车中是否存在此规格商品，支持店铺维度
     * 在多店铺模式下，需要同时匹配商品ID、用户ID、业务类型和店铺ID
     *
     * @param goodsId 商品ID
     * @param userId 用户ID
     * @param goodsAttributes 商品属性
     * @param businessType 业务类型
     * @param shopId 店铺ID
     * @return
     */
    public TpmCart queryExist(Integer goodsId, Integer userId, List<TpmGoodsAttributesDto> goodsAttributes, Integer businessType, Integer shopId) {
        TpmCartExample example = new TpmCartExample();
        TpmCartExample.Criteria criteria = example.or()
                .andGoodsIdEqualTo(goodsId)
                .andUserIdEqualTo(userId)
                .andBusinessTypeEqualTo(businessType)
                .andDeletedEqualTo(false);

        // 如果指定了shopId，则添加shopId条件
        if (shopId != null) {
            criteria.andShopIdEqualTo(shopId);
        }

        List<TpmCart> cartList = cartMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(cartList)) {
            return null;
        }

        String pageSpecificationsJoin = SpecificationUtil.getSpecificationsValueJoinByAttributes(goodsAttributes);
        // 解析购物车规格看是否购物车已存在
        return cartList.stream()
                .filter(z -> {
                    String cartSpecifications = z.getSpecifications();
                    // 如果购物车为空，不论页面提交的是否有属性规格，直接返回true，后面直接用新的提交更新
                    if (StringUtils.isEmpty(cartSpecifications)) {
                        return true;
                    }
                    // specifications 存储的是用户提交时商品属性对应规格的相关属性
                    String cartSpecificationsValueJoin = SpecificationUtil.getSpecificationsValueJoinByAttributes(cartSpecifications);
                    return Objects.equals(cartSpecificationsValueJoin, pageSpecificationsJoin);
                }).findFirst().orElse(null);
    }

    public void add(TpmCart cart) {
        cart.setAddTime(LocalDateTime.now());
        cart.setUpdateTime(LocalDateTime.now());
        cartMapper.insertSelective(cart);
    }

    public int updateById(TpmCart cart) {
        cart.setUpdateTime(LocalDateTime.now());
        return cartMapper.updateByPrimaryKeySelective(cart);
    }

    public List<TpmCart> queryByUid(int userId, Integer businessType) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId)
                .andBusinessTypeEqualTo(businessType)
                .andDeletedEqualTo(false);
        return cartMapper.selectByExample(example);
    }

    public List<TpmCart> queryByUidAndBusinessTypeAndShopId(int userId, Integer businessType,Integer shopId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId)
                .andBusinessTypeEqualTo(businessType)
                .andShopIdEqualTo(shopId)
                .andDeletedEqualTo(false);
        return cartMapper.selectByExample(example);
    }

    public List<TpmCart> queryByUidAndChecked(Integer userId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andCheckedEqualTo(true).andDeletedEqualTo(false);
        return cartMapper.selectByExample(example);
    }

    public int delete(List<Integer> productIdList, int userId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andProductIdIn(productIdList);
        return cartMapper.logicalDeleteByExample(example);
    }

    public int deleteById(Integer id, int userId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andIdEqualTo(id);
        return cartMapper.logicalDeleteByExample(example);
    }

    public int deleteById(List<Integer> idList, int userId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andIdIn(idList).andLogicalDeleted(false);
        return cartMapper.logicalDeleteByExample(example);
    }

    public int deleteByUserId(Integer userId) {
        TpmCartExample example = new TpmCartExample();
        example.createCriteria().andUserIdEqualTo(userId);
        return cartMapper.logicalDeleteByExample(example);
    }
    public int deleteByUserIdAndBusinessType(Integer userId,Integer businessType) {
        TpmCartExample example = new TpmCartExample();
        example.createCriteria().andUserIdEqualTo(userId).andBusinessTypeEqualTo(businessType);
        return cartMapper.logicalDeleteByExample(example);
    }


    public TpmCart findById(Integer id) {
        return cartMapper.selectByPrimaryKey(id);
    }

    public List<TpmCart> findById(List<Integer> idList) {
        TpmCartExample tpmCartExample = new TpmCartExample();
        tpmCartExample.createCriteria().andIdIn(idList).andLogicalDeleted(false);
        return cartMapper.selectByExample(tpmCartExample);
    }

    public int updateCheck(Integer userId, List<Integer> idsList, Boolean checked) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andProductIdIn(idsList).andDeletedEqualTo(false);
        TpmCart cart = new TpmCart();
        cart.setChecked(checked);
        cart.setUpdateTime(LocalDateTime.now());
        return cartMapper.updateByExampleSelective(cart, example);
    }

    public int updateCheckById(Integer userId, List<Integer> idsList, Boolean checked) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andIdIn(idsList).andDeletedEqualTo(false);
        TpmCart cart = new TpmCart();
        cart.setChecked(checked);
        cart.setUpdateTime(LocalDateTime.now());
        return cartMapper.updateByExampleSelective(cart, example);
    }

    public void clearGoods(Integer userId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andCheckedEqualTo(true);
        TpmCart cart = new TpmCart();
        cart.setDeleted(true);
        cartMapper.updateByExampleSelective(cart, example);
    }

    public void clearGoods(Integer userId,Integer businessType) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andBusinessTypeEqualTo(businessType).andCheckedEqualTo(true);
        TpmCart cart = new TpmCart();
        cart.setDeleted(true);
        cartMapper.updateByExampleSelective(cart, example);
    }

    public void clearGoods(Integer userId,List<Integer> cartIdList,Integer businessType) {
        TpmCartExample example = new TpmCartExample();
        example.or().andUserIdEqualTo(userId).andIdIn(cartIdList).andBusinessTypeEqualTo(businessType).andCheckedEqualTo(true);
        TpmCart cart = new TpmCart();
        cart.setDeleted(true);
        cartMapper.updateByExampleSelective(cart, example);
    }

    public List<TpmCart> querySelective(Integer userId, Integer goodsId, Integer page, Integer limit, String sort,
                                        String order) {
        TpmCartExample example = new TpmCartExample();
        TpmCartExample.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(userId)) {
            criteria.andUserIdEqualTo(userId);
        }
        if (!StringUtils.isEmpty(goodsId)) {
            criteria.andGoodsIdEqualTo(goodsId);
        }
        criteria.andDeletedEqualTo(false);

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        PageHelper.startPage(page, limit);
        return cartMapper.selectByExample(example);
    }

    public void deleteById(Integer id) {
        cartMapper.logicalDeleteByPrimaryKey(id);
    }

    public boolean checkExist(Integer goodsId) {
        TpmCartExample example = new TpmCartExample();
        example.or().andGoodsIdEqualTo(goodsId).andCheckedEqualTo(true).andDeletedEqualTo(false);
        return cartMapper.countByExample(example) != 0;
    }
}
