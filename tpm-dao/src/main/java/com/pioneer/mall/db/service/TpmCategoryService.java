package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmCategoryMapper;
import com.pioneer.mall.db.domain.TpmCategory;
import com.pioneer.mall.db.domain.TpmCategoryExample;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class TpmCategoryService {
	@Resource
	private TpmCategoryMapper categoryMapper;
	private TpmCategory.Column[] CHANNEL = { TpmCategory.Column.id, TpmCategory.Column.name,
			TpmCategory.Column.iconUrl };

	public List<TpmCategory> queryL1WithoutRecommend(int offset, int limit) {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or().andLevelEqualTo("L1")
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andNameNotEqualTo("推荐").andDeletedEqualTo(false);
		PageHelper.startPage(offset, limit);
		return categoryMapper.selectByExample(example);
	}

	public List<TpmCategory> queryL1(int offset, int limit) {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or().andLevelEqualTo("L1")
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andDeletedEqualTo(false);
		PageHelper.startPage(offset, limit);
		return categoryMapper.selectByExample(example);
	}

	public List<TpmCategory> adminQueryL1(Integer businessType,Integer shopId) {
		TpmCategoryExample example = new TpmCategoryExample();
		TpmCategoryExample.Criteria criteria = example.or().andLevelEqualTo("L1")
				.andBusinessTypeEqualTo(businessType)
				.andDeletedEqualTo(false);
		// 获取用户下店铺id列表
		if (shopId != null) {
			criteria.andShopIdEqualTo(shopId);
		} else {
			List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
			if (!CollectionUtils.isEmpty(userShopIdList)) {
				criteria.andShopIdIn(userShopIdList);
			}
		}
		example.setOrderByClause(TpmCategory.Column.sortOrder.asc());
		return categoryMapper.selectByExample(example);
	}

	public List<TpmCategory> queryL1(Integer businessType) {
		TpmCategoryExample example = new TpmCategoryExample();
		TpmCategoryExample.Criteria criteria = example.or().andLevelEqualTo("L1")
				.andBusinessTypeEqualTo(businessType)
				.andDeletedEqualTo(false);

        // 获取用户下店铺id列表
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (!CollectionUtils.isEmpty(userShopIdList)) {
            criteria.andShopIdIn(userShopIdList);
        }

		example.setOrderByClause(TpmCategory.Column.sortOrder.asc());
		return categoryMapper.selectByExample(example);
	}
	public List<TpmCategory> queryL1() {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or().andLevelEqualTo("L1")
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andDeletedEqualTo(false);
		return categoryMapper.selectByExample(example);
	}
	public List<TpmCategory> queryL2() {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or().andLevelEqualTo("L2")
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andDeletedEqualTo(false);
		example.setOrderByClause(TpmCategory.Column.sortOrder.asc());
		return categoryMapper.selectByExample(example);
	}

	public List<TpmCategory> adminQueryL2(Integer businessType,Integer shopId) {
		TpmCategoryExample example = new TpmCategoryExample();
		TpmCategoryExample.Criteria criteria = example.or();
		criteria.andLevelEqualTo("L2")
				.andBusinessTypeEqualTo(businessType)
				.andDeletedEqualTo(false);
		example.setOrderByClause(TpmCategory.Column.sortOrder.asc());

		// 判断下shopid
		if (shopId != null) {
			criteria.andShopIdEqualTo(shopId);
		} else {
			List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
			if (!CollectionUtils.isEmpty(userShopIdList)) {
				criteria.andShopIdIn(userShopIdList);
			}
		}

		return categoryMapper.selectByExample(example);
	}

	public List<TpmCategory> queryL2(Integer shopId,Integer businessType) {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or().andLevelEqualTo("L2").andShopIdEqualTo(shopId)
				.andBusinessTypeEqualTo(businessType)
				.andDeletedEqualTo(false);
		example.setOrderByClause(TpmCategory.Column.sortOrder.asc());
		return categoryMapper.selectByExample(example);
	}

    public List<TpmCategory> queryByPid(Integer pid, Integer businessType) {
        TpmCategoryExample example = new TpmCategoryExample();
        TpmCategoryExample.Criteria criteria = example.or()
                .andPidEqualTo(pid)
                .andBusinessTypeEqualTo(businessType)
                .andDeletedEqualTo(false);
        // 判断下shopid
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (!CollectionUtils.isEmpty(userShopIdList)) {
            criteria.andShopIdIn(userShopIdList);
        }

        return categoryMapper.selectByExample(example);
    }
	public List<TpmCategory> queryByPid(Integer pid) {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or()
				.andPidEqualTo(pid)
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andDeletedEqualTo(false);
		return categoryMapper.selectByExample(example);
	}

	public List<TpmCategory> queryL2ByIds(List<Integer> ids) {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or().andIdIn(ids).andLevelEqualTo("L2")
				.andDeletedEqualTo(false);
		return categoryMapper.selectByExample(example);
	}

	public TpmCategory findById(Integer id) {
		return categoryMapper.selectByPrimaryKey(id);
	}
	public List<TpmCategory> findById(List<Integer> id) {
		if (CollectionUtils.isEmpty(id)){
			return new ArrayList<>();
		}
		TpmCategoryExample tpmCategoryExample = new TpmCategoryExample();
		tpmCategoryExample.createCriteria().andIdIn(id).andLogicalDeleted(false);
		return categoryMapper.selectByExample(tpmCategoryExample);
	}

	public List<TpmCategory> querySelective(String id, String name,Integer businessType,
											Integer page, Integer size, String sort,
											String order,Integer shopId) {
		TpmCategoryExample example = new TpmCategoryExample();
		TpmCategoryExample.Criteria criteria = example.createCriteria();
		criteria.andBusinessTypeEqualTo(businessType);
		if (!StringUtils.isEmpty(id)) {
			criteria.andIdEqualTo(Integer.valueOf(id));
		}
		if (!StringUtils.isEmpty(name)) {
			criteria.andNameLike("%" + name + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		// 判断下shopid
		if (shopId != null) {
			criteria.andShopIdEqualTo(shopId);
		} else {
			List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
			if (!CollectionUtils.isEmpty(userShopIdList)) {
				criteria.andShopIdIn(userShopIdList);
			}
		}

		PageHelper.startPage(page, size);
		return categoryMapper.selectByExample(example);
	}

	public int updateById(TpmCategory category) {
		category.setUpdateTime(LocalDateTime.now());
		return categoryMapper.updateByPrimaryKeySelective(category);
	}

	public void deleteById(Integer id) {
		categoryMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmCategory category) {
		category.setAddTime(LocalDateTime.now());
		category.setUpdateTime(LocalDateTime.now());
		categoryMapper.insertSelective(category);
	}

	public List<TpmCategory> queryChannel() {
		TpmCategoryExample example = new TpmCategoryExample();
		example.or().andLevelEqualTo("L1")
				.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
				.andDeletedEqualTo(false);
		PageHelper.startPage(1, 9);// 设置分页10
		return categoryMapper.selectByExampleSelective(example, CHANNEL);
	}
}
