package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmCollectMapper;
import com.pioneer.mall.db.domain.TpmCollect;
import com.pioneer.mall.db.domain.TpmCollectExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmCollectService {
	@Resource
	private TpmCollectMapper collectMapper;

	public int count(int uid, Integer gid) {
		TpmCollectExample example = new TpmCollectExample();
		example.or().andUserIdEqualTo(uid).andValueIdEqualTo(gid).andDeletedEqualTo(false);
		return (int) collectMapper.countByExample(example);
	}

	public List<TpmCollect> queryByType(Integer userId, Byte type, Integer page, Integer size) {
		TpmCollectExample example = new TpmCollectExample();
		example.or().andUserIdEqualTo(userId).andTypeEqualTo(type).andDeletedEqualTo(false);
		example.setOrderByClause(TpmCollect.Column.addTime.desc());
		PageHelper.startPage(page, size);
		return collectMapper.selectByExample(example);
	}

	public int countByType(Integer userId, Byte type) {
		TpmCollectExample example = new TpmCollectExample();
		example.or().andUserIdEqualTo(userId).andTypeEqualTo(type).andDeletedEqualTo(false);
		return (int) collectMapper.countByExample(example);
	}

	public TpmCollect queryByTypeAndValue(Integer userId, Byte type, Integer valueId) {
		TpmCollectExample example = new TpmCollectExample();
		example.or().andUserIdEqualTo(userId).andValueIdEqualTo(valueId).andTypeEqualTo(type).andDeletedEqualTo(false);
		return collectMapper.selectOneByExample(example);
	}

	public void deleteById(Integer id) {
		collectMapper.logicalDeleteByPrimaryKey(id);
	}

	public int add(TpmCollect collect) {
		collect.setAddTime(LocalDateTime.now());
		collect.setUpdateTime(LocalDateTime.now());
		return collectMapper.insertSelective(collect);
	}

	public List<TpmCollect> querySelective(String userId, String valueId, Integer page, Integer size, String sort,
			String order) {
		TpmCollectExample example = new TpmCollectExample();
		TpmCollectExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(userId)) {
			criteria.andUserIdEqualTo(Integer.valueOf(userId));
		}
		if (!StringUtils.isEmpty(valueId)) {
			criteria.andValueIdEqualTo(Integer.valueOf(valueId));
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return collectMapper.selectByExample(example);
	}
}
