package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmCommentMapper;
import com.pioneer.mall.db.dao.ex.CommentMapperEx;

import com.pioneer.mall.db.domain.TpmComment;
import com.pioneer.mall.db.domain.TpmCommentExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmCommentService {
	@Resource
	private TpmCommentMapper commentMapper;
	
	@Resource
	private CommentMapperEx commentMapperEx;

	public List<TpmComment> queryGoodsByGid(Integer id, int offset, int limit) {
		TpmCommentExample example = new TpmCommentExample();
		example.setOrderByClause(TpmComment.Column.addTime.desc());
		example.or().andValueIdEqualTo(id).andTypeEqualTo((byte) 0).andDeletedEqualTo(false);
		PageHelper.startPage(offset, limit);
		return commentMapper.selectByExample(example);
	}

	public List<TpmComment> query(Byte type, Integer valueId, Integer showType, Integer offset, Integer limit) {
		TpmCommentExample example = new TpmCommentExample();
		example.setOrderByClause(TpmComment.Column.addTime.desc());
		if (showType == 0) {
			example.or().andValueIdEqualTo(valueId).andTypeEqualTo(type).andDeletedEqualTo(false);
		} else if (showType == 1) {
			example.or().andValueIdEqualTo(valueId).andTypeEqualTo(type).andHasPictureEqualTo(true)
					.andDeletedEqualTo(false);
		} else {
			throw new RuntimeException("showType不支持");
		}
		PageHelper.startPage(offset, limit);
		return commentMapper.selectByExample(example);
	}

	public int count(Byte type, Integer valueId, Integer showType) {
		TpmCommentExample example = new TpmCommentExample();
		if (showType == 0) {
			example.or().andValueIdEqualTo(valueId).andTypeEqualTo(type).andDeletedEqualTo(false);
		} else if (showType == 1) {
			example.or().andValueIdEqualTo(valueId).andTypeEqualTo(type).andHasPictureEqualTo(true)
					.andDeletedEqualTo(false);
		} else {
			throw new RuntimeException("showType不支持");
		}
		return (int) commentMapper.countByExample(example);
	}

	public int save(TpmComment comment) {
		comment.setAddTime(LocalDateTime.now());
		comment.setUpdateTime(LocalDateTime.now());
		return commentMapper.insertSelective(comment);
	}

	public List<TpmComment> querySelective(String userId, String valueId, Integer page, Integer size, String sort,
			String order) {
		TpmCommentExample example = new TpmCommentExample();
		TpmCommentExample.Criteria criteria = example.createCriteria();

		// type=2 是订单商品回复，这里过滤
		criteria.andTypeNotEqualTo((byte) 2);

		if (!StringUtils.isEmpty(userId)) {
			criteria.andUserIdEqualTo(Integer.valueOf(userId));
		}
		if (!StringUtils.isEmpty(valueId)) {
			criteria.andValueIdEqualTo(Integer.valueOf(valueId)).andTypeEqualTo((byte) 0);
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return commentMapper.selectByExample(example);
	}

	public void deleteById(Integer id) {
		commentMapper.logicalDeleteByPrimaryKey(id);
	}

	public String queryReply(Integer id) {
		TpmCommentExample example = new TpmCommentExample();
		example.or().andTypeEqualTo((byte) 2).andValueIdEqualTo(id);
		List<TpmComment> commentReply = commentMapper.selectByExampleSelective(example, TpmComment.Column.content);
		// 目前业务只支持回复一次
		if (commentReply.size() == 1) {
			return commentReply.get(0).getContent();
		}
		return null;
	}

	public TpmComment findById(Integer id) {
		return commentMapper.selectByPrimaryKey(id);
	}

	/**
	 * 入驻店铺对应商品的评价
	 * @param brandIds
	 * @param userId
	 * @param valueId
	 * @param page
	 * @param limit
	 * @param sort
	 * @param order
	 * @return
	 */
	public List<TpmComment> queryBrandCommentSelective(List<Integer> brandIds, String userId, String valueId,
			Integer page, Integer size, String sort, String order) {
		
		String orderBySql = null;
		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			orderBySql = "o."+sort + " " + order;
		}
		
		String brandIdsSql = null;
		if (brandIds != null) {
			brandIdsSql = "";
			for (Integer brandId : brandIds) {
				brandIdsSql += "," + brandId;
			}
			brandIdsSql = " and g.brand_id in (" + brandIdsSql.substring(1) + ") ";
		}

		PageHelper.startPage(page, size);
		
		Byte type = (byte) 0;//品牌入驻管理员限定只查商品评论
		return commentMapperEx.queryBrandComment(type,userId,valueId,orderBySql,brandIdsSql);
	}
}
