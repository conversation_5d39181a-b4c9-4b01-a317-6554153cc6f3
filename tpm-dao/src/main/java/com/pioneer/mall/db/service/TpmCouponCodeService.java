package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmCouponCodeMapper;
import com.pioneer.mall.db.domain.TpmCouponCode;
import com.pioneer.mall.db.domain.TpmCouponCodeExample;
import com.pioneer.mall.db.domain.TpmCouponCodeUseLog;
import com.pioneer.mall.db.domain.TpmOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 优惠口令服务
 * @date 2024/12/17 21:10
 */
@Slf4j
@Service
public class TpmCouponCodeService {
    @Resource
    private TpmCouponCodeMapper tpmCouponCodeMapper;
    @Resource
    private TpmCouponCodeUseLogService tpmCouponCodeUseLogService;

    public List<TpmCouponCode> queryList(String code, int offset, int limit, String sort, String order) {
        return queryList(TpmCouponCodeExample.newAndCreateCriteria(), code, offset, limit, sort, order);
    }

    public List<TpmCouponCode> queryList(TpmCouponCodeExample.Criteria criteria, String code, int offset, int limit, String sort,
                                         String order) {
        criteria.andDeletedEqualTo(false);
        if (Objects.nonNull(code)) {
            criteria.andCodeLike("%" + code + "%");
        }
        criteria.example().setOrderByClause(sort + " " + order);
        PageHelper.startPage(offset, limit);
        return tpmCouponCodeMapper.selectByExampleSelective(criteria.example());
    }

    public int add(TpmCouponCode tpmCouponCode) {
        TpmCouponCode existCode = this.findByCode(tpmCouponCode.getCode());
        if (Objects.nonNull(existCode)) {
            throw new RuntimeException("优惠口令已存在");
        }
        tpmCouponCode.setDeleted(false);
        return tpmCouponCodeMapper.insertSelective(tpmCouponCode);
    }

    public TpmCouponCode findById(Integer id) {
        return tpmCouponCodeMapper.selectByPrimaryKey(id);
    }

    public int updateById(TpmCouponCode tpmCouponCode) {
        tpmCouponCode.setDeleted(false);
        return tpmCouponCodeMapper.updateByPrimaryKeySelective(tpmCouponCode);
    }

    public TpmCouponCode findByCode(String code) {
        TpmCouponCodeExample example = new TpmCouponCodeExample();
        example.or().andCodeEqualTo(code).andDeletedEqualTo(false);
        return tpmCouponCodeMapper.selectOneByExample(example);
    }

    public int count() {
        TpmCouponCodeExample example = new TpmCouponCodeExample();
        example.or().andDeletedEqualTo(false);
        return (int) tpmCouponCodeMapper.countByExample(example);
    }

    public int deleteById(Integer id) {
        return tpmCouponCodeMapper.deleteByPrimaryKey(id);
    }


    public void reduceRemainingTimesAndLog(TpmOrder order, Integer couponCodeId) {
        if (Objects.isNull(couponCodeId)) {
            return;
        }
        TpmCouponCode tpmCouponCode = this.findById(couponCodeId);
        if (Objects.isNull(tpmCouponCode)) {
            return;
        }
        this.reduceRemainingTimes(tpmCouponCode);
        buildLog(order, couponCodeId, tpmCouponCode);
    }

    public void reduceRemainingTimes(TpmCouponCode tpmCouponCode) {
        try {
            TpmCouponCode update = new TpmCouponCode();
            update.setId(tpmCouponCode.getId());
            if (Objects.nonNull(tpmCouponCode.getRemainingTimes()) && tpmCouponCode.getRemainingTimes() >= 0) {
                update.setRemainingTimes(tpmCouponCode.getRemainingTimes() - 1);
                tpmCouponCodeMapper.updateByPrimaryKeySelective(update);
            }

        } catch (Exception e) {
            log.error("优惠口令次数扣减失败", e);
        }
    }


    public void buildLog(TpmOrder order, Integer couponCodeId, TpmCouponCode tpmCouponCode) {
        TpmCouponCodeUseLog log = new TpmCouponCodeUseLog();
        log.setTccId(couponCodeId);
        log.setCode(tpmCouponCode.getCode());
        log.setDiscount(tpmCouponCode.getDiscount());
        log.setUserId(order.getUserId());
        log.setOrderId(order.getId());
        log.setOrderSn(order.getOrderSn());
        log.setDiscountAmount(order.getCouponPrice());
        log.setAddTime(LocalDateTime.now());
        log.setDeleted(false);
        tpmCouponCodeUseLogService.add(log);
    }


}
