package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmCouponCodeUseLogMapper;
import com.pioneer.mall.db.domain.TpmCouponCodeUseLog;
import com.pioneer.mall.db.domain.TpmCouponCodeUseLogExample;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @description: 优惠口令日志
 * @date 2024/12/17 21:10
 */
@Service
public class TpmCouponCodeUseLogService {
    @Resource
    private TpmCouponCodeUseLogMapper tpmCouponCodeUseLogMapper;

    public int add(TpmCouponCodeUseLog tpmCouponCodeUseLog) {
        return tpmCouponCodeUseLogMapper.insertSelective(tpmCouponCodeUseLog);
    }

    public List<TpmCouponCodeUseLog> queryUseList(Integer codeId,Integer userId,int offset, int limit, String sort, String order){
        TpmCouponCodeUseLogExample example = new TpmCouponCodeUseLogExample();
        TpmCouponCodeUseLogExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(codeId)){
            criteria.andTccIdEqualTo(codeId);
        }
        if (Objects.nonNull(userId)){
            criteria.andUserIdEqualTo(userId);
        }
        criteria.example().setOrderByClause(sort + " " + order);
        PageHelper.startPage(offset, limit);
        return tpmCouponCodeUseLogMapper.selectByExample(example);
    }

    public TpmCouponCodeUseLog findByOrderId(Integer orderId) {
        TpmCouponCodeUseLogExample example = new TpmCouponCodeUseLogExample();
        example.createCriteria().andOrderIdEqualTo(orderId).andLogicalDeleted(false);
        return tpmCouponCodeUseLogMapper.selectOneByExample(example);
    }
}