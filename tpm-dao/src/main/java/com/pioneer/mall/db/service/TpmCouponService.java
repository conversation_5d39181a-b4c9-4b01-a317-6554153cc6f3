package com.pioneer.mall.db.service;

import com.alibaba.druid.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmCouponMapper;
import com.pioneer.mall.db.dao.TpmCouponUserMapper;
import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCoupon.Column;
import com.pioneer.mall.db.domain.TpmCouponExample;
import com.pioneer.mall.db.domain.TpmCouponUser;
import com.pioneer.mall.db.domain.TpmCouponUserExample;
import com.pioneer.mall.db.util.CouponConstant;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;

@Service
public class TpmCouponService {
    @Resource
    private TpmCouponMapper couponMapper;
    @Resource
    private TpmCouponUserMapper couponUserMapper;

    private Column[] result = new Column[]{Column.id, Column.name, Column.desc, Column.tag, Column.days,
            Column.startTime, Column.endTime, Column.discount, Column.min};


    /**
     * 有效时间限制，如果是0，则基于领取时间的有效天数days；如果是1，则start_time和end_time是优惠券有效期；
     *
     * @param tpmCoupon
     * @return
     */
    public LocalDate getRealStartTime(TpmCoupon tpmCoupon) {

        if (Objects.equals(tpmCoupon.getTimeType(), Short.valueOf("0"))) {
            return LocalDate.from(LocalDateTime.now());
        } else if (Objects.equals(tpmCoupon.getTimeType(), Short.valueOf("1"))) {
            return LocalDate.from(tpmCoupon.getStartTime().atStartOfDay());
        }
        return null;
    }

    /**
     * 有效时间限制，如果是0，则基于领取时间的有效天数days；如果是1，则start_time和end_time是优惠券有效期；
     *
     * @param tpmCoupon
     * @return
     */
    public LocalDate getRealEndTime(TpmCoupon tpmCoupon) {
        if (Objects.equals(tpmCoupon.getTimeType(), Short.valueOf("0"))) {
            return LocalDate.from(LocalDateTime.now().plusDays(tpmCoupon.getDays()));
        } else if (Objects.equals(tpmCoupon.getTimeType(), Short.valueOf("1"))) {
            return LocalDate.from(tpmCoupon.getEndTime().atTime(LocalTime.MAX));
        }
        return null;
    }


    /**
     * 查询，空参数
     *
     * @param offset
     * @param limit
     * @param sort
     * @param order
     * @return
     */
    public List<TpmCoupon> queryList(int offset, int limit, String sort, String order) {
        return queryList(TpmCouponExample.newAndCreateCriteria(), offset, limit, sort, order);
    }

    /**
     * 查询
     *
     * @param criteria 可扩展的条件
     * @param offset
     * @param limit
     * @param sort
     * @param order
     * @return
     */
    public List<TpmCoupon> queryList(TpmCouponExample.Criteria criteria, int offset, int limit, String sort,
                                     String order) {
        criteria.andTypeEqualTo(CouponConstant.TYPE_COMMON).andStatusEqualTo(CouponConstant.STATUS_NORMAL)
                .andDeletedEqualTo(false);
        criteria.example().setOrderByClause(sort + " " + order);
        PageHelper.startPage(offset, limit);
        return couponMapper.selectByExampleSelective(criteria.example(), result);
    }

    public int queryTotal() {
        TpmCouponExample example = new TpmCouponExample();
        example.or().andTypeEqualTo(CouponConstant.TYPE_COMMON).andStatusEqualTo(CouponConstant.STATUS_NORMAL)
                .andDeletedEqualTo(false);
        return (int) couponMapper.countByExample(example);
    }

    public List<TpmCoupon> queryAvailableList(Integer userId, int offset, int limit) {
        assert userId != null;
        // 过滤掉登录账号已经领取过的coupon
        TpmCouponExample.Criteria c = TpmCouponExample.newAndCreateCriteria();
        List<TpmCouponUser> used = couponUserMapper
                .selectByExample(TpmCouponUserExample.newAndCreateCriteria().andUserIdEqualTo(userId).example());
        if (used != null && !used.isEmpty()) {
            c.andIdNotIn(used.stream().map(TpmCouponUser::getCouponId).collect(Collectors.toList()));
        }
        return queryList(c, offset, limit, "add_time", "desc");
    }

    public List<TpmCoupon> queryList(int offset, int limit) {
        return queryList(offset, limit, "add_time", "desc");
    }

    public TpmCoupon findById(Integer id) {
        return couponMapper.selectByPrimaryKey(id);
    }

    public List<TpmCoupon> findById(List<Integer> id) {
        if (CollectionUtils.isEmpty(id)) {
            return new ArrayList<>();
        }
        TpmCouponExample example = new TpmCouponExample();
        example.createCriteria().andIdIn(id).andLogicalDeleted(false);
        return couponMapper.selectByExample(example);
    }

    public TpmCoupon findByCode(String code) {
        TpmCouponExample example = new TpmCouponExample();
        example.or().andCodeEqualTo(code).andTypeEqualTo(CouponConstant.TYPE_CODE)
                .andStatusEqualTo(CouponConstant.STATUS_NORMAL).andDeletedEqualTo(false);
        List<TpmCoupon> couponList = couponMapper.selectByExample(example);
        if (couponList.size() > 1) {
            throw new RuntimeException("");
        } else if (couponList.size() == 0) {
            return null;
        } else {
            return couponList.get(0);
        }
    }

    /**
     * 查询新用户注册优惠券
     *
     * @return
     */
    public List<TpmCoupon> queryRegister() {
        TpmCouponExample example = new TpmCouponExample();
        example.or().andTypeEqualTo(CouponConstant.TYPE_REGISTER).andStatusEqualTo(CouponConstant.STATUS_NORMAL)
                .andDeletedEqualTo(false);
        return couponMapper.selectByExample(example);
    }

    public List<TpmCoupon> querySelective(String name, Short type, Short status, Integer page, Integer limit,
                                          String sort, String order) {
        TpmCouponExample example = new TpmCouponExample();
        TpmCouponExample.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        if (type != null) {
            criteria.andTypeEqualTo(type);
        }
        if (status != null) {
            criteria.andStatusEqualTo(status);
        }
        criteria.andDeletedEqualTo(false);

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        PageHelper.startPage(page, limit);
        return couponMapper.selectByExample(example);
    }

    public void add(TpmCoupon coupon) {
        coupon.setAddTime(LocalDateTime.now());
        coupon.setUpdateTime(LocalDateTime.now());
        couponMapper.insertSelective(coupon);
    }

    public int updateById(TpmCoupon coupon) {
        coupon.setUpdateTime(LocalDateTime.now());
        return couponMapper.updateByPrimaryKeySelective(coupon);
    }

    public void deleteById(Integer id) {
        couponMapper.logicalDeleteByPrimaryKey(id);
    }

    private String getRandomNum(Integer num) {
        String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        base += "0123456789";

        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < num; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 生成优惠码
     *
     * @return 可使用优惠码
     */
    public String generateCode() {
        String code = getRandomNum(8);
        while (findByCode(code) != null) {
            code = getRandomNum(8);
        }
        return code;
    }

    /**
     * 查询过期的优惠券: 注意：如果timeType=0, 即基于领取时间有效期的优惠券，则优惠券不会过期
     *
     * @return
     */
    public List<TpmCoupon> queryExpired() {
        TpmCouponExample example = new TpmCouponExample();
        example.or().andStatusEqualTo(CouponConstant.STATUS_NORMAL).andTimeTypeEqualTo(CouponConstant.TIME_TYPE_TIME)
                .andEndTimeLessThan(LocalDate.now()).andDeletedEqualTo(false);
        return couponMapper.selectByExample(example);
    }
}
