package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmCouponUserMapper;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.util.CouponUserConstant;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class TpmCouponUserService {
    @Resource
    private TpmCouponUserMapper couponUserMapper;
    @Autowired
    private TpmCouponService tpmCouponService;
    @Autowired
    @Lazy
    private CouponVerifyService couponVerifyService;

    @Transactional(rollbackFor = Exception.class)
    public TpmCouponUser verifyCouponQrCode(String couponQrCode) {
        if (couponQrCode == null) {
            throw new RuntimeException("优惠券二维码异常");
        }
        TpmCouponUserExample example = new TpmCouponUserExample();
        example.or().andCouponQrCodeEqualTo(couponQrCode)
                .andDeletedEqualTo(false)
                .andStatusEqualTo(CouponUserConstant.STATUS_USABLE);
        List<TpmCouponUser> couponUserList = couponUserMapper.selectByExample(example);
        if (couponUserList.size() > 1) {
            throw new RuntimeException("优惠券数据异常");
        } else if (couponUserList.size() == 0) {
            throw new RuntimeException("未找到有效的优惠券数据");
        } else {
            TpmCouponUser tpmCouponUser = couponUserList.get(0);
            Integer id = tpmCouponUser.getId();
            TpmCouponUser user = new TpmCouponUser();
            user.setId(id);
            user.setStatus(CouponUserConstant.STATUS_USED);
            user.setUsedType(CouponUserConstant.USED_TYPE_OFFLINE);
            update(user);
            return tpmCouponUser;
        }
    }

    public Integer countCoupon(Integer couponId) {
        TpmCouponUserExample example = new TpmCouponUserExample();
        example.or().andCouponIdEqualTo(couponId).andDeletedEqualTo(false);
        return (int) couponUserMapper.countByExample(example);
    }

    public Integer countUserAndCoupon(Integer userId, Integer couponId) {
        TpmCouponUserExample example = new TpmCouponUserExample();
        TpmCouponUserExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(userId)){
            criteria.andUserIdEqualTo(userId);
        }
        if (Objects.nonNull(couponId)){
            criteria.andCouponIdEqualTo(couponId);
        }
        criteria.andDeletedEqualTo(false);
        return (int) couponUserMapper.countByExample(example);
    }

    public void add(TpmCouponUser couponUser) {
        couponUser.setAddTime(LocalDateTime.now());
        couponUser.setUpdateTime(LocalDateTime.now());
        couponUserMapper.insertSelective(couponUser);
    }

    public Integer countAvailableCoupon(Integer userId) {
        TpmCouponUserExample tpmCouponUserExample = new TpmCouponUserExample();
        tpmCouponUserExample.createCriteria().andLogicalDeleted(false).andUserIdEqualTo(userId).andStatusEqualTo(CouponUserConstant.STATUS_USABLE);
        return (int) couponUserMapper.countByExample(tpmCouponUserExample);
    }

    public List<TpmCouponUser> queryList(Integer userId, Integer couponId, Short status, Integer page, Integer size,
                                         String sort, String order) {
        TpmCouponUserExample example = new TpmCouponUserExample();
        TpmCouponUserExample.Criteria criteria = example.createCriteria();
        if (userId != null) {
            criteria.andUserIdEqualTo(userId);
        }
        if (couponId != null) {
            criteria.andCouponIdEqualTo(couponId);
        }
        if (status != null) {
            criteria.andStatusEqualTo(status);
        }
        criteria.andDeletedEqualTo(false);

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        if (!StringUtils.isEmpty(page) && !StringUtils.isEmpty(size)) {
            PageHelper.startPage(page, size);
        }

        return couponUserMapper.selectByExample(example);
    }

    public List<TpmCouponUser> queryAll(Integer userId, Integer couponId) {
        return queryList(userId, couponId, CouponUserConstant.STATUS_USABLE, null, null, "add_time", "desc");
    }

    public List<TpmCouponUser> queryAll(Integer userId) {
        return queryList(userId, null, CouponUserConstant.STATUS_USABLE, null, null, "add_time", "desc");
    }

    public TpmCouponUser queryOne(Integer userId, Integer couponId) {
        List<TpmCouponUser> couponUserList = queryList(userId, couponId, CouponUserConstant.STATUS_USABLE, 1, 1,
                "add_time", "desc");
        if (couponUserList.size() == 0) {
            return null;
        }
        return couponUserList.get(0);
    }

    public TpmCouponUser findById(Integer id) {
        return couponUserMapper.selectByPrimaryKey(id);
    }

    public int update(TpmCouponUser couponUser) {
        couponUser.setUpdateTime(LocalDateTime.now());
        return couponUserMapper.updateByPrimaryKeySelective(couponUser);
    }

    public List<TpmCouponUser> queryExpired() {
        TpmCouponUserExample example = new TpmCouponUserExample();
        example.or().andStatusEqualTo(CouponUserConstant.STATUS_USABLE).andEndTimeLessThan(LocalDate.now())
                .andDeletedEqualTo(false);
        return couponUserMapper.selectByExample(example);
    }

    public void giveBackCoupon(String orderSn) {
        TpmCouponUserExample tpmCouponUserExample = new TpmCouponUserExample();
        tpmCouponUserExample.or().andOrderSnEqualTo(orderSn).andStatusEqualTo(CouponUserConstant.STATUS_USED)
                .andDeletedEqualTo(false);
        TpmCouponUser tpmCouponUser;
        try {
            tpmCouponUser = couponUserMapper.selectOneByExample(tpmCouponUserExample);
        } catch (TooManyResultsException e) {
            log.error("单号:{} 查询到多条数据 不处理了", orderSn);
            return;
        }
        if (Objects.isNull(tpmCouponUser)) {
            log.info("单号:{} 没有找到优惠券", orderSn);
            return;
        }
        Integer couponId = tpmCouponUser.getCouponId();
        TpmCoupon tpmCoupon = tpmCouponService.findById(couponId);
        if (Objects.isNull(tpmCoupon)) {
            return;
        }
        boolean isAvailable = couponVerifyService.CheckExpirationDate(tpmCoupon, tpmCouponUser);
        log.info("单号:{} 优惠券id:{} 优惠券:{} 是否可用:{}", orderSn, tpmCoupon.getId(), tpmCoupon.getName(), isAvailable);
        if (isAvailable) {
            TpmCouponUser update = new TpmCouponUser();
            update.setId(tpmCouponUser.getId());
            update.setStatus(CouponUserConstant.STATUS_USABLE);
            int i = couponUserMapper.updateByPrimaryKeySelective(update);
            log.info("单号:{} 优惠券id:{} 优惠券:{} 更新状态为可用:{}", orderSn, tpmCoupon.getId(), tpmCoupon.getName(), i);
        }
    }

    public Map<Integer, Integer> countByUserId(List<TpmUser> userList) {
        Map<Integer, Integer> userCouponCount = new HashMap<>();
        for (TpmUser user : userList) {
            Integer userId = user.getId();
            Integer count = countUserAndCoupon(userId, null);
            userCouponCount.put(userId, count);
        }
        return userCouponCount;
    }
}
