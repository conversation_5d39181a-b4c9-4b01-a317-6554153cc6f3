package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmDeliveryRecordMapper;
import com.pioneer.mall.db.domain.TpmDeliveryRecord;
import com.pioneer.mall.db.domain.TpmDeliveryRecordExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/31 10:18
 */
@Service
public class TpmDeliveryRecordService {
    @Autowired
    private TpmDeliveryRecordMapper tpmDeliveryRecordMapper;

    public int add(TpmDeliveryRecord tpmDeliveryRecord) {
        tpmDeliveryRecord.setAddTime(LocalDateTime.now());
        tpmDeliveryRecord.setUpdateTime(LocalDateTime.now());
        return tpmDeliveryRecordMapper.insertSelective(tpmDeliveryRecord);
    }

    public int update(TpmDeliveryRecord tpmDeliveryRecord) {
        return tpmDeliveryRecordMapper.updateByPrimaryKeySelective(tpmDeliveryRecord);
    }

    public TpmDeliveryRecord findById(Integer id) {
        return tpmDeliveryRecordMapper.selectByPrimaryKey(id);
    }

    public int deleteById(Integer id) {
        return tpmDeliveryRecordMapper.deleteByPrimaryKey(id);
    }

    public long countByTpmOrderId(Integer id) {
        TpmDeliveryRecordExample tpmDeliveryRecordExample = new TpmDeliveryRecordExample();
        tpmDeliveryRecordExample.or().andTpmOrderIdEqualTo(id).andLogicalDeleted(false);
        return tpmDeliveryRecordMapper.countByExample(tpmDeliveryRecordExample);
    }

    public List<TpmDeliveryRecord> findByOrderId(String orderId) {
        TpmDeliveryRecordExample tpmDeliveryRecordExample = new TpmDeliveryRecordExample();
        tpmDeliveryRecordExample.or().andOrderIdEqualTo(orderId).andLogicalDeleted(false);
        return tpmDeliveryRecordMapper.selectByExample(tpmDeliveryRecordExample);
    }

    public List<TpmDeliveryRecord> findByTpmOrderId(Integer tpmOrderId) {
        TpmDeliveryRecordExample tpmDeliveryRecordExample = new TpmDeliveryRecordExample();
        tpmDeliveryRecordExample.or().andTpmOrderIdEqualTo(tpmOrderId).andLogicalDeleted(false);
        tpmDeliveryRecordExample.setOrderByClause("add_time desc");
        return tpmDeliveryRecordMapper.selectByExample(tpmDeliveryRecordExample);
    }


}
