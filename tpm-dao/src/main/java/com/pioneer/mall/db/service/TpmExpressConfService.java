package com.pioneer.mall.db.service;

import com.alibaba.fastjson.JSONArray;
import com.pioneer.mall.db.dao.TpmExpressConfMapper;
import com.pioneer.mall.db.domain.TpmAddress;
import com.pioneer.mall.db.domain.TpmExpressConf;
import com.pioneer.mall.db.domain.TpmExpressConfExample;
import com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.enums.TpmTransportTypeEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class TpmExpressConfService {


    private static final Object syncLock = new Object();

    @Autowired
    private TpmExpressConfMapper tpmExpressConfMapper;

    /**
     * 城市价格缓存：
     * key : expressFeeType_cityCode
     * value : 价格
     */
    private Map<String, BigDecimal> cityPriceCacheMap = new ConcurrentHashMap<>();


    /**
     * 获取运费配置的加个
     *
     * @param expressFeeType 运费类型  {@link com.pioneer.mall.db.enums.TpmTransportTypeEnums}
     * @param cityCode       城市变慢
     * @return
     */
    public BigDecimal getConfPriceByCityCode(Integer expressFeeType, TpmAddress tpmAddress) {
        if (Objects.isNull(tpmAddress)){
            return null;
        }
        Integer cityCode = tpmAddress.getCityId();
        if (expressFeeType == null || cityCode == null) {
            return null;
        }

        String key = expressFeeType + "_" + cityCode;
        BigDecimal price = cityPriceCacheMap.get(key);
        if (price == null && CollectionUtils.isEmpty(cityPriceCacheMap)) {
            init();
            price = cityPriceCacheMap.get(key);
        }
        if (price == null){
            //todo 需要添加 城市名反查运费的方法 eg. 杭州市 ，荆州市
        }
        if (price == null) {
            return BigDecimal.ZERO;
        }
        return price;
    }

    public void init() {
        List<TpmTransportTypeEnums> list = Arrays.asList(TpmTransportTypeEnums.COLD_CHAIN, TpmTransportTypeEnums.COMMON);
        List<TpmExpressConfWithBLOBs> result = new ArrayList<>();
        for (TpmTransportTypeEnums tpmTransportTypeEnums : list) {
            List<TpmExpressConfWithBLOBs> tpmExpressConfWithBLOBsList = this.getExpressConfByExpressFeeType(tpmTransportTypeEnums.getCode());
            result.addAll(tpmExpressConfWithBLOBsList);
        }
        if (!CollectionUtils.isEmpty(result)){
            initCityPriceCacheMap(result);
        }
    }


    public void init(Integer expressFeeType) {
        List<TpmExpressConfWithBLOBs> tpmExpressConfWithBLOBsList = this.getExpressConfByExpressFeeType(expressFeeType);
        initCityPriceCacheMap(tpmExpressConfWithBLOBsList);
    }


    public List<TpmExpressConfWithBLOBs> getExpressConfByExpressFeeType(Integer expressFeeType) {
        TpmExpressConfExample tpmExpressConfExample = new TpmExpressConfExample();
        tpmExpressConfExample.createCriteria().andExpressFeeTypeEqualTo(expressFeeType)
                .andDeletedEqualTo(false);
        return tpmExpressConfMapper.selectByExampleWithBLOBs(tpmExpressConfExample);
    }

    @Transactional(rollbackFor = Exception.class)
    public void upsert(Integer expressFeeType, List<TpmExpressConfWithBLOBs> expressConfWithBLOBsList) {

        // 简单处理，先将已有配置删除，再新增
        TpmExpressConfExample tpmExpressConfExample = new TpmExpressConfExample();
        tpmExpressConfExample.createCriteria().andDeletedEqualTo(false)
                .andExpressFeeTypeEqualTo(expressFeeType);
        TpmExpressConfWithBLOBs record = new TpmExpressConfWithBLOBs();
        record.setDeleted(true);
        record.setUpdateTime(LocalDateTime.now());
        tpmExpressConfMapper.updateByExampleSelective(record, tpmExpressConfExample);

        // 新增
        for (TpmExpressConfWithBLOBs tpmExpressConfWithBLOBs : expressConfWithBLOBsList) {
            tpmExpressConfMapper.insert(tpmExpressConfWithBLOBs);
        }
        initCityPriceCacheMap(expressConfWithBLOBsList);
    }


    private void initCityPriceCacheMap(List<TpmExpressConfWithBLOBs> tpmExpressConfList) {

        if (CollectionUtils.isEmpty(tpmExpressConfList)) {
            return;
        }
        synchronized (syncLock) {
            cityPriceCacheMap.clear();
            for (TpmExpressConfWithBLOBs tpmExpressConf : tpmExpressConfList) {
                Integer expressFeeType = tpmExpressConf.getExpressFeeType();
                List<String> cityCodeList = JSONArray.parseArray(tpmExpressConf.getDeliveryAreaCode(), String.class);
                for (String s : cityCodeList) {
                    cityPriceCacheMap.put(expressFeeType + "_" + s, tpmExpressConf.getShippingFee());
                }
            }
        }
    }


}
