package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmFeedbackMapper;
import com.pioneer.mall.db.domain.TpmFeedback;
import com.pioneer.mall.db.domain.TpmFeedbackExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/12/27
 */
@Service
public class TpmFeedbackService {
	@Autowired
	private TpmFeedbackMapper feedbackMapper;

	public Integer add(TpmFeedback feedback) {
		feedback.setAddTime(LocalDateTime.now());
		feedback.setUpdateTime(LocalDateTime.now());
		return feedbackMapper.insertSelective(feedback);
	}

	public List<TpmFeedback> querySelective(Integer userId, String username, Integer page, Integer limit, String sort,
											String order) {
		TpmFeedbackExample example = new TpmFeedbackExample();
		TpmFeedbackExample.Criteria criteria = example.createCriteria();

		if (userId != null) {
			criteria.andUserIdEqualTo(userId);
		}
		if (!StringUtils.isEmpty(username)) {
			criteria.andUsernameLike("%" + username + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, limit);
		return feedbackMapper.selectByExample(example);
	}
}
