package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmFootprintMapper;
import com.pioneer.mall.db.domain.TpmFootprint;
import com.pioneer.mall.db.domain.TpmFootprintExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmFootprintService {
	@Resource
	private TpmFootprintMapper footprintMapper;

	public List<TpmFootprint> queryByAddTime(Integer userId, Integer page, Integer size) {
		TpmFootprintExample example = new TpmFootprintExample();
		example.or().andUserIdEqualTo(userId).andDeletedEqualTo(false);
		example.setOrderByClause(TpmFootprint.Column.addTime.desc());
		PageHelper.startPage(page, size);
		return footprintMapper.selectByExample(example);
	}

	public TpmFootprint findById(Integer id) {
		return footprintMapper.selectByPrimaryKey(id);
	}

	public void deleteById(Integer id) {
		footprintMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmFootprint footprint) {
		footprint.setAddTime(LocalDateTime.now());
		footprint.setUpdateTime(LocalDateTime.now());
		footprintMapper.insertSelective(footprint);
	}

	public List<TpmFootprint> querySelective(String userId, String goodsId, Integer page, Integer size, String sort,
			String order) {
		TpmFootprintExample example = new TpmFootprintExample();
		TpmFootprintExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(userId)) {
			criteria.andUserIdEqualTo(Integer.valueOf(userId));
		}
		if (!StringUtils.isEmpty(goodsId)) {
			criteria.andGoodsIdEqualTo(Integer.valueOf(goodsId));
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return footprintMapper.selectByExample(example);
	}
}
