package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmFreightStepPriceMapper;
import com.pioneer.mall.db.domain.TpmFreightStepPrice;
import com.pioneer.mall.db.domain.TpmFreightStepPriceExample;
import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
public class TpmFreightStepPriceService {
    private static final Logger logger = LoggerFactory.getLogger(TpmFreightStepPriceService.class);
    @Autowired
    private TpmFreightStepPriceMapper TpmFreightStepPriceMapper;


    @Transactional(rollbackFor = Exception.class)
    public void add(TpmFreightStepPrice freightStepPrice) {
        freightStepPrice.setAddTime(LocalDateTime.now());
        freightStepPrice.setUpdateTime(LocalDateTime.now());
        freightStepPrice.setDeleted(false);
        TpmFreightStepPriceMapper.insertSelective(freightStepPrice);
    }

    public List<TpmFreightStepPrice> querySelective(TpmFreightStepPrice tpmVipSearch, String sort, String order, Integer pageNume, Integer pageSize, Integer shopId) {
        TpmFreightStepPriceExample freightStepPriceExample = new TpmFreightStepPriceExample();
        TpmFreightStepPriceExample.Criteria criteria = freightStepPriceExample.createCriteria();
        String shopName = tpmVipSearch.getShopName();
        if (Objects.nonNull(shopName)) {
            criteria.andShopNameLike("%" + shopName + "%");
        }
        String shopCode = tpmVipSearch.getShopCode();
        if (Objects.nonNull(shopCode)) {
            criteria.andShopCodeEqualTo(shopCode);
        }
        criteria.andLogicalDeleted(false);

        if (Objects.nonNull(shopId)) {
            criteria.andShopIdEqualTo(shopId);
        } else {
            List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
            if (!CollectionUtils.isEmpty(userShopIdList)) {
                criteria.andShopIdIn(userShopIdList);
            }
        }
        // 排序
        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            freightStepPriceExample.setOrderByClause(sort + " " + order);
        }
        PageHelper.startPage(pageNume, pageSize);
        return TpmFreightStepPriceMapper.selectByExample(freightStepPriceExample);
    }

    public TpmFreightStepPrice findById(Long id) {
        return TpmFreightStepPriceMapper.selectByPrimaryKey(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateById(TpmFreightStepPrice tpmFreightStepPrice) {
        if (tpmFreightStepPrice.getId() == null) {
            return 0;
        }
        tpmFreightStepPrice.setUpdateTime(LocalDateTime.now());
        return TpmFreightStepPriceMapper.updateByPrimaryKeySelective(tpmFreightStepPrice);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteById(TpmFreightStepPrice tpmFreightStepPrice) {
        if (Objects.isNull(tpmFreightStepPrice) || Objects.isNull(tpmFreightStepPrice.getId())) {
            return 0;
        }
        return TpmFreightStepPriceMapper.deleteByPrimaryKey(tpmFreightStepPrice.getId());
    }

    public TpmFreightStepPrice getFreightPriceByShopCodeAndDistance(Integer shopId, Integer distance) throws Exception {
        List<TpmFreightStepPrice> freightStepPriceByShopCodeAndType = this.getFreightPriceByShopIdAndDistance(shopId);
        if (freightStepPriceByShopCodeAndType.isEmpty()) {
            return null;
        }
        long maxRange = freightStepPriceByShopCodeAndType.stream().mapToLong(TpmFreightStepPrice::getRangeEnd).max().getAsLong();
        if (distance > maxRange) {
            logger.error("超出配送范围 最大距离:" + maxRange + " 计算距离:" + distance);
            throw new Exception("超出最大配送范围");
        }
        for (TpmFreightStepPrice price : freightStepPriceByShopCodeAndType) {
            if (distance >= price.getRangeStart() && distance < price.getRangeEnd()) {
                return price;
            }
        }
        return null;
    }

    public List<TpmFreightStepPrice> getFreightStepPriceByShopCodeAndType(String shopCode, Integer type) {
        TpmFreightStepPriceExample tpmFreightStepPriceExample = new TpmFreightStepPriceExample();
        tpmFreightStepPriceExample.createCriteria().andShopCodeEqualTo(shopCode).andTypeEqualTo(type).andLogicalDeleted(false);
        return TpmFreightStepPriceMapper.selectByExample(tpmFreightStepPriceExample);
    }

    public List<TpmFreightStepPrice> getFreightPriceByShopIdAndDistance(Integer shopId) {
        TpmFreightStepPriceExample tpmFreightStepPriceExample = new TpmFreightStepPriceExample();
        tpmFreightStepPriceExample.createCriteria().andShopIdEqualTo(shopId).andLogicalDeleted(false);
        return TpmFreightStepPriceMapper.selectByExample(tpmFreightStepPriceExample);
    }
}
