package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmGoodsAttributeMapper;
import com.pioneer.mall.db.domain.TpmGoodsAttribute;
import com.pioneer.mall.db.domain.TpmGoodsAttributeExample;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class TpmGoodsAttributeService {
    @Resource
    private TpmGoodsAttributeMapper goodsAttributeMapper;

    public List<TpmGoodsAttribute> queryByGid(Integer goodsId) {
        TpmGoodsAttributeExample example = new TpmGoodsAttributeExample();
        example.or().andGoodsIdEqualTo(goodsId).andDeletedEqualTo(false);
        return goodsAttributeMapper.selectByExample(example);
    }

    public List<TpmGoodsAttribute> queryByGid(List<Integer> goodsId) {
        if (CollectionUtils.isEmpty(goodsId)){
            return new ArrayList<>();
        }
        TpmGoodsAttributeExample example = new TpmGoodsAttributeExample();
        example.or().andGoodsIdIn(goodsId).andDeletedEqualTo(false);
        return goodsAttributeMapper.selectByExample(example);
    }

    public void add(TpmGoodsAttribute goodsAttribute) {
        goodsAttribute.setAddTime(LocalDateTime.now());
        goodsAttribute.setUpdateTime(LocalDateTime.now());
        goodsAttributeMapper.insertSelective(goodsAttribute);
    }

    public TpmGoodsAttribute findById(Integer id) {
        if (id == null) {
            return null;
        }
        return goodsAttributeMapper.selectByPrimaryKey(id);
    }

    public void deleteByGid(Integer gid) {
        TpmGoodsAttributeExample example = new TpmGoodsAttributeExample();
        example.or().andGoodsIdEqualTo(gid);
        goodsAttributeMapper.logicalDeleteByExample(example);
    }
}
