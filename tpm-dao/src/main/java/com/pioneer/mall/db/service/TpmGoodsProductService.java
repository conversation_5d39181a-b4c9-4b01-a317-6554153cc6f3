package com.pioneer.mall.db.service;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;

import com.pioneer.mall.db.dao.TpmGoodsProductMapper;
import com.pioneer.mall.db.domain.TpmGoodsProduct;
import com.pioneer.mall.db.domain.TpmGoodsProductExample;
import org.springframework.stereotype.Service;

import com.pioneer.mall.db.dao.ex.GoodsProductMapper;

@Service
public class TpmGoodsProductService {
	@Resource
	private TpmGoodsProductMapper tpmGoodsProductMapper;
	@Resource
	private GoodsProductMapper goodsProductMapper;

	public List<TpmGoodsProduct> queryByGid(Integer gid) {
		TpmGoodsProductExample example = new TpmGoodsProductExample();
		example.or().andGoodsIdEqualTo(gid).andDeletedEqualTo(false);
		return tpmGoodsProductMapper.selectByExample(example);
	}

	public TpmGoodsProduct findById(Integer id) {
		return tpmGoodsProductMapper.selectByPrimaryKey(id);
	}

	public void deleteById(Integer id) {
		tpmGoodsProductMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmGoodsProduct goodsProduct) {
		goodsProduct.setAddTime(LocalDateTime.now());
		goodsProduct.setUpdateTime(LocalDateTime.now());
		tpmGoodsProductMapper.insertSelective(goodsProduct);
	}

	public int count() {
		TpmGoodsProductExample example = new TpmGoodsProductExample();
		example.or().andDeletedEqualTo(false);
		return (int) tpmGoodsProductMapper.countByExample(example);
	}

	public void deleteByGid(Integer gid) {
		TpmGoodsProductExample example = new TpmGoodsProductExample();
		example.or().andGoodsIdEqualTo(gid);
		tpmGoodsProductMapper.logicalDeleteByExample(example);
	}

	public int addStock(Integer id, Short num) {
		return goodsProductMapper.addStock(id, num);
	}

	public int addBrowse(Integer id, Short num) {
		return goodsProductMapper.addBrowse(id, num);// 新增商品流量量
	}

	public int reduceStock(Integer id, Integer goodsId, Short num) {
		goodsProductMapper.addSales(goodsId, num);// 每次需将商品的销售量加下
		return goodsProductMapper.reduceStock(id, num);
	}
}