package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.bean.dto.TpmGoodsFullInfoDTO;
import com.pioneer.mall.db.dao.TpmGoodsMapper;
import com.pioneer.mall.db.dao.ex.GoodsInventoryMapper;
import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.domain.TpmGoods.Column;
import com.pioneer.mall.db.domain.TpmGoodsExample;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.util.SaleRangeEnum;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class TpmGoodsService {
    TpmGoods.Column[] columns = new Column[]{Column.id, Column.name, Column.brief, Column.picUrl, Column.isHot, Column.isNew,
            Column.counterPrice, Column.retailPrice};
    @Resource
    private TpmGoodsMapper goodsMapper;
    @Resource
    private GoodsInventoryMapper goodsInventoryMapper;
    @Resource
    private TpmAttributeSpecificationService tpmAttributeSpecificationService;

    /**
     * 获取热卖商品
     *
     * @param offset
     * @param limit
     * @return
     */
    public List<TpmGoods> queryByHot(int offset, int limit) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIsHotEqualTo(true)
                .andIsOnSaleEqualTo(true)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andDeletedEqualTo(false);
        example.setOrderByClause("browse desc");
        PageHelper.startPage(offset, limit);

        return goodsMapper.selectByExampleSelective(example, columns);
    }

    /**
     * 获取新品上市
     *
     * @param offset
     * @param limit
     * @return
     */
    public List<TpmGoods> queryByNew(int offset, int limit) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIsNewEqualTo(true)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andIsOnSaleEqualTo(true).andDeletedEqualTo(false);
        example.setOrderByClause("add_time desc");
        PageHelper.startPage(offset, limit);

        return goodsMapper.selectByExampleSelective(example, columns);
    }

    /**
     * 获取分类下的商品
     *
     * @param catList
     * @param offset
     * @param limit
     * @return
     */
    public List<TpmGoods> queryByCategory(List<Integer> catList, int offset, int limit) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andCategoryIdIn(catList)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andIsOnSaleEqualTo(true).andDeletedEqualTo(false);
        example.setOrderByClause("sort_order  asc");
        PageHelper.startPage(offset, limit);

        return goodsMapper.selectByExampleSelective(example, columns);
    }

    /**
     * 获取分类下的商品
     *
     * @param catId
     * @param offset
     * @param limit
     * @return
     */
    public List<TpmGoods> queryByCategory(Integer catId, int offset, int limit) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andCategoryIdEqualTo(catId)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andIsOnSaleEqualTo(true).andDeletedEqualTo(false);
        example.setOrderByClause("add_time desc");
        PageHelper.startPage(offset, limit);

        return goodsMapper.selectByExampleSelective(example, columns);
    }

    public List<TpmGoods> querySelective(Integer catId, Integer businessType,String keywords, Boolean isHot, Boolean isNew,
                                         Integer offset, Integer limit, String sort, String order) {
        TpmGoodsExample example = new TpmGoodsExample();
        TpmGoodsExample.Criteria criteria1 = example.or();
        TpmGoodsExample.Criteria criteria2 = example.or();
        criteria1.andBusinessTypeEqualTo(businessType);
        criteria2.andBusinessTypeEqualTo(businessType);
        if (!StringUtils.isEmpty(catId) && catId != 0) {
            criteria1.andCategoryIdEqualTo(catId);
            criteria2.andCategoryIdEqualTo(catId);
        }
        if (!StringUtils.isEmpty(isNew)) {
            criteria1.andIsNewEqualTo(isNew);
            criteria2.andIsNewEqualTo(isNew);
        }
        if (!StringUtils.isEmpty(isHot)) {
            criteria1.andIsHotEqualTo(isHot);
            criteria2.andIsHotEqualTo(isHot);
        }
        if (!StringUtils.isEmpty(keywords)) {
            criteria1.andKeywordsLike("%" + keywords + "%");
            criteria2.andNameLike("%" + keywords + "%");
        }
        criteria1.andIsOnSaleEqualTo(true);
        criteria2.andIsOnSaleEqualTo(true);
        criteria1.andDeletedEqualTo(false);
        criteria2.andDeletedEqualTo(false);

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        PageHelper.startPage(offset, limit);

        return goodsMapper.selectByExampleSelective(example, columns);
    }

    public List<TpmGoods> querySelective(String goodsSn, String name, Integer categoryId, Integer businessType, Integer page, Integer size, String sort,
                                         String order, List<Integer> brandIds, Integer shopId) {
        TpmGoodsExample example = new TpmGoodsExample();
        TpmGoodsExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(businessType)){
            criteria.andBusinessTypeEqualTo(businessType);
        }else{
            criteria.andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode());
        }
        if (!StringUtils.isEmpty(goodsSn)) {
            criteria.andGoodsSnEqualTo(goodsSn);
        }
        if (!StringUtils.isEmpty(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        criteria.andDeletedEqualTo(false);

        if (brandIds != null && brandIds.size() > 0) {
            criteria.andBrandIdIn(brandIds);
        }

        if (Objects.nonNull(categoryId)) {
            criteria.andCategoryIdEqualTo(categoryId);
        }
        // 判断下shopid
        if (shopId != null) {
            criteria.andShopIdEqualTo(shopId);
        } else {
            List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
            if (!CollectionUtils.isEmpty(userShopIdList)) {
                criteria.andShopIdIn(userShopIdList);
            }
        }

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        PageHelper.startPage(page, size);
        return goodsMapper.selectByExampleWithBLOBs(example);
    }

    /**
     * 获取某个商品信息,包含完整信息
     *
     * @param id
     * @return
     */
    public TpmGoods findById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIdEqualTo(id).andDeletedEqualTo(false);
        return goodsMapper.selectOneByExampleWithBLOBs(example);
    }

    /**
     * 获取某个商品信息,包含完整信息，支持店铺维度
     * 在多店铺模式下，可以根据shopId获取特定店铺的商品信息
     * 如果shopId为null，则使用全局商品信息
     *
     * @param id 商品ID
     * @param shopId 店铺ID，为null时获取全局商品信息
     * @return
     */
    public TpmGoods findById(Integer id, Integer shopId) {
        if (Objects.isNull(id)) {
            return null;
        }

        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIdEqualTo(id).andShopIdEqualTo(shopId).andDeletedEqualTo(false);

        TpmGoods goods = goodsMapper.selectOneByExampleWithBLOBs(example);

        // 如果指定了shopId，可以在这里添加店铺特定的逻辑
        // 例如：检查商品是否在该店铺可用、获取店铺特定的价格等
        if (shopId != null && goods != null) {
            // TODO: 添加店铺特定的商品逻辑
            // 例如：检查商品在该店铺的可用性、库存等
        }

        return goods;
    }


    public List<TpmGoods> findById(List<Integer> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return new ArrayList<>();
        }
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIdIn(goodsIdList).andDeletedEqualTo(false);
        return goodsMapper.selectByExampleWithBLOBs(example);
    }

    /**
     * 根据序列码找到商品
     *
     * @param goodsSn
     * @return
     */
    public TpmGoods findByGoodsSn(String goodsSn) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andGoodsSnEqualTo(goodsSn).andDeletedEqualTo(false);
        return goodsMapper.selectOneByExampleWithBLOBs(example);
    }

    /**
     * 获取某个商品信息，仅展示相关内容
     *
     * @param id
     * @return
     */
    public TpmGoods findByIdVO(Integer id) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIdEqualTo(id).andIsOnSaleEqualTo(true).andDeletedEqualTo(false);
        return goodsMapper.selectOneByExampleSelective(example, columns);
    }

    public TpmGoods findBySnVO(String sn) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andGoodsSnEqualTo(sn).andIsOnSaleEqualTo(true).andDeletedEqualTo(false);
        return goodsMapper.selectOneByExampleSelective(example, columns);
    }

    /**
     * 获取所有在售物品总数
     *
     * @return
     */
    public Integer queryOnSale() {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIsOnSaleEqualTo(true)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andDeletedEqualTo(false);
        return (int) goodsMapper.countByExample(example);
    }
    public Integer queryOnSale(Integer businessType) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andIsOnSaleEqualTo(true)
                .andBusinessTypeEqualTo(businessType)
                .andDeletedEqualTo(false);
        return (int) goodsMapper.countByExample(example);
    }

    public int updateById(TpmGoods goods) {
        goods.setUpdateTime(LocalDateTime.now());
        return goodsMapper.updateByPrimaryKeySelective(goods);
    }

    public void deleteById(Integer id) {
        goodsMapper.logicalDeleteByPrimaryKey(id);
    }

    public void add(TpmGoods goods) {
        goods.setAddTime(LocalDateTime.now());
        goods.setUpdateTime(LocalDateTime.now());
        goodsMapper.insertSelective(goods);
    }

    /**
     * 获取所有物品总数，包括在售的和下架的，但是不包括已删除的商品
     *
     * @return
     */
    public int count() {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or()
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andDeletedEqualTo(false);
        return (int) goodsMapper.countByExample(example);
    }

    public List<Integer> getCatIds(Integer brandId, Integer businessType, String keywords, Boolean isHot, Boolean isNew) {
        TpmGoodsExample example = new TpmGoodsExample();
        TpmGoodsExample.Criteria criteria1 = example.or();
        TpmGoodsExample.Criteria criteria2 = example.or();
        criteria1.andBusinessTypeEqualTo(businessType);
        criteria2.andBusinessTypeEqualTo(businessType);
        if (!StringUtils.isEmpty(brandId)) {
            criteria1.andBrandIdEqualTo(brandId);
            criteria2.andBrandIdEqualTo(brandId);
        }
        if (!StringUtils.isEmpty(isNew)) {
            criteria1.andIsNewEqualTo(isNew);
            criteria2.andIsNewEqualTo(isNew);
        }
        if (!StringUtils.isEmpty(isHot)) {
            criteria1.andIsHotEqualTo(isHot);
            criteria2.andIsHotEqualTo(isHot);
        }
        if (!StringUtils.isEmpty(keywords)) {
            criteria1.andKeywordsLike("%" + keywords + "%");
            criteria2.andNameLike("%" + keywords + "%");
        }
        criteria1.andIsOnSaleEqualTo(true);
        criteria2.andIsOnSaleEqualTo(true);
        criteria1.andDeletedEqualTo(false);
        criteria2.andDeletedEqualTo(false);

        List<TpmGoods> goodsList = goodsMapper.selectByExampleSelective(example, Column.categoryId);
        List<Integer> cats = new ArrayList<Integer>();
        for (TpmGoods goods : goodsList) {
            cats.add(goods.getCategoryId());
        }
        return cats;
    }

    public boolean checkExistByName(String name, Integer businessType) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andNameEqualTo(name)
                .andBusinessTypeEqualTo(businessType)
                .andIsOnSaleEqualTo(true).andDeletedEqualTo(false);
        return goodsMapper.countByExample(example) != 0;
    }

    /**
     * 根据店铺，获取店铺对应类别的商品
     *
     * @param brandId
     * @param i
     * @param related
     * @return
     */
    public List<TpmGoods> queryByBrandId(int bid, int cid, int offset, int limit) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andBrandIdEqualTo(bid)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andCategoryIdEqualTo(cid).andIsOnSaleEqualTo(true).andDeletedEqualTo(false);
        example.setOrderByClause("browse desc");
        PageHelper.startPage(offset, limit);

        return goodsMapper.selectByExampleSelective(example, columns);
    }

    /**
     * 同类商品，且不同店铺
     *
     * @param brandId
     * @param cid
     * @param i
     * @param limitCid
     * @return
     */
    public List<TpmGoods> queryByCategoryAndNotSameBrandId(int bid, int cid, int offset, int limit) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.or().andBrandIdNotEqualTo(bid)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andCategoryIdEqualTo(cid).andIsOnSaleEqualTo(true)
                .andDeletedEqualTo(false);
        example.setOrderByClause("browse desc");
        PageHelper.startPage(offset, limit);

        return goodsMapper.selectByExampleSelective(example, columns);
    }

    public List<TpmGoods> listAll() {
        TpmGoodsExample example = new TpmGoodsExample();
        example.createCriteria()
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andLogicalDeleted(false);
        return goodsMapper.selectByExample(example);
    }

    public List<TpmGoods> listAllOnSell() {
        TpmGoodsExample example = new TpmGoodsExample();
        example.createCriteria()
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andIsOnSaleEqualTo(true).andLogicalDeleted(false);
        return goodsMapper.selectByExample(example);
    }


    public List<TpmGoods> listAllOnSellBySaleRange(Integer pageSaleRange, Integer businessType) {
        String condition = "";
        if (Objects.equals(businessType, TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())){
            Integer dbSaleRange = SaleRangeEnum.getEnumByPageSaleRange(pageSaleRange).getDbSaleRange();
            if (dbSaleRange != null) {
                condition = "sale_range & " + dbSaleRange + " = " + dbSaleRange;
            }
        }
        TpmGoodsExample example = new TpmGoodsExample();
        example.createCriteria()
                .addCondition(condition)
                .andBusinessTypeEqualTo(businessType)
                .andIsOnSaleEqualTo(true)
                .andLogicalDeleted(false);
        return goodsMapper.selectByExample(example);
    }

    /**
     * 根据销售范围、业务类型和店铺ID获取在售商品列表
     * @param pageSaleRange 页面销售范围
     * @param businessType 业务类型
     * @param shopId 店铺ID
     * @return 商品列表
     */
    public List<TpmGoods> listAllOnSellBySaleRange(Integer pageSaleRange, Integer businessType, Integer shopId) {
        String condition = "";
        if (Objects.equals(businessType, TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())){
            Integer dbSaleRange = SaleRangeEnum.getEnumByPageSaleRange(pageSaleRange).getDbSaleRange();
            if (dbSaleRange != null) {
                condition = "sale_range & " + dbSaleRange + " = " + dbSaleRange;
            }
        }
        TpmGoodsExample example = new TpmGoodsExample();
        TpmGoodsExample.Criteria criteria = example.createCriteria()
                .addCondition(condition)
                .andBusinessTypeEqualTo(businessType)
                .andIsOnSaleEqualTo(true)
                .andLogicalDeleted(false);

        // 添加店铺ID过滤条件
        if (shopId != null) {
            criteria.andShopIdEqualTo(shopId);
        }

        return goodsMapper.selectByExample(example);
    }


    public List<TpmGoods> listAllOnSellByBusinessTypeAndName(Integer businessType,String name) {
        TpmGoodsExample example = new TpmGoodsExample();
        TpmGoodsExample.Criteria criteria = example.createCriteria()
                .andBusinessTypeEqualTo(businessType)
                .andIsOnSaleEqualTo(true)
                .andLogicalDeleted(false);
        if (!StringUtils.isEmpty(name)){
            criteria.andNameLike("%"+name+"%");
        }
        return goodsMapper.selectByExample(example);

    }
    public List<TpmGoods> listAllOnSellByBusinessType(Integer businessType) {
        TpmGoodsExample example = new TpmGoodsExample();
        example.createCriteria()
                .andBusinessTypeEqualTo(businessType)
                .andIsOnSaleEqualTo(true)
                .andLogicalDeleted(false);
        return goodsMapper.selectByExample(example);
    }

    public TpmGoodsFullInfoDTO findFullInfoDTOById(Integer id) {
        TpmGoods tpmGoods = this.findById(id);
        if (Objects.isNull(tpmGoods)) {
            return null;
        }
        List<TpmGoodsAttributesDto> tpmGoodsAttributesDtos = tpmAttributeSpecificationService.queryByGid(id);
        return new TpmGoodsFullInfoDTO(tpmGoods, tpmGoodsAttributesDtos);
    }

    public List<TpmGoodsFullInfoDTO> findFullInfoDTOByIds(List<Integer> ids) {
        List<TpmGoods> tpmGoodsList = this.findById(ids);
        if (CollectionUtils.isEmpty(tpmGoodsList)) {
            return new ArrayList<>();
        }
        List<TpmGoodsAttributesDto> tpmGoodsAttributesDtos = tpmAttributeSpecificationService.queryByGid(ids);
        Map<Integer, List<TpmGoodsAttributesDto>> goodsIdAttributesMap = tpmGoodsAttributesDtos.stream().collect(Collectors.groupingBy(TpmGoodsAttributesDto::getGoodsId));
        return tpmGoodsList.stream().map(tpmGoods -> new TpmGoodsFullInfoDTO(tpmGoods, goodsIdAttributesMap.getOrDefault(tpmGoods.getId(), new ArrayList<>()))).collect(Collectors.toList());
    }


    public int addInventory(Integer goodsId, Short number) {
        return goodsInventoryMapper.addInventory(goodsId, number);
    }

    public int reduceInventory(Integer goodsId, Short number) {
        return goodsInventoryMapper.reduceInventory(goodsId, number);
    }
}
