package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmGoodsSpecificationMapper;
import com.pioneer.mall.db.domain.TpmGoodsSpecification;
import com.pioneer.mall.db.domain.TpmGoodsSpecificationExample;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.dto.TpmGoodsSpecificationsDto;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class TpmGoodsSpecificationService {
    @Resource
    private TpmGoodsSpecificationMapper goodsSpecificationMapper;

    public List<TpmGoodsSpecification> queryByAttributeId(Integer id) {
        TpmGoodsSpecificationExample example = new TpmGoodsSpecificationExample();
        example.or().andAttributeIdEqualTo(id).andDeletedEqualTo(false);
        return goodsSpecificationMapper.selectByExample(example);
    }

    public List<TpmGoodsSpecification> queryByGid(Integer id) {
        TpmGoodsSpecificationExample example = new TpmGoodsSpecificationExample();
        example.or().andGoodsIdEqualTo(id).andDeletedEqualTo(false);
        return goodsSpecificationMapper.selectByExample(example);
    }

    public List<TpmGoodsSpecification> queryByGid(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        TpmGoodsSpecificationExample example = new TpmGoodsSpecificationExample();
        example.or().andGoodsIdIn(idList).andDeletedEqualTo(false);
        return goodsSpecificationMapper.selectByExample(example);
    }

    public TpmGoodsSpecification findById(Integer id) {
        return goodsSpecificationMapper.selectByPrimaryKey(id);
    }

    public void deleteByGid(Integer gid) {
        TpmGoodsSpecificationExample example = new TpmGoodsSpecificationExample();
        example.or().andGoodsIdEqualTo(gid);
        goodsSpecificationMapper.logicalDeleteByExample(example);
    }

    public void add(TpmGoodsSpecification goodsSpecification) {
        goodsSpecification.setAddTime(LocalDateTime.now());
        goodsSpecification.setUpdateTime(LocalDateTime.now());
        goodsSpecificationMapper.insertSelective(goodsSpecification);
    }

    /**
     * [ { name: '', valueList: [ {}, {}] }, { name: '', valueList: [ {}, {}] } ]
     *
     * @param id
     * @return
     */
    public List<VO> getSpecificationVoList(Integer id) {
        List<TpmGoodsSpecification> goodsSpecificationList = queryByGid(id);

        Map<String, VO> map = new HashMap<>();
        List<VO> specificationVoList = new ArrayList<>();

        for (TpmGoodsSpecification goodsSpecification : goodsSpecificationList) {
            String specification = goodsSpecification.getSpecification();
            VO goodsSpecificationVo = map.get(specification);
            if (goodsSpecificationVo == null) {
                goodsSpecificationVo = new VO();
                goodsSpecificationVo.setName(specification);
                List<TpmGoodsSpecification> valueList = new ArrayList<>();
                valueList.add(goodsSpecification);
                goodsSpecificationVo.setValueList(valueList);
                map.put(specification, goodsSpecificationVo);
                specificationVoList.add(goodsSpecificationVo);
            } else {
                List<TpmGoodsSpecification> valueList = goodsSpecificationVo.getValueList();
                valueList.add(goodsSpecification);
            }
        }

        return specificationVoList;
    }

    public class VO {
        private String name;
        private List<TpmGoodsSpecification> valueList;

        @SuppressWarnings("unused")
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<TpmGoodsSpecification> getValueList() {
            return valueList;
        }

        public void setValueList(List<TpmGoodsSpecification> valueList) {
            this.valueList = valueList;
        }
    }

}
