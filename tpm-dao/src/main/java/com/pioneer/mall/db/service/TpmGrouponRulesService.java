package com.pioneer.mall.db.service;

import com.alibaba.druid.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmGoodsMapper;
import com.pioneer.mall.db.dao.TpmGrouponRulesMapper;
import com.pioneer.mall.db.dao.TpmGoodsMapper;
import com.pioneer.mall.db.dao.TpmGrouponRulesMapper;
import com.pioneer.mall.db.dao.ex.GrouponMapperEx;
import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.domain.TpmGrouponRules;
import com.pioneer.mall.db.domain.TpmGrouponRulesExample;

import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.domain.TpmGoods.Column;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TpmGrouponRulesService {
	@Resource
	private TpmGrouponRulesMapper mapper;
	@Resource
	private TpmGoodsMapper goodsMapper;
	@Resource
	private GrouponMapperEx grouponMapperEx;
	
	private TpmGoods.Column[] goodsColumns = new TpmGoods.Column[] { TpmGoods.Column.id, TpmGoods.Column.name,
			TpmGoods.Column.brief, TpmGoods.Column.picUrl, TpmGoods.Column.counterPrice, TpmGoods.Column.retailPrice };

	public int createRules(TpmGrouponRules rules) {
		rules.setAddTime(LocalDateTime.now());
		rules.setUpdateTime(LocalDateTime.now());
		return mapper.insertSelective(rules);
	}

	/**
	 * 根据ID查找对应团购项
	 *
	 * @param id
	 * @return
	 */
	public TpmGrouponRules queryById(Integer id) {
		TpmGrouponRulesExample example = new TpmGrouponRulesExample();
		example.or().andIdEqualTo(id).andDeletedEqualTo(false);
		return mapper.selectOneByExample(example);
	}

	/**
	 * 查询某个商品关联的团购规则
	 *
	 * @param goodsId
	 * @return
	 */
	public List<TpmGrouponRules> queryByGoodsId(Long goodsId) {
		TpmGrouponRulesExample example = new TpmGrouponRulesExample();
		example.or().andGoodsIdEqualTo(goodsId).andDeletedEqualTo(false);
		return mapper.selectByExample(example);
	}

	/**
	 * 获取首页团购活动列表
	 *
	 * @param offset
	 * @param limit
	 * @return
	 */
	public List<Map<String, Object>> queryList(int offset, int limit) {
		return queryList(offset, limit, "add_time", "desc");
	}

	public List<Map<String, Object>> queryList(int offset, int limit, String sort, String order) {
		TpmGrouponRulesExample example = new TpmGrouponRulesExample();
		example.or().andDeletedEqualTo(false);
		example.setOrderByClause(sort + " " + order);
		PageHelper.startPage(offset, limit);
		List<TpmGrouponRules> grouponRules = mapper.selectByExample(example);

		List<Map<String, Object>> grouponList = new ArrayList<>(grouponRules.size());
		for (TpmGrouponRules rule : grouponRules) {
			Integer goodsId = rule.getGoodsId().intValue();
			TpmGoods goods = goodsMapper.selectByPrimaryKeySelective(goodsId, goodsColumns);
			if (goods == null)
				continue;

			Map<String, Object> item = new HashMap<>();
			item.put("goods", goods);
			item.put("groupon_price", goods.getRetailPrice().subtract(rule.getDiscount()));
			item.put("groupon_member", rule.getDiscountMember());
			grouponList.add(item);
		}

		return grouponList;
	}

	/**
	 * 判断某个团购活动是否已经过期
	 *
	 * @return
	 */
	public boolean isExpired(TpmGrouponRules rules) {
		return (rules == null || rules.getExpireTime().isBefore(LocalDateTime.now()));
	}

	/**
	 * 获取团购活动列表
	 *
	 * @param goodsId
	 * @param page
	 * @param size
	 * @param sort
	 * @param order
	 * @return
	 */
	public List<TpmGrouponRules> querySelective(String goodsId, Integer page, Integer size, String sort, String order) {
		TpmGrouponRulesExample example = new TpmGrouponRulesExample();
		example.setOrderByClause(sort + " " + order);

		TpmGrouponRulesExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(goodsId)) {
			criteria.andGoodsIdEqualTo(Long.parseLong(goodsId));
		}
		criteria.andDeletedEqualTo(false);

		PageHelper.startPage(page, size);
		return mapper.selectByExample(example);
	}

	public void delete(Integer id) {
		mapper.logicalDeleteByPrimaryKey(id);
	}

	public int updateById(TpmGrouponRules grouponRules) {
		grouponRules.setUpdateTime(LocalDateTime.now());
		return mapper.updateByPrimaryKeySelective(grouponRules);
	}

	/**
	 * 查询品牌入驻管理员团购规则
	 * @param brandIds
	 * @param goodsId
	 * @param page
	 * @param limit
	 * @param sort
	 * @param order
	 * @return
	 */
	public List<TpmGrouponRules> queryBrandGrouponRules(List<Integer> brandIds, String goodsId, Integer page,
			Integer size, String sort, String order) {
		String orderBySql = null;
		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			orderBySql = "o."+sort + " " + order;
		}
		
		String brandIdsSql = null;
		if (brandIds != null) {
			brandIdsSql = "";
			for (Integer brandId : brandIds) {
				brandIdsSql += "," + brandId;
			}
			brandIdsSql = " and g.brand_id in (" + brandIdsSql.substring(1) + ") ";
		}
		
		PageHelper.startPage(page, size);
		
		return grouponMapperEx.queryBrandGrouponRules(goodsId,orderBySql,brandIdsSql);
	}
}