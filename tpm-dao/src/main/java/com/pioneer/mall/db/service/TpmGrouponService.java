package com.pioneer.mall.db.service;

import com.alibaba.druid.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmGrouponMapper;
import com.pioneer.mall.db.dao.ex.GrouponMapperEx;

import com.pioneer.mall.db.domain.TpmGroupon;
import com.pioneer.mall.db.domain.TpmGrouponExample;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmGrouponService {
	@Resource
	private TpmGrouponMapper mapper;
	@Resource
	private GrouponMapperEx grouponMapperEx;

	/**
	 * 获取用户发起的团购记录
	 *
	 * @param userId
	 * @return
	 */
	public List<TpmGroupon> queryMyGroupon(Integer userId) {
		TpmGrouponExample example = new TpmGrouponExample();
		example.or().andUserIdEqualTo(userId).andCreatorUserIdEqualTo(userId).andGrouponIdEqualTo(0)
				.andDeletedEqualTo(false).andPayedEqualTo(true);
		example.orderBy("add_time desc");
		return mapper.selectByExample(example);
	}

	/**
	 * 获取用户参与的团购记录
	 *
	 * @param userId
	 * @return
	 */
	public List<TpmGroupon> queryMyJoinGroupon(Integer userId) {
		TpmGrouponExample example = new TpmGrouponExample();
		example.or().andUserIdEqualTo(userId).andGrouponIdNotEqualTo(0).andDeletedEqualTo(false).andPayedEqualTo(true);
		example.orderBy("add_time desc");
		return mapper.selectByExample(example);
	}

	/**
	 * 根据OrderId查询团购记录
	 *
	 * @param orderId
	 * @return
	 */
	public TpmGroupon queryByOrderId(Integer orderId) {
		TpmGrouponExample example = new TpmGrouponExample();
		example.or().andOrderIdEqualTo(orderId).andDeletedEqualTo(false);
		return mapper.selectOneByExample(example);
	}

	/**
	 * 获取某个团购活动参与的记录
	 *
	 * @param id
	 * @return
	 */
	public List<TpmGroupon> queryJoinRecord(Integer id) {
		TpmGrouponExample example = new TpmGrouponExample();
		example.or().andGrouponIdEqualTo(id).andDeletedEqualTo(false).andPayedEqualTo(true);
		example.orderBy("add_time desc");
		return mapper.selectByExample(example);
	}

	/**
	 * 根据ID查询记录
	 *
	 * @param id
	 * @return
	 */
	public TpmGroupon queryById(Integer id) {
		TpmGrouponExample example = new TpmGrouponExample();
		example.or().andIdEqualTo(id).andDeletedEqualTo(false).andPayedEqualTo(true);
		return mapper.selectOneByExample(example);
	}

	/**
	 * 返回某个发起的团购参与人数
	 *
	 * @param grouponId
	 * @return
	 */
	public int countGroupon(Integer grouponId) {
		TpmGrouponExample example = new TpmGrouponExample();
		example.or().andGrouponIdEqualTo(grouponId).andDeletedEqualTo(false).andPayedEqualTo(true);
		return (int) mapper.countByExample(example);
	}

	public int updateById(TpmGroupon groupon) {
		groupon.setUpdateTime(LocalDateTime.now());
		return mapper.updateByPrimaryKeySelective(groupon);
	}

	/**
	 * 创建或参与一个团购
	 *
	 * @param groupon
	 * @return
	 */
	public int createGroupon(TpmGroupon groupon) {
		groupon.setAddTime(LocalDateTime.now());
		groupon.setUpdateTime(LocalDateTime.now());
		return mapper.insertSelective(groupon);
	}

	/**
	 * 查询所有发起的团购记录
	 *
	 * @param rulesId
	 * @param page
	 * @param size
	 * @param sort
	 * @param order
	 * @return
	 */
	public List<TpmGroupon> querySelective(String rulesId, Integer page, Integer size, String sort, String order) {
		TpmGrouponExample example = new TpmGrouponExample();
		TpmGrouponExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(rulesId)) {
			criteria.andRulesIdEqualTo(Integer.parseInt(rulesId));
		}
		criteria.andDeletedEqualTo(false);
		criteria.andPayedEqualTo(true);
		criteria.andGrouponIdEqualTo(0);

		PageHelper.startPage(page, size);
		return mapper.selectByExample(example);
	}

	/**
	 * 根据品牌入驻店铺获取对应的团购数据
	 * @param brandIds
	 * @param rulesId
	 * @param page
	 * @param limit
	 * @param sort
	 * @param order
	 * @return
	 */
	public List<TpmGroupon> queryBrandGroupons(List<Integer> brandIds, String rulesId, Integer page, Integer size,
			String sort, String order) {
		String orderBySql = null;
		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			orderBySql = "o."+sort + " " + order;
		}
		
		String brandIdsSql = null;
		if (brandIds != null) {
			brandIdsSql = "";
			for (Integer brandId : brandIds) {
				brandIdsSql += "," + brandId;
			}
			brandIdsSql = " and g.brand_id in (" + brandIdsSql.substring(1) + ") ";
		}
		
		PageHelper.startPage(page, size);
		
		return grouponMapperEx.queryBrandGroupons(rulesId,orderBySql,brandIdsSql);
	}
}
