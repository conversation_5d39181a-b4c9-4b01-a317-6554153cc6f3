package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmIssueMapper;
import com.pioneer.mall.db.domain.TpmIssue;
import com.pioneer.mall.db.domain.TpmIssueExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmIssueService {
	@Resource
	private TpmIssueMapper issueMapper;

	public List<TpmIssue> query() {
		TpmIssueExample example = new TpmIssueExample();
		example.or().andDeletedEqualTo(false);
		return issueMapper.selectByExample(example);
	}

	public void deleteById(Integer id) {
		issueMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmIssue issue) {
		issue.setAddTime(LocalDateTime.now());
		issue.setUpdateTime(LocalDateTime.now());
		issueMapper.insertSelective(issue);
	}

	public List<TpmIssue> querySelective(String question, Integer page, Integer size, String sort, String order) {
		TpmIssueExample example = new TpmIssueExample();
		TpmIssueExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(question)) {
			criteria.andQuestionLike("%" + question + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return issueMapper.selectByExample(example);
	}

	public int updateById(TpmIssue issue) {
		issue.setUpdateTime(LocalDateTime.now());
		return issueMapper.updateByPrimaryKeySelective(issue);
	}

	public TpmIssue findById(Integer id) {
		return issueMapper.selectByPrimaryKey(id);
	}
}
