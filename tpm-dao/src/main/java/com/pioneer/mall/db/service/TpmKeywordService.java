package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmKeywordMapper;
import com.pioneer.mall.db.domain.TpmKeyword;
import com.pioneer.mall.db.domain.TpmKeywordExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmKeywordService {
	@Resource
	private TpmKeywordMapper keywordsMapper;

	public TpmKeyword queryDefault() {
		TpmKeywordExample example = new TpmKeywordExample();
		example.or().andIsDefaultEqualTo(true).andDeletedEqualTo(false);
		return keywordsMapper.selectOneByExample(example);
	}

	public List<TpmKeyword> queryHots() {
		TpmKeywordExample example = new TpmKeywordExample();
		example.or().andIsHotEqualTo(true).andDeletedEqualTo(false);
		return keywordsMapper.selectByExample(example);
	}

	public List<TpmKeyword> queryByKeyword(String keyword, Integer page, Integer size) {
		TpmKeywordExample example = new TpmKeywordExample();
		example.setDistinct(true);
		example.or().andKeywordLike("%" + keyword + "%").andDeletedEqualTo(false);
		PageHelper.startPage(page, size);
		return keywordsMapper.selectByExampleSelective(example, TpmKeyword.Column.keyword);
	}

	public List<TpmKeyword> querySelective(String keyword, String url, Integer page, Integer limit, String sort,
			String order) {
		TpmKeywordExample example = new TpmKeywordExample();
		TpmKeywordExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(keyword)) {
			criteria.andKeywordLike("%" + keyword + "%");
		}
		if (!StringUtils.isEmpty(url)) {
			criteria.andUrlLike("%" + url + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, limit);
		return keywordsMapper.selectByExample(example);
	}

	public void add(TpmKeyword keywords) {
		keywords.setAddTime(LocalDateTime.now());
		keywords.setUpdateTime(LocalDateTime.now());
		keywordsMapper.insertSelective(keywords);
	}

	public TpmKeyword findById(Integer id) {
		return keywordsMapper.selectByPrimaryKey(id);
	}

	public int updateById(TpmKeyword keywords) {
		keywords.setUpdateTime(LocalDateTime.now());
		return keywordsMapper.updateByPrimaryKeySelective(keywords);
	}

	public void deleteById(Integer id) {
		keywordsMapper.logicalDeleteByPrimaryKey(id);
	}
}
