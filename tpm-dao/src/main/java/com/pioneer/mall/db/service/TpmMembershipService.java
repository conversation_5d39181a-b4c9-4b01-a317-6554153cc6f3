package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmMembershipMapper;
import com.pioneer.mall.db.dao.TpmUserMapper;
import com.pioneer.mall.db.domain.TpmMembership;
import com.pioneer.mall.db.domain.TpmMembershipExample;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.dto.TpmMembershipDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class TpmMembershipService {

    @Autowired
    private TpmMembershipMapper tpmMembershipMapper;
    @Autowired
    private TpmUserMapper tpmUserMapper;

    public List<TpmMembership> querySelective(TpmMembership tpmMembership, Integer page, Integer size, String sort, String order) {
        TpmMembershipExample tpmVipExample = new TpmMembershipExample();
        TpmMembershipExample.Criteria criteria = tpmVipExample.createCriteria();
        String name = tpmMembership.getName();
        if (Objects.nonNull(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        if (Objects.nonNull(tpmMembership.getEnable())) {
            criteria.andEnableEqualTo(tpmMembership.getEnable());
        }
        criteria.andLogicalDeleted(false);
        // 排序
        if (!org.springframework.util.StringUtils.isEmpty(sort) && !org.springframework.util.StringUtils.isEmpty(order)) {
            tpmVipExample.setOrderByClause(sort + " " + order);
        }
        PageHelper.startPage(page, size);
        return tpmMembershipMapper.selectByExample(tpmVipExample);
    }

    public List<TpmMembershipDto> selectAll() {
        TpmMembershipExample example = new TpmMembershipExample();
        example.createCriteria().andLogicalDeleted(false);
        example.orderBy(TpmMembership.Column.points.asc());
        List<TpmMembership> tpmMemberships = tpmMembershipMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(tpmMemberships)) {
            return new ArrayList();
        }
        return tpmMemberships.stream()
                .map(tpmMembership -> {
                    TpmMembershipDto dto = new TpmMembershipDto();
                    BeanUtils.copyProperties(tpmMembership, TpmMembershipDto.class);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 找到该积分下最高的vip登记
     *
     * @param points
     * @return
     */
    public TpmMembership findByPoints(BigDecimal points) {
        if (Objects.isNull(points)) {
            points = BigDecimal.ZERO;
        }
        TpmMembershipExample example = new TpmMembershipExample();
        example.createCriteria().andLogicalDeleted(false).andPointsLessThanOrEqualTo(points);
        example.orderBy(TpmMembership.Column.points.desc());
        List<TpmMembership> tpmMemberships = tpmMembershipMapper.selectByExample(example);
        if (!tpmMemberships.isEmpty()) {
            return tpmMemberships.get(0);
        }
        return null;
    }

    public void add(TpmMembershipDto tpmMembershipDto) throws Exception {
        checkCodeExist(tpmMembershipDto.getCode());
        TpmMembership tpmMembership = new TpmMembership();
        BeanUtils.copyProperties(tpmMembershipDto, tpmMembership);
        tpmMembership.setCreateTime(LocalDateTime.now());
        tpmMembership.setUpdateTime(LocalDateTime.now());
        tpmMembershipMapper.insertSelective(tpmMembership);
    }

    public void update(TpmMembershipDto tpmMembershipDto) throws Exception {
        checkCodeExist(tpmMembershipDto.getCode());
        TpmMembership tpmMembership = new TpmMembership();
        BeanUtils.copyProperties(tpmMembershipDto, tpmMembership);
        TpmMembershipExample example = new TpmMembershipExample();
        example.createCriteria().andLogicalDeleted(false);
        tpmMembershipMapper.updateByExampleSelective(tpmMembership, example);
    }

    public void checkCodeExist(String code) throws Exception {
        TpmMembershipExample tpmMembershipExample = new TpmMembershipExample();
        tpmMembershipExample.createCriteria().andCodeEqualTo(code).andDeletedEqualTo(false);
        long l = tpmMembershipMapper.countByExample(tpmMembershipExample);
        if (l > 0) {
            throw new Exception("已存在code为:" + code + "的会员配置");
        }
    }

    public int deleteById(TpmMembershipDto tpmMembershipDto) {
        if (Objects.isNull(tpmMembershipDto) && Objects.isNull(tpmMembershipDto.getId())) {
            return 0;
        }
        return tpmMembershipMapper.deleteByPrimaryKey(tpmMembershipDto.getId());
    }

    public void updateById(TpmMembershipDto tpmMembershipDto) {
        TpmMembership tpmMembership = new TpmMembership();
        BeanUtils.copyProperties(tpmMembershipDto, tpmMembership);
        tpmMembership.setUpdateTime(LocalDateTime.now());
        tpmMembershipMapper.updateByPrimaryKeySelective(tpmMembership);
    }

    public TpmMembership findById(Integer membershipId) {
        if (Objects.isNull(membershipId)) {
            return null;
        }
        return tpmMembershipMapper.selectByPrimaryKey(membershipId);
    }

    public void updateMemberInfo(TpmUser tpmUser) {
        if (Objects.isNull(tpmUser) || Objects.isNull(tpmUser.getId())) {
            return;
        }
        TpmUser codeUpdate = new TpmUser();
        codeUpdate.setId(tpmUser.getId());
        TpmMembership tpmMembership = this.findByPoints(tpmUser.getPoints());
        if (Objects.nonNull(tpmMembership)) {
            codeUpdate.setMembershipId(tpmMembership.getId());
            codeUpdate.setMembershipLevel(tpmMembership.getCode());
        }
        if (Objects.isNull(tpmUser.getMembershipCode())) {
            Integer id = tpmUser.getId();
            int cal = (id + 1) * 2;
            String leftPad = StringUtils.leftPad(String.valueOf(cal), 7, "0");
            String memberCode = "8888" + leftPad;
            codeUpdate.setMembershipCode(memberCode);
        }
        tpmUserMapper.updateByPrimaryKeySelective(codeUpdate);
    }
}
