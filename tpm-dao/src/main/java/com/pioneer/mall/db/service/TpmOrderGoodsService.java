package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmOrderGoodsMapper;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.domain.TpmOrderGoodsExample;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmOrderGoodsService {
    @Resource
    private TpmOrderGoodsMapper orderGoodsMapper;

    public int add(TpmOrderGoods orderGoods) {
        orderGoods.setAddTime(LocalDateTime.now());
        orderGoods.setUpdateTime(LocalDateTime.now());
        return orderGoodsMapper.insertSelective(orderGoods);
    }

    public List<TpmOrderGoods> queryByOid(Integer orderId) {
        TpmOrderGoodsExample example = new TpmOrderGoodsExample();
        example.or().andOrderIdEqualTo(orderId).andDeletedEqualTo(false);
        return orderGoodsMapper.selectByExample(example);
    }

    public List<TpmOrderGoods> queryByOids(List<Integer> orderIds) {
        TpmOrderGoodsExample example = new TpmOrderGoodsExample();
        example.or().andOrderIdIn(orderIds).andDeletedEqualTo(false);
        return orderGoodsMapper.selectByExample(example);
    }

    public List<TpmOrderGoods> findByOidAndGid(Integer orderId, Integer goodsId) {
        TpmOrderGoodsExample example = new TpmOrderGoodsExample();
        example.or().andOrderIdEqualTo(orderId).andGoodsIdEqualTo(goodsId).andDeletedEqualTo(false);
        return orderGoodsMapper.selectByExample(example);
    }

    public TpmOrderGoods findById(Integer id) {
        return orderGoodsMapper.selectByPrimaryKey(id);
    }

    public void updateById(TpmOrderGoods orderGoods) {
        orderGoods.setUpdateTime(LocalDateTime.now());
        orderGoodsMapper.updateByPrimaryKeySelective(orderGoods);
    }

    public Short getComments(Integer orderId) {
        TpmOrderGoodsExample example = new TpmOrderGoodsExample();
        example.or().andOrderIdEqualTo(orderId).andDeletedEqualTo(false);
        long count = orderGoodsMapper.countByExample(example);
        return (short) count;
    }

    public boolean checkExist(Integer goodsId) {
        TpmOrderGoodsExample example = new TpmOrderGoodsExample();
        example.or().andGoodsIdEqualTo(goodsId).andDeletedEqualTo(false);
        return orderGoodsMapper.countByExample(example) != 0;
    }
}
