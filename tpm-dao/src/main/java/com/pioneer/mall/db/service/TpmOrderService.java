package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.bean.CategorySellAmts;
import com.pioneer.mall.db.bean.DayStatis;
import com.pioneer.mall.db.bean.search.TpmOrderSearch;
import com.pioneer.mall.db.dao.TpmOrderMapper;
import com.pioneer.mall.db.dao.ex.OrderMapper;
import com.pioneer.mall.db.dao.ex.StatMapper;
import com.pioneer.mall.db.domain.TpmDeliveryRecord;
import com.pioneer.mall.db.domain.TpmOrder;
import com.pioneer.mall.db.domain.TpmOrderExample;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.util.OrderHandleOption;
import com.pioneer.mall.db.util.OrderUtil;
import com.pioneer.mall.db.util.ThreadContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.pioneer.mall.db.util.WxResponseCode.ORDER_CONFIRM_OPERATION;

@Slf4j
@Service
public class TpmOrderService {
    @Resource
    private TpmOrderMapper tpmOrderMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private StatMapper statMapper;

    @Resource
    private TpmPointService tpmPointService;

    @Resource
    private TpmDeliveryRecordService tpmDeliveryRecordService;

    public int add(TpmOrder order) {
        order.setAddTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        return tpmOrderMapper.insertSelective(order);
    }

    public int count(Integer userId) {
        TpmOrderExample example = new TpmOrderExample();
        example.or().andUserIdEqualTo(userId)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andDeletedEqualTo(false);
        return (int) tpmOrderMapper.countByExample(example);
    }

    public TpmOrder findById(Integer orderId) {
        return tpmOrderMapper.selectByPrimaryKey(orderId);
    }

    public List<TpmOrder> findById(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        TpmOrderExample tpmOrderExample = new TpmOrderExample();
        tpmOrderExample.createCriteria().andLogicalDeleted(false).andIdIn(orderIdList);
        return tpmOrderMapper.selectByExample(tpmOrderExample);
    }

    private String getRandomNum(Integer num) {
        String base = "0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < num; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    public int countByOrderSn(Integer userId, String orderSn) {
        TpmOrderExample example = new TpmOrderExample();
        example.or().andUserIdEqualTo(userId)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andOrderSnEqualTo(orderSn).andDeletedEqualTo(false);
        return (int) tpmOrderMapper.countByExample(example);
    }

    // TODO 这里应该产生一个唯一的订单，但是实际上这里仍然存在两个订单相同的可能性
    public String generateOrderSn(Integer userId) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        String now = df.format(LocalDate.now());
        String orderSn = now + getRandomNum(6);
        while (countByOrderSn(userId, orderSn) != 0) {
            orderSn = getRandomNum(6);
        }
        return orderSn;
    }

    public List<TpmOrder> queryByOrderStatus(Integer userId, List<Short> orderStatus, Integer page, Integer size) {
        TpmOrderExample example = new TpmOrderExample();
        example.setOrderByClause(TpmOrder.Column.addTime.desc());
        TpmOrderExample.Criteria criteria = example.or();
        criteria.andUserIdEqualTo(userId);
        if (orderStatus != null) {
            criteria.andOrderStatusIn(orderStatus);
        }
        criteria.andDeletedEqualTo(false);
        PageHelper.startPage(page, size);
        return tpmOrderMapper.selectByExample(example);
    }

    public List<TpmOrder> list(TpmOrderSearch tpmOrderSearch) {
        TpmOrderExample example = new TpmOrderExample();
        example.setOrderByClause(TpmOrder.Column.addTime.desc());
        TpmOrderExample.Criteria criteria = example.or();
        criteria.andUserIdEqualTo(tpmOrderSearch.getUserId());
        Integer todayOrHistory = tpmOrderSearch.getTodayOrHistory();
        if (Objects.nonNull(todayOrHistory)) {
            LocalDateTime localDateTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            if (1 == todayOrHistory) {
                criteria.andAddTimeGreaterThanOrEqualTo(localDateTime);
            } else if (0 == todayOrHistory) {
                criteria.andAddTimeLessThan(localDateTime);
            }
        }
        Integer orderType = tpmOrderSearch.getOrderType();
        if (Objects.equals(0, orderType)) {
            criteria.andFreightTypeEqualTo((byte) 0);
        } else if (Objects.equals(1, orderType)) {
            criteria.andFreightTypeEqualTo((byte) 1);
        } else if (Objects.equals(3, orderType)) {
            criteria.andBusinessTypeEqualTo(2);
        }
        criteria.andDeletedEqualTo(false);
        PageHelper.startPage(tpmOrderSearch.getCurrentPage(), tpmOrderSearch.getPageSize());
        return tpmOrderMapper.selectByExample(example);
    }

    public List<TpmOrder> querySelective(Integer userId, String orderSn, List<Short> orderStatusArray, Integer page,
                                         Integer size, String sort, String order, Integer businessType, Integer freightType,Integer shopId) {
        TpmOrderExample example = new TpmOrderExample();
        TpmOrderExample.Criteria criteria = example.createCriteria();
        criteria.andBusinessTypeEqualTo(businessType);
        if (freightType != null) {
            criteria.andFreightTypeEqualTo(freightType.byteValue());
        }
        if (userId != null) {
            criteria.andUserIdEqualTo(userId);
        }
        if (!StringUtils.isEmpty(orderSn)) {
            criteria.andOrderSnEqualTo(orderSn);
        }
        if (orderStatusArray != null && orderStatusArray.size() != 0) {
            criteria.andOrderStatusIn(orderStatusArray);
        }
        criteria.andDeletedEqualTo(false);

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        // 判断下shopid
        if (shopId != null) {
            criteria.andShopIdEqualTo(shopId);
        } else {
            List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
            if (!CollectionUtils.isEmpty(userShopIdList)) {
                criteria.andShopIdIn(userShopIdList);
            }
        }

        PageHelper.startPage(page, size);
        return tpmOrderMapper.selectByExample(example);
    }

    public int updateWithOptimisticLocker(TpmOrder order) {
        LocalDateTime preUpdateTime = order.getUpdateTime();
        order.setUpdateTime(LocalDateTime.now());
        return orderMapper.updateWithOptimisticLocker(preUpdateTime, order);
    }

    public void deleteById(Integer id) {
        tpmOrderMapper.logicalDeleteByPrimaryKey(id);
    }

    public int count() {
        TpmOrderExample example = new TpmOrderExample();
        example.or().andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode()).andDeletedEqualTo(false);
        return (int) tpmOrderMapper.countByExample(example);
    }

    public List<TpmOrder> queryUnpaid() {
        TpmOrderExample example = new TpmOrderExample();
        example.or()
//                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andOrderStatusEqualTo(OrderUtil.STATUS_CREATE).andDeletedEqualTo(false);
        return tpmOrderMapper.selectByExample(example);
    }

    public List<TpmOrder> queryUnconfirm() {
        TpmOrderExample example = new TpmOrderExample();
        example.or()
//                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andOrderStatusEqualTo(OrderUtil.STATUS_SHIP)
                .andShipTimeIsNotNull().andDeletedEqualTo(false);
        LocalDate today = LocalDate.now();
        LocalDate threeDaysAgo = today.minusDays(3);
        LocalDateTime startOfToday = LocalDateTime.of(threeDaysAgo, LocalTime.MIN);
        LocalDateTime endOfToday = LocalDateTime.of(today, LocalTime.MAX);
        example.or().andOrderStatusIn(Arrays.asList(OrderUtil.STATUS_PREPARE, OrderUtil.STATUS_PAY)).andPayTimeBetween(startOfToday, endOfToday);
        return tpmOrderMapper.selectByExample(example);
    }

    public List<TpmOrder> queryPickUpAndDeliveryUnconfirmed() {
        TpmOrderExample example = new TpmOrderExample();
        example.or()
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andOrderStatusEqualTo(OrderUtil.STATUS_SHIP)
                .andShipTimeIsNotNull().andDeletedEqualTo(false);
        LocalDate today = LocalDate.now();
        LocalDate threeDaysAgo = today.minusDays(3);
        LocalDateTime startOfToday = LocalDateTime.of(threeDaysAgo, LocalTime.MIN);
        LocalDateTime endOfToday = LocalDateTime.of(today, LocalTime.MAX);
        example.or()
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andOrderStatusIn(Arrays.asList(OrderUtil.STATUS_PREPARE, OrderUtil.STATUS_PAY))
                .andPayTimeBetween(startOfToday, endOfToday);
        return tpmOrderMapper.selectByExample(example);
    }

    public List<TpmOrder> queryExpressUnconfirmed() {
        TpmOrderExample example = new TpmOrderExample();
        example.or()
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.EXPRESS.getCode())
                .andOrderStatusEqualTo(OrderUtil.STATUS_SHIP)
                .andShipTimeIsNotNull().andDeletedEqualTo(false);
        LocalDate today = LocalDate.now();
        LocalDate threeDaysAgo = today.minusDays(14);
        LocalDateTime startOfToday = LocalDateTime.of(threeDaysAgo, LocalTime.MIN);
        LocalDateTime endOfToday = LocalDateTime.of(today, LocalTime.MAX);
        example.or()
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.EXPRESS.getCode())
                .andOrderStatusIn(Arrays.asList(OrderUtil.STATUS_PREPARE, OrderUtil.STATUS_PAY))
                .andPayTimeBetween(startOfToday, endOfToday);
        return tpmOrderMapper.selectByExample(example);
    }

    public TpmOrder findBySn(String orderSn) {
        TpmOrderExample example = new TpmOrderExample();
        example.or().andOrderSnEqualTo(orderSn).andDeletedEqualTo(false);
        return tpmOrderMapper.selectOneByExample(example);
    }
    public List<TpmOrder> findBySn(List<String> orderSnList) {
        TpmOrderExample example = new TpmOrderExample();
        example.or().andOrderSnIn(orderSnList).andDeletedEqualTo(false);
        return tpmOrderMapper.selectByExample(example);
    }

    public void updateDeliveryOrderId(Integer orderId, String deliveryOrderId) {
        if (StringUtils.isEmpty(deliveryOrderId)) {
            return;
        }
        TpmOrder tpmOrder = new TpmOrder();
        tpmOrder.setId(orderId);
        tpmOrder.setDeliveryOrderId(deliveryOrderId);
        tpmOrder.setUpdateTime(LocalDateTime.now());
        tpmOrderMapper.updateByPrimaryKeySelective(tpmOrder);
    }

    public void updateShipsn(Integer orderId, String shipsn) {
        if (StringUtils.isEmpty(shipsn)) {
            return;
        }
        TpmOrder tpmOrder = new TpmOrder();
        tpmOrder.setId(orderId);
        tpmOrder.setShipSn(shipsn);
        tpmOrder.setOrderStatus(OrderUtil.STATUS_SHIP);
        tpmOrder.setUpdateTime(LocalDateTime.now());
        tpmOrder.setShipTime(LocalDateTime.now());
        tpmOrderMapper.updateByPrimaryKeySelective(tpmOrder);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateDeliveryStatusAndRecord(Integer orderId, String deliveryStatus, TpmDeliveryRecord tpmDeliveryRecord) {
        if (StringUtils.isEmpty(deliveryStatus)) {
            return;
        }
        TpmOrder tpmOrder = new TpmOrder();
        tpmOrder.setId(orderId);
        tpmOrder.setDeliveryStatus(deliveryStatus);
        tpmOrder.setUpdateTime(LocalDateTime.now());
        if (Objects.equals(deliveryStatus, "4")) {
            tpmOrder.setOrderStatus(OrderUtil.STATUS_CONFIRM);
            tpmOrder.setConfirmTime(LocalDateTime.now());
        }
        //更新状态
        tpmOrderMapper.updateByPrimaryKeySelective(tpmOrder);
        if (Objects.equals(deliveryStatus, "4")) {
            TpmOrder order = this.findById(orderId);
            TpmPointDto tpmPointDto = new TpmPointDto();
            tpmPointDto.setUserId(order.getUserId());
            tpmPointDto.setOrderSn(order.getOrderSn());
            tpmPointDto.setAmount(order.getActualPrice());
            tpmPointDto.setDescription("订单:" + order.getOrderSn() + "完成收货添加积分:" + order.getActualPrice());
            tpmPointService.addPoint(tpmPointDto);
            log.info("订单 ID=" + order.getId() + "完成收货 添加积分");
        }

        tpmDeliveryRecordService.add(tpmDeliveryRecord);
    }


    public TpmOrder getByExternalDeliveryPlatformOrderId(String deliveryOrderId) {
        if (StringUtils.isEmpty(deliveryOrderId)) {
            return null;

        }
        TpmOrderExample example = new TpmOrderExample();
        example.or().andDeliveryOrderIdEqualTo(deliveryOrderId).andDeletedEqualTo(false);
        return tpmOrderMapper.selectOneByExample(example);
    }

    public Map<Object, Object> orderInfo(Integer userId) {
        TpmOrderExample example = new TpmOrderExample();
        example.or().andUserIdEqualTo(userId).andDeletedEqualTo(false);
        List<TpmOrder> orders = tpmOrderMapper.selectByExampleSelective(example, TpmOrder.Column.orderStatus,
                TpmOrder.Column.comments);

        int unpaid = 0;
        int unship = 0;
        int unrecv = 0;
        int uncomment = 0;
        for (TpmOrder order : orders) {
            if (OrderUtil.isCreateStatus(order)) {
                unpaid++;
            } else if (OrderUtil.isPayStatus(order)) {
                unship++;
            } else if (OrderUtil.isShipStatus(order)) {
                unrecv++;
            } else if (OrderUtil.isConfirmStatus(order) || OrderUtil.isAutoConfirmStatus(order)) {
                uncomment += order.getComments();
            } else {
                // do nothing
            }
        }

        Map<Object, Object> orderInfo = new HashMap<Object, Object>();
        orderInfo.put("unpaid", unpaid);
        orderInfo.put("unship", unship);
        orderInfo.put("unrecv", unrecv);
        orderInfo.put("uncomment", uncomment);
        return orderInfo;
    }

    public List<TpmOrder> queryComment() {
        TpmOrderExample example = new TpmOrderExample();
        example.or().andCommentsGreaterThan((short) 0).andDeletedEqualTo(false);
        return tpmOrderMapper.selectByExample(example);
    }

    public List<DayStatis> recentCount(int statisDaysRang) {
        return statMapper.statisIncreaseOrderCnt(statisDaysRang);
    }

    public List<CategorySellAmts> categorySell() {
        return statMapper.categorySellStatis();
    }

    /**
     * 获取指定店铺的订单
     *
     * @param brandIds
     * @param userId
     * @param orderSn
     * @param orderStatusArray
     * @param page
     * @param limit
     * @param sort
     * @param order
     * @return
     */
    public List<TpmOrder> queryBrandSelective(List<Integer> brandIds, Integer userId, String orderSn,
                                              List<Short> orderStatusArray, Integer page, Integer size, String sort, String order, Integer businessType, Integer freightType,
                                              Integer shopId) {

        String orderStatusSql = null;
        if (orderStatusArray != null) {
            orderStatusSql = "";
            for (Short orderStatus : orderStatusArray) {
                orderStatusSql += "," + orderStatus;
            }
            orderStatusSql = "and o.order_status in (" + orderStatusSql.substring(1) + ") ";
        }

        String orderBySql = null;
        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            orderBySql = "o." + sort + " " + order;
        }

        String brandIdsSql = null;
        if (brandIds != null) {
            brandIdsSql = "";
            for (Integer brandId : brandIds) {
                brandIdsSql += "," + brandId;
            }
            brandIdsSql = " and g.brand_id in (" + brandIdsSql.substring(1) + ") ";
        }

        PageHelper.startPage(page, size);
        List<Integer> userShopIdList = ThreadContextUtil.getUserShopIdList();
        if (shopId != null) {
            userShopIdList = new ArrayList(1) {{
                add(shopId);
            }};
        }
        return orderMapper.selectBrandOrdersByExample(userId, orderSn, orderStatusSql, orderBySql, brandIdsSql, businessType, freightType, userShopIdList);
    }

    public void updateStatus(Integer orderId, Short status) {
        TpmOrder order = new TpmOrder();
        order.setId(orderId);
        order.setOrderStatus(status);
        tpmOrderMapper.updateByPrimaryKeySelective(order);
    }

    public void delivery(Integer orderId) {
        TpmOrder order = findById(orderId);
        if (Objects.isNull(order)) {
            throw new RuntimeException("订单不存在");
        }
        if (!Objects.equals(order.getFreightType(), 1)) {
            throw new RuntimeException("自提订单无法配送");
        }
        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isShip()) {
            log.error("订单发起派送失败: 当前状态不支持");
            throw new RuntimeException("当前状态不支持");
        }
        this.updateStatus(orderId, OrderUtil.STATUS_SHIP);
    }

    @Transactional(rollbackFor = Exception.class)
    public void finish(Integer orderId) {
        TpmOrder order = findById(orderId);
        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isConfirm()) {
            log.error("订单确认收货失败：{}", ORDER_CONFIRM_OPERATION.desc());
            throw new RuntimeException(ORDER_CONFIRM_OPERATION.desc());
        }
        if (Objects.equals(order.getFreightType(), 0)
                && Objects.equals(order.getOrderStatus(), OrderUtil.STATUS_SHIP)) {
            throw new RuntimeException("外卖订单未派送，无法确认收货");
        }
        order.setConfirmTime(LocalDateTime.now());
        order.setOrderStatus(OrderUtil.STATUS_CONFIRM);
        if (this.updateWithOptimisticLocker(order) == 0) {
            log.error("订单确认收货失败：{}", "更新订单信息失败");
            throw new RuntimeException("更新订单信息失败");
        }
        TpmPointDto tpmPointDto = new TpmPointDto();
        tpmPointDto.setUserId(order.getUserId());
        tpmPointDto.setOrderSn(order.getOrderSn());
        tpmPointDto.setAmount(order.getActualPrice());
        tpmPointDto.setDescription("订单:" + order.getOrderSn() + "确认收货添加积分:" + order.getActualPrice());
        tpmPointService.addPoint(tpmPointDto);

        log.info("【请求结束】订单确认收货成功！");
    }

    public void prepare(Integer orderId) {
        TpmOrder order = this.findById(orderId);
        this.prepare(order);
    }

    public void prepare(TpmOrder order) {
        if (Objects.isNull(order)) {
            throw new RuntimeException("订单不存在");
        }
        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isPrepare()) {
            log.error("订单发起备货失败：{}", "当前状态不支持");
            throw new RuntimeException("当前状态不支持");
        }
        this.updateStatus(order.getId(), OrderUtil.STATUS_PREPARE);
    }

    public int countByTypeToday(Byte freightType) {
        int count = orderMapper.countByTypeToday(freightType);
        return count;
    }

    public synchronized TpmOrder verifyQRCode(String qrCode) {
        log.info("verifyQRCode qrCode={}", qrCode);
        synchronized (this) {
            TpmOrderExample tpmOrderExample = new TpmOrderExample();
            TpmOrderExample.Criteria criteria = tpmOrderExample.createCriteria().andMealQrCodeEqualTo(qrCode);
            criteria.andLogicalDeleted(false);
            List<TpmOrder> tpmOrders = tpmOrderMapper.selectByExample(tpmOrderExample);
            if (CollectionUtils.isEmpty(tpmOrders)) {
                throw new RuntimeException("核销码不存在");
            }
            if (tpmOrders.size() > 1) {
                throw new RuntimeException("存在重复核销码");
            }
            TpmOrder tpmOrder = tpmOrders.get(0);
            if (Objects.isNull(tpmOrder)) {
                throw new RuntimeException("核销码对应订单不存在");
            }
            if (tpmOrder.getMealPickupStatus()) {
                throw new RuntimeException("取餐码:" + tpmOrder.getMealCode() + "该订单已核销");
            }
            tpmOrder.setMealPickupTime(LocalDateTime.now());
            tpmOrder.setMealPickupStatus(true);
            tpmOrder.setOrderStatus(OrderUtil.STATUS_CONFIRM);
            tpmOrder.setConfirmTime(LocalDateTime.now());
            tpmOrderMapper.updateByPrimaryKeySelective(tpmOrder);
            log.info("verifyQRCode qrCode={} 订单号={} 已核销", qrCode, tpmOrder.getOrderSn());
            TpmPointDto tpmPointDto = new TpmPointDto();
            tpmPointDto.setUserId(tpmOrder.getUserId());
            tpmPointDto.setOrderSn(tpmOrder.getOrderSn());
            tpmPointDto.setAmount(tpmOrder.getActualPrice());
            tpmPointDto.setDescription("订单:" + tpmOrder.getOrderSn() + "取餐完成添加积分:" + tpmOrder.getActualPrice());
            tpmPointService.addPoint(tpmPointDto);
            log.info("订单 ID=" + tpmOrder.getId() + " 取餐完成添加积分");
            return tpmOrder;
        }
    }

    public List<TpmOrder> queryUserExistUnTakeMeal(Integer userId) {
        TpmOrderExample tpmOrderExample = new TpmOrderExample();
        tpmOrderExample.createCriteria().andUserIdEqualTo(userId)
                .andAddTimeGreaterThanOrEqualTo(LocalDateTime.of(LocalDate.now(), LocalTime.MIDNIGHT))
                .andFreightTypeIn(Arrays.asList((byte) 1, (byte) 2))
                .andDeletedEqualTo(false)
                .andBusinessTypeEqualTo(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())
                .andMealQrCodeIsNotNull()
                .andMealCodeIsNotNull()
                .andMealPickupStatusEqualTo(false)
                .andMealPickupTimeIsNull();
        tpmOrderExample.orderBy("add_time desc");
        return tpmOrderMapper.selectByExample(tpmOrderExample);

    }

    public TpmOrder findByWayBillNo(String wayBillNo) {
        if (Objects.isNull(wayBillNo)) {
            return null;
        }
        TpmOrderExample tpmOrderExample = new TpmOrderExample();
        tpmOrderExample.createCriteria().andShipSnEqualTo(wayBillNo);
        List<TpmOrder> tpmOrders = tpmOrderMapper.selectByExample(tpmOrderExample);
        if (CollectionUtils.isEmpty(tpmOrders)) {
            return null;
        }
        return tpmOrders.get(0);
    }

    public List<TpmOrder> findByPayId(String payId) {
        if (Objects.isNull(payId)) {
            return null;
        }
        TpmOrderExample tpmOrderExample = new TpmOrderExample();
        tpmOrderExample.createCriteria().andPayIdEqualTo(payId).andDeletedEqualTo(false);
        return tpmOrderMapper.selectByExample(tpmOrderExample);
    }
}
