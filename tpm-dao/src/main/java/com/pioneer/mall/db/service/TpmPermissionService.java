package com.pioneer.mall.db.service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import com.pioneer.mall.db.dao.TpmPermissionMapper;
import com.pioneer.mall.db.domain.TpmPermission;
import com.pioneer.mall.db.domain.TpmPermissionExample;
import org.springframework.stereotype.Service;

@Service
public class TpmPermissionService {
	@Resource
	private TpmPermissionMapper permissionMapper;

	public Set<String> queryByRoleIds(Integer[] roleIds) {
		Set<String> permissions = new HashSet<String>();
		if (roleIds.length == 0) {
			return permissions;
		}

		TpmPermissionExample example = new TpmPermissionExample();
		example.or().andRoleIdIn(Arrays.asList(roleIds)).andDeletedEqualTo(false);
		List<TpmPermission> permissionList = permissionMapper.selectByExample(example);

		for (TpmPermission permission : permissionList) {
			permissions.add(permission.getPermission());
		}

		return permissions;
	}

	public Set<String> queryByRoleId(Integer roleId) {
		Set<String> permissions = new HashSet<String>();
		if (roleId == null) {
			return permissions;
		}

		TpmPermissionExample example = new TpmPermissionExample();
		example.or().andRoleIdEqualTo(roleId).andDeletedEqualTo(false);
		List<TpmPermission> permissionList = permissionMapper.selectByExample(example);

		for (TpmPermission permission : permissionList) {
			permissions.add(permission.getPermission());
		}

		return permissions;
	}

	public boolean checkSuperPermission(Integer roleId) {
		if (roleId == null) {
			return false;
		}

		TpmPermissionExample example = new TpmPermissionExample();
		example.or().andRoleIdEqualTo(roleId).andPermissionEqualTo("*").andDeletedEqualTo(false);
		return permissionMapper.countByExample(example) != 0;
	}

	public void deleteByRoleId(Integer roleId) {
		TpmPermissionExample example = new TpmPermissionExample();
		example.or().andRoleIdEqualTo(roleId).andDeletedEqualTo(false);
		permissionMapper.logicalDeleteByExample(example);
	}

	public void add(TpmPermission TpmPermission) {
		TpmPermission.setAddTime(LocalDateTime.now());
		TpmPermission.setUpdateTime(LocalDateTime.now());
		permissionMapper.insertSelective(TpmPermission);
	}
}
