package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmPointExchangeConfigMapper;
import com.pioneer.mall.db.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TpmPointExchangeConfigService {
    @Resource
    private TpmPointExchangeConfigMapper tpmPointExchangeConfigMapper;

    @Transactional(rollbackFor = Exception.class)
    public void add(TpmPointExchangeConfig config) {
        tpmPointExchangeConfigMapper.insertSelective(config);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateByPrimaryKeySelective(TpmPointExchangeConfig config) {
        tpmPointExchangeConfigMapper.updateByPrimaryKeySelective(config);
    }


    public List<TpmPointExchangeConfig> querySelective(String id, String name, Integer page, Integer size, String sort, String order) {
        TpmPointExchangeConfigExample example = new TpmPointExchangeConfigExample();
        TpmPointExchangeConfigExample.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(id)) {
            criteria.andIdEqualTo(Integer.valueOf(id));
        }
        if (!StringUtils.isEmpty(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        criteria.andDeletedEqualTo(false);
        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }
        PageHelper.startPage(page, size);
        return tpmPointExchangeConfigMapper.selectByExample(example);
    }

    public List<TpmPointExchangeConfig> getAllPointExchangeConfigList() {

        TpmPointExchangeConfigExample example = new TpmPointExchangeConfigExample();
        TpmPointExchangeConfigExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedEqualTo(false);
        example.setOrderByClause("sort_order asc");
        return tpmPointExchangeConfigMapper.selectByExample(example);
    }

    public TpmPointExchangeConfig findById(Integer id) {
        return tpmPointExchangeConfigMapper.selectByPrimaryKey(id);
    }

    public void deleteById(Integer id) {
        tpmPointExchangeConfigMapper.deleteByPrimaryKey(id);
    }


}
