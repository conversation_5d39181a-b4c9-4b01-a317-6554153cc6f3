package com.pioneer.mall.db.service;


import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmPointMapper;
import com.pioneer.mall.db.domain.TpmMembership;
import com.pioneer.mall.db.domain.TpmPoint;
import com.pioneer.mall.db.domain.TpmPointExample;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.dto.request.PointPagingReq;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 积分管理业务实现类
 * <p>
 * Created by FSQ
 * CopyRight https://www.fuint.cn
 */
@Service
public class TpmPointService {

    @Autowired
    private TpmPointMapper pointMapper;

    /**
     * 会员服务接口
     */
    @Autowired
    private TpmUserService userService;

    @Autowired
    private TpmMembershipService membershipService;


    public List<TpmPointDto> paging(PointPagingReq req, String sort, String order) {

        TpmPointExample tpmPointExample = new TpmPointExample();
        TpmPointExample.Criteria criteria = tpmPointExample.createCriteria();
        criteria.andDeletedEqualTo(false);
        if (Objects.nonNull(req.getUserId())) {
            criteria.andUserIdIn(req.getUserId());
        }
        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            tpmPointExample.setOrderByClause(sort + " " + order);
        }
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<TpmPoint> pointList = pointMapper.selectByExample(tpmPointExample);

        List<TpmPointDto> dataList = new ArrayList<>();
        for (TpmPoint point : pointList) {
            TpmUser userInfo = userService.findById(point.getUserId());
            TpmPointDto item = buildTpmPointVO(point, userInfo);
            dataList.add(item);
        }
        return dataList;
    }

    /**
     * 分页查询积分列表
     *
     * @param req
     * @return
     */
    public List<TpmPointDto> paging(PointPagingReq req) {

        TpmPointExample tpmPointExample = new TpmPointExample();
        TpmPointExample.Criteria criteria = tpmPointExample.createCriteria();
        criteria.andDeletedEqualTo(false);
        if (Objects.nonNull(req.getUserId())) {
            criteria.andUserIdIn(req.getUserId());
        }

        tpmPointExample.orderBy("id desc");
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<TpmPoint> pointList = pointMapper.selectByExample(tpmPointExample);

        List<TpmPointDto> dataList = new ArrayList<>();
        for (TpmPoint point : pointList) {
            TpmUser userInfo = userService.findById(point.getUserId());
            TpmPointDto item = buildTpmPointVO(point, userInfo);
            dataList.add(item);
        }

        return dataList;
    }

    private TpmPointDto buildTpmPointVO(TpmPoint point, TpmUser userInfo) {
        TpmPointDto item = new TpmPointDto();
        item.setId(point.getId());
        item.setAmount(point.getAmount());
        item.setDescription(point.getDescription());
        item.setCreateTime(point.getCreateTime());
        item.setUpdateTime(point.getUpdateTime());
        item.setUserId(point.getUserId());
        item.setUserInfo(userInfo);
        item.setOrderSn(point.getOrderSn());
        item.setOperator(point.getOperator());
        return item;
    }

    /**
     * 添加积分记录
     *
     * @param pointDto 积分参数
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void addPoint(TpmPointDto pointDto) {
        if (Objects.isNull(pointDto) || Objects.isNull(pointDto.getUserId())) {
            return;
        }
        if (pointDto.getUserId() < 0) {
            return;
        }
        TpmPoint tpmPoint = new TpmPoint();
        tpmPoint.andLogicalDeleted(false);
        tpmPoint.setCreateTime(LocalDateTime.now());
        tpmPoint.setUpdateTime(LocalDateTime.now());
        tpmPoint.setAmount(pointDto.getAmount());
        tpmPoint.setDescription(pointDto.getDescription());
        tpmPoint.setUserId(pointDto.getUserId());
        if (pointDto.getOperator() != null) {
            tpmPoint.setOperator(pointDto.getOperator());
        }
        if (pointDto.getOrderSn() != null) {
            tpmPoint.setOrderSn(pointDto.getOrderSn());
        }
        TpmUser user = userService.findById(pointDto.getUserId());
        BigDecimal newAmount = Optional.ofNullable(user.getPoints()).orElse(BigDecimal.ZERO).add(pointDto.getAmount());
        if (tpmPoint.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            user.setAccumulativePoints(user.getAccumulativePoints().add(pointDto.getAmount()));
        }
        user.setPoints(newAmount);
        TpmMembership tpmMembership = membershipService.findByPoints(user.getAccumulativePoints());
        user.setMembershipLevel(tpmMembership.getCode());
        userService.updateById(user);
        pointMapper.insert(tpmPoint);
    }

    public List<TpmPointDto> listByUserId(Integer userId) {
        TpmPointExample example = new TpmPointExample();
        example.createCriteria().andLogicalDeleted(false).andUserIdEqualTo(userId);
        example.orderBy(TpmPoint.Column.id.desc());
        List<TpmPoint> tpmPoints = pointMapper.selectByExample(example);
        return tpmPoints.stream().map(i -> {
            TpmPointDto tpmPointDto = new TpmPointDto();
            BeanUtils.copyProperties(i, tpmPointDto);
            return tpmPointDto;
        }).collect(Collectors.toList());
    }

}
