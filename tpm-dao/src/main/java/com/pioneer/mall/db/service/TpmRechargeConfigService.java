package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.bean.search.TpmRechargeConfigSearch;
import com.pioneer.mall.db.dao.TpmRechargeConfigMapper;
import com.pioneer.mall.db.domain.TpmRechargeConfig;
import com.pioneer.mall.db.domain.TpmRechargeConfigExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/7 14:39
 */
@Service
public class TpmRechargeConfigService {
    @Autowired
    private TpmRechargeConfigMapper tpmRechargeConfigMapper;

    public List<TpmRechargeConfig> querySelective(TpmRechargeConfigSearch tpmRechargeConfigSearch) {
        TpmRechargeConfigExample example = new TpmRechargeConfigExample();
        TpmRechargeConfigExample.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(tpmRechargeConfigSearch.getTitle())) {
            criteria.andTitleLike("%" + tpmRechargeConfigSearch.getTitle() + "%");
        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getId())) {
            criteria.andIdEqualTo(tpmRechargeConfigSearch.getId());
        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getAmount())) {
            criteria.andAmountEqualTo(tpmRechargeConfigSearch.getAmount());
        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getGiveAmount())) {
            criteria.andGiveAmountEqualTo(tpmRechargeConfigSearch.getGiveAmount());
        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getGivePoint())) {
            criteria.andGivePointEqualTo(tpmRechargeConfigSearch.getGivePoint());
        }
//        if (Objects.nonNull(tpmRechargeConfigSearch.getSort())) {
//            criteria.andSortEqualTo(tpmRechargeConfigSearch.getSort());
//        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getStatus())) {
            criteria.andStatusEqualTo(tpmRechargeConfigSearch.getStatus());
        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getAddTime())) {
            criteria.andAddTimeEqualTo(tpmRechargeConfigSearch.getAddTime());
        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getUpdateTime())) {
            criteria.andUpdateTimeEqualTo(tpmRechargeConfigSearch.getUpdateTime());
        }
        if (Objects.nonNull(tpmRechargeConfigSearch.getDeleted())) {
            criteria.andDeletedEqualTo(tpmRechargeConfigSearch.getDeleted());
        }
        if(Objects.nonNull(tpmRechargeConfigSearch.getTitle())){
            criteria.andTitleEqualTo(tpmRechargeConfigSearch.getTitle());
        }
        criteria.andLogicalDeleted(false);
        // 排序
        String sort = tpmRechargeConfigSearch.getSort();
        String order = tpmRechargeConfigSearch.getOrder();
        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }
        PageHelper.startPage(tpmRechargeConfigSearch.getCurrentPage(), tpmRechargeConfigSearch.getPageSize());
        return tpmRechargeConfigMapper.selectByExample(example);
    }

    /**
     * 添加配置
     *
     * @param tpmRechargeConfig
     */
    @Transactional(rollbackFor = Exception.class)
    public void addConfig(TpmRechargeConfig tpmRechargeConfig) {
        if (Objects.isNull(tpmRechargeConfig.getSort())) {
            throw new RuntimeException("排序不能为空");
        }
        BigDecimal amount = tpmRechargeConfig.getAmount();
        TpmRechargeConfigExample tpmRechargeConfigExample = new TpmRechargeConfigExample();
        tpmRechargeConfigExample.createCriteria().andAmountEqualTo(amount).andLogicalDeleted(false);
        if (tpmRechargeConfigMapper.countByExample(tpmRechargeConfigExample) > 0) {
            throw new RuntimeException("该充值金额已存在");
        }
        tpmRechargeConfig.setStatus(1);
        tpmRechargeConfig.setAddTime(LocalDateTime.now());
        tpmRechargeConfig.setUpdateTime(LocalDateTime.now());
        tpmRechargeConfig.setDeleted(false);
        tpmRechargeConfigMapper.insertSelective(tpmRechargeConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateConfig(TpmRechargeConfig tpmRechargeConfig) {
        if (Objects.isNull(tpmRechargeConfig.getId())) {
            throw new RuntimeException("id不能为空");
        }
        tpmRechargeConfigMapper.updateByPrimaryKeySelective(tpmRechargeConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteConfig(TpmRechargeConfig tpmRechargeConfig) {
        if (Objects.isNull(tpmRechargeConfig.getId())) {
            throw new RuntimeException("id不能为空");
        }
        tpmRechargeConfigMapper.logicalDeleteByPrimaryKey(tpmRechargeConfig.getId());
    }

    public TpmRechargeConfig findById(Integer configId) {
        if (Objects.isNull(configId)) {
            throw new RuntimeException("id不能为空");
        }
        return tpmRechargeConfigMapper.selectByPrimaryKey(configId);
    }
}
