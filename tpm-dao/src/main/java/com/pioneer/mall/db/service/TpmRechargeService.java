package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.bean.request.TpmRechargeRequest;
import com.pioneer.mall.db.bean.search.TpmRechargeSearch;
import com.pioneer.mall.db.dao.TpmRechargeMapper;
import com.pioneer.mall.db.domain.TpmRecharge;
import com.pioneer.mall.db.domain.TpmRechargeConfig;
import com.pioneer.mall.db.domain.TpmRechargeExample;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.util.OrderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/6 22:06
 */
@Service
public class TpmRechargeService {
    @Resource
    private TpmRechargeMapper tpmRechargeMapper;
    @Autowired
    private TpmRechargeConfigService tpmRechargeConfigService;

    public List<TpmRecharge> querySelective(TpmRechargeSearch tpmRechargeSearch) {
        TpmRechargeExample example = new TpmRechargeExample();
        TpmRechargeExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(tpmRechargeSearch.getId())) {
            criteria.andIdEqualTo(tpmRechargeSearch.getId());
        }
        if (Objects.nonNull(tpmRechargeSearch.getUserId())) {
            criteria.andUserIdEqualTo(tpmRechargeSearch.getUserId());
        }
        if (Objects.nonNull(tpmRechargeSearch.getNickname())) {
            criteria.andNicknameEqualTo(tpmRechargeSearch.getNickname());
        }
        if (Objects.nonNull(tpmRechargeSearch.getOrderNo())) {
            criteria.andOrderNoEqualTo(tpmRechargeSearch.getOrderNo());
        }
        if (Objects.nonNull(tpmRechargeSearch.getRechargeAmount())) {
            criteria.andRechargeAmountEqualTo(tpmRechargeSearch.getRechargeAmount());
        }
        if (Objects.nonNull(tpmRechargeSearch.getGiveAmount())) {
            criteria.andGiveAmountEqualTo(tpmRechargeSearch.getGiveAmount());
        }
        if (Objects.nonNull(tpmRechargeSearch.getRechargeType())) {
            criteria.andRechargeTypeEqualTo(tpmRechargeSearch.getRechargeType());
        }
        if (Objects.nonNull(tpmRechargeSearch.getStatus())) {
            criteria.andStatusEqualTo(tpmRechargeSearch.getStatus());
        }
        if (Objects.nonNull(tpmRechargeSearch.getPayTime())) {
            criteria.andPayTimeEqualTo(tpmRechargeSearch.getPayTime());
        }
        if (Objects.nonNull(tpmRechargeSearch.getPayTimeBegin()) && Objects.nonNull(tpmRechargeSearch.getPayTimeEnd())) {
            criteria.andPayTimeBetween(tpmRechargeSearch.getPayTimeBegin(), tpmRechargeSearch.getPayTimeEnd());
        }

        if (Objects.nonNull(tpmRechargeSearch.getAddTime())) {
            criteria.andAddTimeEqualTo(tpmRechargeSearch.getAddTime());
        }
        if (Objects.nonNull(tpmRechargeSearch.getAddTimeBegin()) && Objects.nonNull(tpmRechargeSearch.getAddTimeEnd())) {
            criteria.andUpdateTimeBetween(tpmRechargeSearch.getAddTimeBegin(), tpmRechargeSearch.getAddTimeEnd());
        }
        if (Objects.nonNull(tpmRechargeSearch.getUpdateTime())) {
            criteria.andUpdateTimeEqualTo(tpmRechargeSearch.getUpdateTime());
        }
        criteria.andLogicalDeleted(false);
        example.orderBy(TpmRecharge.Column.addTime.desc());
        PageHelper.startPage(tpmRechargeSearch.getCurrentPage(), tpmRechargeSearch.getPageSize());
        return tpmRechargeMapper.selectByExample(example);
    }

    public void createNewRecharge(TpmUser user, TpmRechargeRequest tpmRechargeRequest, String tradeNo) {
        TpmRechargeConfig rechargeConfig = tpmRechargeConfigService.findById(tpmRechargeRequest.getConfigId());
        if (rechargeConfig == null) {
            throw new RuntimeException("充值配置不存在");
        }
        TpmRecharge tpmRecharge = new TpmRecharge();
        tpmRecharge.setUserId(user.getId());
        tpmRecharge.setNickname(user.getNickname());
        tpmRecharge.setOrderNo(tradeNo);
        tpmRecharge.setRechargeAmount(rechargeConfig.getAmount());
        tpmRecharge.setGiveAmount(rechargeConfig.getGiveAmount());
        tpmRecharge.setGivePoint(rechargeConfig.getGivePoint());
        tpmRecharge.setRechargeType("WX");
        tpmRecharge.setStatus(OrderUtil.STATUS_CREATE.intValue());
        tpmRecharge.setAddTime(LocalDateTime.now());
        tpmRecharge.setUpdateTime(LocalDateTime.now());
        tpmRecharge.setDeleted(false);
        tpmRechargeMapper.insertSelective(tpmRecharge);
    }

    public TpmRecharge findBySn(String orderSn) {
        TpmRechargeExample example = new TpmRechargeExample().createCriteria().andOrderNoEqualTo(orderSn).andLogicalDeleted(false).example();
        return tpmRechargeMapper.selectOneByExampleSelective(example);
    }
}
