package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmRegionMapper;
import com.pioneer.mall.db.domain.TpmRegion;
import com.pioneer.mall.db.domain.TpmRegionExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TpmRegionService {

	@Resource
	private TpmRegionMapper regionMapper;

	public List<TpmRegion> getAll() {
		TpmRegionExample example = new TpmRegionExample();
		byte b = 4;
		example.or().andTypeNotEqualTo(b);
		return regionMapper.selectByExample(example);
	}

	public List<TpmRegion> queryByPid(Integer parentId) {
		TpmRegionExample example = new TpmRegionExample();
		example.or().andPidEqualTo(parentId);
		return regionMapper.selectByExample(example);
	}

	public TpmRegion findById(Integer id) {
		return regionMapper.selectByPrimaryKey(id);
	}

	public List<TpmRegion> querySelective(String name, Integer code, Integer page, Integer size, String sort,
			String order) {
		TpmRegionExample example = new TpmRegionExample();
		TpmRegionExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(name)) {
			criteria.andNameLike("%" + name + "%");
		}
		if (!StringUtils.isEmpty(code)) {
			criteria.andCodeEqualTo(code);
		}

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return regionMapper.selectByExample(example);
	}
}
