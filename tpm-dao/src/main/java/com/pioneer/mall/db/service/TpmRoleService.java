package com.pioneer.mall.db.service;

import com.alibaba.druid.util.StringUtils;
import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmRoleMapper;
import com.pioneer.mall.db.domain.TpmRole;
import com.pioneer.mall.db.domain.TpmRoleExample;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class TpmRoleService {
	@Resource
	private TpmRoleMapper roleMapper;

	public Set<String> queryByIds(Integer[] roleIds) {
		Set<String> roles = new HashSet<String>();
		if (roleIds.length == 0) {
			return roles;
		}

		TpmRoleExample example = new TpmRoleExample();
		example.or().andIdIn(Arrays.asList(roleIds)).andEnabledEqualTo(true).andDeletedEqualTo(false);
		List<TpmRole> roleList = roleMapper.selectByExample(example);

		for (TpmRole role : roleList) {
			roles.add(role.getName());
		}

		return roles;

	}

	public List<TpmRole> querySelective(String roleName, Integer page, Integer size, String sort, String order) {
		TpmRoleExample example = new TpmRoleExample();
		TpmRoleExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(roleName)) {
			criteria.andNameEqualTo("%" + roleName + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return roleMapper.selectByExample(example);
	}

	public TpmRole findById(Integer id) {
		return roleMapper.selectByPrimaryKey(id);
	}

	public void add(TpmRole role) {
		role.setAddTime(LocalDateTime.now());
		role.setUpdateTime(LocalDateTime.now());
		roleMapper.insertSelective(role);
	}

	public void deleteById(Integer id) {
		roleMapper.logicalDeleteByPrimaryKey(id);
	}

	public void updateById(TpmRole role) {
		role.setUpdateTime(LocalDateTime.now());
		roleMapper.updateByPrimaryKeySelective(role);
	}

	public boolean checkExist(String name) {
		TpmRoleExample example = new TpmRoleExample();
		example.or().andNameEqualTo(name).andDeletedEqualTo(false);
		return roleMapper.countByExample(example) != 0;
	}

	public List<TpmRole> queryAll() {
		TpmRoleExample example = new TpmRoleExample();
		example.or().andDeletedEqualTo(false);
		return roleMapper.selectByExample(example);
	}
}
