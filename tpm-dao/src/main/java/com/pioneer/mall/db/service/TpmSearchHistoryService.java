package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmSearchHistoryMapper;
import com.pioneer.mall.db.domain.TpmSearchHistory;
import com.pioneer.mall.db.domain.TpmSearchHistoryExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmSearchHistoryService {
	@Resource
	private TpmSearchHistoryMapper searchHistoryMapper;

	public void save(TpmSearchHistory searchHistory) {
		searchHistory.setAddTime(LocalDateTime.now());
		searchHistory.setUpdateTime(LocalDateTime.now());
		searchHistoryMapper.insertSelective(searchHistory);
	}

	public List<TpmSearchHistory> queryByUid(int uid) {
		TpmSearchHistoryExample example = new TpmSearchHistoryExample();
		example.or().andUserIdEqualTo(uid).andDeletedEqualTo(false);
		example.setDistinct(true);
		return searchHistoryMapper.selectByExampleSelective(example, TpmSearchHistory.Column.keyword);
	}

	public void deleteByUid(int uid) {
		TpmSearchHistoryExample example = new TpmSearchHistoryExample();
		example.or().andUserIdEqualTo(uid);
		searchHistoryMapper.logicalDeleteByExample(example);
	}

	public List<TpmSearchHistory> querySelective(String userId, String keyword, Integer page, Integer size, String sort,
			String order) {
		TpmSearchHistoryExample example = new TpmSearchHistoryExample();
		TpmSearchHistoryExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(userId)) {
			criteria.andUserIdEqualTo(Integer.valueOf(userId));
		}
		if (!StringUtils.isEmpty(keyword)) {
			criteria.andKeywordLike("%" + keyword + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, size);
		return searchHistoryMapper.selectByExample(example);
	}
}
