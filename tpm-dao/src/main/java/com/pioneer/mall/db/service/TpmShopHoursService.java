package com.pioneer.mall.db.service;

import com.pioneer.mall.db.bean.request.TpmShopHourSettingReqVO;
import com.pioneer.mall.db.dao.TpmShopHoursMapper;
import com.pioneer.mall.db.domain.TpmShopHours;
import com.pioneer.mall.db.domain.TpmShopHoursExample;
import com.pioneer.mall.db.dto.TpmShopOpenStatusDTO;
import com.pioneer.mall.db.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Time;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/4 22:43
 */
@Slf4j
@Service
public class TpmShopHoursService {
    @Resource
    private TpmShopHoursMapper tpmShopHoursMapper;


    // 保存店铺营业时间配置
    public Object saveShopHours(List<TpmShopHourSettingReqVO> reqVOList) {
        for (TpmShopHourSettingReqVO tpmShopHourSettingReqVO : reqVOList) {

            // 校验店铺 ID 是否合法
            if (tpmShopHourSettingReqVO.getShopId() <= 0) {
                return ResponseUtil.fail(-1,"店铺 ID 不合法");
            }
            Integer dayOfWeek = tpmShopHourSettingReqVO.getDayOfWeek();
            // 校验星期几的值是否在合法范围内
            if (dayOfWeek < 1 || dayOfWeek > 7) {
                return ResponseUtil.fail(-1,"星期几的值不合法");
            }
            Boolean isOpen = tpmShopHourSettingReqVO.getIsOpen();
            String openingTimeDesc = tpmShopHourSettingReqVO.getOpeningTimeDesc();
            String closingTimeDesc = tpmShopHourSettingReqVO.getClosingTimeDesc();
            // 校验营业状态
            if (isOpen && (StringUtils.isEmpty(openingTimeDesc) || StringUtils.isEmpty(closingTimeDesc))) {
                return ResponseUtil.fail(-1,"营业状态为营业时，开门时间和关门时间不能为空");
            }
            if (isOpen) {
                Time openingTime, closingTime;
                try {
                    openingTime = convertStringToTime(openingTimeDesc);
                    tpmShopHourSettingReqVO.setOpeningTime(openingTime);
                    closingTime = convertStringToTime(closingTimeDesc);
                    tpmShopHourSettingReqVO.setClosingTime(closingTime);
                } catch (Exception e) {
                    return ResponseUtil.fail(-1,"营业时间格式错误");
                }
                if (openingTime != null && openingTime.after(closingTime)) {
                    return ResponseUtil.fail(-1,"营业状态为营业时，开门时间不能大于等于关门时间");
                }
            }
        }
        for (TpmShopHourSettingReqVO tpmShopHourSettingReqVO : reqVOList) {
            Integer shopId = tpmShopHourSettingReqVO.getShopId();
            Integer dayOfWeek = tpmShopHourSettingReqVO.getDayOfWeek();
            Boolean isOpen = tpmShopHourSettingReqVO.getIsOpen();
            Time openingTime = tpmShopHourSettingReqVO.getOpeningTime();
            Time closingTime = tpmShopHourSettingReqVO.getClosingTime();
            String statusDesc = tpmShopHourSettingReqVO.getStatusDes();
            TpmShopHoursExample tpmShopHoursExample = new TpmShopHoursExample();
            tpmShopHoursExample.createCriteria()
                    .andShopIdEqualTo(shopId)
                    .andDayOfWeekEqualTo(dayOfWeek)
                    .andDeletedEqualTo(false);
            TpmShopHours tpmShopHours = tpmShopHoursMapper.selectOneByExample(tpmShopHoursExample);
            if (Objects.isNull(tpmShopHours)) {
                tpmShopHours = new TpmShopHours();
                tpmShopHours.setShopId(shopId);
                tpmShopHours.setDayOfWeek(dayOfWeek);
                tpmShopHours.setIsOpen(isOpen);
                tpmShopHours.setOpeningTime(openingTime.toLocalTime());
                tpmShopHours.setClosingTime(closingTime.toLocalTime());
                tpmShopHours.setStatusDesc(statusDesc);
                tpmShopHours.setDeleted(false);
                try {

                    int insert = tpmShopHoursMapper.insert(tpmShopHours);
                    if (insert > 0) {
                        log.info("【请求结束】保存店铺营业时间成功,shopId:{} dayOfWeek:{}", shopId,dayOfWeek);
                    }
                } catch (Exception e) {
                    log.error("【请求结束】保存店铺营业时间失败,shopId:{} error={}", shopId, e.getMessage(), e);
                    return ResponseUtil.fail(-1,e.getMessage());
                }
            } else {
                tpmShopHours.setIsOpen(isOpen);
                if (Objects.nonNull(isOpen)&&isOpen){
                    tpmShopHours.setOpeningTime(openingTime.toLocalTime());
                    tpmShopHours.setClosingTime(closingTime.toLocalTime());
                }else{
                    tpmShopHours.setOpeningTime(null);
                    tpmShopHours.setClosingTime(null);
                }
                tpmShopHours.setStatusDesc(statusDesc);
                try {
                    int update = tpmShopHoursMapper.updateByPrimaryKey(tpmShopHours);
                    if (update > 0) {
                        log.info("【请求结束】更新店铺营业时间成功,shopId:{} dayOfWeek:{}", shopId,dayOfWeek);
                    }
                } catch (Exception e) {
                    log.error("【请求结束】更新店铺营业时间失败,shopId:{} error={}", shopId, e.getMessage(), e);
                    return ResponseUtil.fail(-1,e.getMessage());
                }
            }
        }
        return ResponseUtil.ok();
    }

    public static Time convertStringToTime(String timeStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        try {
            java.util.Date date = sdf.parse(timeStr);
            return new Time(date.getTime());
        } catch (ParseException e) {
            log.error("时间转换错误：" + e.getMessage(), e);
            return null;
        }
    }


    public TpmShopOpenStatusDTO getShopOpenStatus(Integer shopId) {
        TpmShopOpenStatusDTO tpmShopOpenStatusDTO = new TpmShopOpenStatusDTO();
        tpmShopOpenStatusDTO.setShopId(shopId);
        tpmShopOpenStatusDTO.setOpen(true);
        try {
            TpmShopHoursExample tpmShopHoursExample = new TpmShopHoursExample();
            tpmShopHoursExample.createCriteria().andShopIdEqualTo(shopId).andDeletedEqualTo(false);
            List<TpmShopHours> tpmShopHours = tpmShopHoursMapper.selectByExample(tpmShopHoursExample);
            if (CollectionUtils.isEmpty(tpmShopHours)) {
                tpmShopOpenStatusDTO.setOpen(false);
                tpmShopOpenStatusDTO.setStatusDesc("店铺未配置营业时间");
                return tpmShopOpenStatusDTO;
            }
            TpmShopHours shopConfig = tpmShopHours.stream().filter(t -> Objects.equals(t.getDayOfWeek(), 0)).findFirst().orElse(null);
            if (Objects.isNull(shopConfig)) {
                tpmShopOpenStatusDTO.setOpen(false);
                tpmShopOpenStatusDTO.setStatusDesc("店铺未配置营业时间");
                return tpmShopOpenStatusDTO;
            }
            Boolean isOpen = shopConfig.getIsOpen();
            if (!isOpen) {
                //0的代表 店铺是否开业，open代表是否在营业时间内
                tpmShopOpenStatusDTO.setWorking(false);
                tpmShopOpenStatusDTO.setOpen(false);
                tpmShopOpenStatusDTO.setStatusDesc(shopConfig.getStatusDesc() == null ? "店铺未营业" : shopConfig.getStatusDesc());
                return tpmShopOpenStatusDTO;
            }else{
                tpmShopOpenStatusDTO.setWorking(true);
            }
            LocalDate today = LocalDate.now();
            DayOfWeek dayOfWeek = today.getDayOfWeek();
            int value = dayOfWeek.getValue();
            TpmShopHours todayConfig = tpmShopHours.stream().filter(t -> Objects.equals(t.getDayOfWeek(), value)).findFirst().orElse(null);
            if (Objects.isNull(todayConfig)) {
                tpmShopOpenStatusDTO.setOpen(false);
                tpmShopOpenStatusDTO.setStatusDesc("店铺未配置营业时间");
                return tpmShopOpenStatusDTO;
            }
            isOpen = todayConfig.getIsOpen();
            if (!isOpen) {
                tpmShopOpenStatusDTO.setOpen(false);
                tpmShopOpenStatusDTO.setStatusDesc(todayConfig.getStatusDesc() == null ? "店铺未营业" : todayConfig.getStatusDesc());
                return tpmShopOpenStatusDTO;
            }
            LocalTime openingTime = todayConfig.getOpeningTime();
            LocalTime closingTime = todayConfig.getClosingTime();
            String openingTimeString = openingTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            String closingTimeString = closingTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            String statusDesc = "下单时间为" + openingTimeString + "-" + closingTimeString;

            // 设置开始时间和结束时间
            tpmShopOpenStatusDTO.setOpeningTime(openingTime);
            tpmShopOpenStatusDTO.setClosingTime(closingTime);

            LocalTime now = LocalTime.now();
            if (now.isBefore(openingTime) || now.isAfter(closingTime)) {
                tpmShopOpenStatusDTO.setOpen(false);
                tpmShopOpenStatusDTO.setStatusDesc(statusDesc);
                return tpmShopOpenStatusDTO;
            }
            tpmShopOpenStatusDTO.setOpenTimeDesc(statusDesc);
            return tpmShopOpenStatusDTO;
        } catch (TooManyResultsException e) {
            log.error("查询到多条数据");
            tpmShopOpenStatusDTO.setOpen(false);
            tpmShopOpenStatusDTO.setStatusDesc("店铺未营业");
            return tpmShopOpenStatusDTO;
        }
    }

    public Object setShopStatus(TpmShopHourSettingReqVO reqVO) {
        if (Objects.isNull(reqVO)){
            return ResponseUtil.fail(-1,"参数为空");
        }
        if (Objects.isNull(reqVO.getShopId())){
            return ResponseUtil.fail(-1,"参数为空");
        }
        if (!Objects.equals(reqVO.getDayOfWeek(),0)){
            return ResponseUtil.fail(-1,"参数错误");
        }
        Integer shopId = reqVO.getShopId();
        Integer dayOfWeek = reqVO.getDayOfWeek();
        Boolean isOpen = reqVO.getIsOpen();
        String statusDesc = reqVO.getStatusDes();
        TpmShopHoursExample tpmShopHoursExample = new TpmShopHoursExample();
        tpmShopHoursExample.createCriteria().andShopIdEqualTo(shopId).andDayOfWeekEqualTo(dayOfWeek).andDeletedEqualTo(false);
        TpmShopHours tpmShopHour = tpmShopHoursMapper.selectOneByExample(tpmShopHoursExample);
        if (Objects.isNull(tpmShopHour)) {
            tpmShopHour = new TpmShopHours();
            tpmShopHour.setShopId(shopId);
            tpmShopHour.setDayOfWeek(dayOfWeek);
            tpmShopHour.setIsOpen(isOpen);
            tpmShopHour.setStatusDesc(statusDesc);
            tpmShopHour.setDeleted(false);
            try {

                int insert = tpmShopHoursMapper.insert(tpmShopHour);
                if (insert > 0) {
                    log.info("【请求结束】保存店铺营业时间成功,shopId:{} dayOfWeek:{}", shopId,dayOfWeek);
                }
            } catch (Exception e) {
                log.error("【请求结束】保存店铺营业时间失败,shopId:{} error={}", shopId, e.getMessage(), e);
                return ResponseUtil.fail(-1,e.getMessage());
            }
        } else {
            tpmShopHour.setIsOpen(isOpen);
            tpmShopHour.setStatusDesc(statusDesc);
            try {
                int update = tpmShopHoursMapper.updateByPrimaryKeySelective(tpmShopHour);
                if (update > 0) {
                    log.info("【请求结束】更新店铺营业时间成功,shopId:{} dayOfWeek:{}", shopId,dayOfWeek);
                }
            } catch (Exception e) {
                log.error("【请求结束】更新店铺营业时间失败,shopId:{} error={}", shopId, e.getMessage(), e);
                return ResponseUtil.fail(-1,e.getMessage());
            }
        }
        return ResponseUtil.ok();

    }

    public Object getShopSetting(Integer shopId) {
        if (Objects.isNull(shopId)){
            return ResponseUtil.fail(-1,"参数为空");
        }
        TpmShopHoursExample tpmShopHoursExample = new TpmShopHoursExample();
        tpmShopHoursExample.createCriteria().andShopIdEqualTo(shopId).andDeletedEqualTo(false);
        List<TpmShopHours> tpmShopHours = tpmShopHoursMapper.selectByExample(tpmShopHoursExample);
        TpmShopHours shopInfoHour = tpmShopHours.stream().filter(t -> Objects.equals(t.getDayOfWeek(), 0)).findFirst().orElse(null);
        List<TpmShopHours> tpmShopHoursList = tpmShopHours.stream().filter(t -> !Objects.equals(t.getDayOfWeek(), 0)).collect(Collectors.toList());
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("shopInfo",shopInfoHour);
        resultMap.put("shopHourSetting",tpmShopHoursList);
        return ResponseUtil.ok(resultMap);
    }
}
