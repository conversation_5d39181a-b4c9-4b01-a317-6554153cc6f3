package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmShopInfoMapper;
import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.domain.TpmShopInfoExample;
import com.pioneer.mall.db.util.ThreadContextUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/24 21:21
 */
@Service
public class TpmShopInfoService {

    @Resource
    private TpmShopInfoMapper tpmShopInfoMapper;


    public List<TpmShopInfo> getList(){
        TpmShopInfoExample example = new TpmShopInfoExample();
        example.createCriteria().andDeletedEqualTo(false);
        return tpmShopInfoMapper.selectByExample(example);
    }

    public TpmShopInfo getShopInfoById(Integer shopId) {
        if (shopId == null) {
            return null;
        }
        return tpmShopInfoMapper.selectByPrimaryKey(shopId);
    }

    public String getLatitudeByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getLatitude().toString();
        }
        return null;
    }

    public String getLongitudeByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getLongitude().toString();
        }
        return null;
    }

    /**
     * 获取默认店铺是否支持自提
     *
     * @return 是否开启自提（0 表示否，1 表示是）
     */
    public Boolean getSelfPickupByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getSelfPickup();
        }
        return null;
    }


    /**
     * 获取默认店铺是否支持堂食
     *
     * @return 是否开启堂食（0 表示否，1 表示是）
     */
    public Boolean getDineInByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getDineIn();
        }
        return null;
    }

    /**
     * 获取默认店铺是否支持配送
     *
     * @return 是否开启外送（0 表示否，1 表示是）
     */
    public Boolean getDeliveryByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getDelivery();
        }
        return null;
    }

    public List<TpmShopInfo> selectById(List<Integer> shopIdList) {
        if (shopIdList != null && !shopIdList.isEmpty()) {
            TpmShopInfoExample example = new TpmShopInfoExample();
            example.createCriteria().andIdIn(shopIdList).andDeletedEqualTo(false);
            return tpmShopInfoMapper.selectByExample(example);
        }
        return new ArrayList<>();
    }

    public Map<Integer,String> selectShopInfo(List<Integer> shopIdList) {
        Map<Integer, String> shopInfoMap = new HashMap<>();
        if (shopIdList != null && !shopIdList.isEmpty()) {
            TpmShopInfoExample example = new TpmShopInfoExample();
            example.createCriteria().andIdIn(shopIdList).andDeletedEqualTo(false);
            List<TpmShopInfo>  tpmShopInfoList = tpmShopInfoMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(tpmShopInfoList)) {
                shopInfoMap = tpmShopInfoList.stream().collect(Collectors.toMap(TpmShopInfo::getId, TpmShopInfo::getShopName));
            }
        }
        return shopInfoMap;
    }

    public TpmShopInfo findById(Integer shopId) {
        if (shopId == null) {
            return null;
        }
        return tpmShopInfoMapper.selectByPrimaryKey(shopId);
    }
}
