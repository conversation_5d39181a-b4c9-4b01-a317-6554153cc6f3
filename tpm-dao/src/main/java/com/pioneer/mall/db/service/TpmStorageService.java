package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmStorageMapper;
import com.pioneer.mall.db.domain.TpmStorage;
import com.pioneer.mall.db.domain.TpmStorageExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmStorageService {
	@Autowired
	private TpmStorageMapper storageMapper;

	public void deleteByKey(String key) {
		TpmStorageExample example = new TpmStorageExample();
		example.or().andKeyEqualTo(key);
		storageMapper.logicalDeleteByExample(example);
	}

	public void add(TpmStorage storageInfo) {
		storageInfo.setAddTime(LocalDateTime.now());
		storageInfo.setUpdateTime(LocalDateTime.now());
		storageMapper.insertSelective(storageInfo);
	}

	public TpmStorage findByKey(String key) {
		TpmStorageExample example = new TpmStorageExample();
		example.or().andKeyEqualTo(key).andDeletedEqualTo(false);
		return storageMapper.selectOneByExample(example);
	}

	public int update(TpmStorage storageInfo) {
		storageInfo.setUpdateTime(LocalDateTime.now());
		return storageMapper.updateByPrimaryKeySelective(storageInfo);
	}

	public TpmStorage findById(Integer id) {
		return storageMapper.selectByPrimaryKey(id);
	}

	public List<TpmStorage> querySelective(String key, String name, Integer page, Integer limit, String sort,
			String order) {
		TpmStorageExample example = new TpmStorageExample();
		TpmStorageExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(key)) {
			criteria.andKeyEqualTo(key);
		}
		if (!StringUtils.isEmpty(name)) {
			criteria.andNameLike("%" + name + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, limit);
		return storageMapper.selectByExample(example);
	}
}
