package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmSystemMapper;
import com.pioneer.mall.db.domain.TpmSystem;
import com.pioneer.mall.db.domain.TpmSystemExample;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TpmSystemConfigService {
	@Resource
	private TpmSystemMapper systemMapper;

	public List<TpmSystem> queryAll() {
		TpmSystemExample example = new TpmSystemExample();
		example.or();
		return systemMapper.selectByExample(example);
	}
}
