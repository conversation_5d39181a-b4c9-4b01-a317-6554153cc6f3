package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;

import com.pioneer.mall.db.dao.TpmTopicMapper;
import com.pioneer.mall.db.domain.TpmTopic;
import com.pioneer.mall.db.domain.TpmTopic.Column;
import com.pioneer.mall.db.domain.TpmTopicExample;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class TpmTopicService {
	@Resource
	private TpmTopicMapper topicMapper;
	private TpmTopic.Column[] columns = new Column[] { Column.id, Column.title, Column.subtitle, Column.price, Column.picUrl,
			Column.readCount };

	public List<TpmTopic> queryList(int offset, int limit) {
		return queryList(offset, limit, "add_time", "desc");
	}

	public List<TpmTopic> queryList(int offset, int limit, String sort, String order) {
		TpmTopicExample example = new TpmTopicExample();
		example.or().andDeletedEqualTo(false);
		example.setOrderByClause(sort + " " + order);
		PageHelper.startPage(offset, limit);
		return topicMapper.selectByExampleSelective(example, columns);
	}

	public int queryTotal() {
		TpmTopicExample example = new TpmTopicExample();
		example.or().andDeletedEqualTo(false);
		return (int) topicMapper.countByExample(example);
	}

	public TpmTopic findById(Integer id) {
		TpmTopicExample example = new TpmTopicExample();
		example.or().andIdEqualTo(id).andDeletedEqualTo(false);
		return topicMapper.selectOneByExampleWithBLOBs(example);
	}

	public List<TpmTopic> queryRelatedList(Integer id, int offset, int limit) {
		TpmTopicExample example = new TpmTopicExample();
		example.or().andIdEqualTo(id).andDeletedEqualTo(false);
		List<TpmTopic> topics = topicMapper.selectByExample(example);
		if (topics.size() == 0) {
			return queryList(offset, limit, "add_time", "desc");
		}
		TpmTopic topic = topics.get(0);

		example = new TpmTopicExample();
		example.or().andIdNotEqualTo(topic.getId()).andDeletedEqualTo(false);
		PageHelper.startPage(offset, limit);
		List<TpmTopic> relateds = topicMapper.selectByExampleWithBLOBs(example);
		if (relateds.size() != 0) {
			return relateds;
		}

		return queryList(offset, limit, "add_time", "desc");
	}

	public List<TpmTopic> querySelective(String title, String subtitle, Integer page, Integer limit, String sort,
			String order) {
		TpmTopicExample example = new TpmTopicExample();
		TpmTopicExample.Criteria criteria = example.createCriteria();

		if (!StringUtils.isEmpty(title)) {
			criteria.andTitleLike("%" + title + "%");
		}
		if (!StringUtils.isEmpty(subtitle)) {
			criteria.andSubtitleLike("%" + subtitle + "%");
		}
		criteria.andDeletedEqualTo(false);

		if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
			example.setOrderByClause(sort + " " + order);
		}

		PageHelper.startPage(page, limit);
		return topicMapper.selectByExampleWithBLOBs(example);
	}

	public int updateById(TpmTopic topic) {
		topic.setUpdateTime(LocalDateTime.now());
		TpmTopicExample example = new TpmTopicExample();
		example.or().andIdEqualTo(topic.getId());
		return topicMapper.updateByExampleSelective(topic, example);
	}

	public void deleteById(Integer id) {
		topicMapper.logicalDeleteByPrimaryKey(id);
	}

	public void add(TpmTopic topic) {
		topic.setAddTime(LocalDateTime.now());
		topic.setUpdateTime(LocalDateTime.now());
		topicMapper.insertSelective(topic);
	}

}
