package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.bean.DayStatis;
import com.pioneer.mall.db.dao.TpmBalanceMapper;
import com.pioneer.mall.db.dao.TpmUserAccountMapper;
import com.pioneer.mall.db.dao.TpmUserMapper;
import com.pioneer.mall.db.dao.ex.StatMapper;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.enums.StatusEnum;
import com.pioneer.mall.db.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TpmUserService {

    @Resource
    private TpmUserMapper userMapper;
    @Resource
    private TpmBalanceMapper tpmBalanceMapper;
    @Resource
    @Lazy
    private TpmPointService tpmPointService;
    @Resource
    private TpmBalanceService tpmBalanceService;
    @Resource
    private TpmUserAccountMapper userAccountMapper;

    @Resource
    private StatMapper statMapper;

    public TpmUser findById(Integer userId) {
        return userMapper.selectByPrimaryKey(userId);
    }

    public UserVo findUserVoById(Integer userId) {
        TpmUser user = findById(userId);
        UserVo userVo = new UserVo();
        userVo.setNickname(user.getNickname());
        userVo.setAvatar(user.getAvatar());
        return userVo;
    }

    public TpmUser queryByOid(String openId) {
        TpmUserExample example = new TpmUserExample();
        example.or().andWeixinOpenidEqualTo(openId).andDeletedEqualTo(false);
        return userMapper.selectOneByExample(example);
    }

    public void add(TpmUser user) {
        user.setAddTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        userMapper.insertSelective(user);
    }

    public int updateById(TpmUser user) {
        user.setUpdateTime(LocalDateTime.now());
        return userMapper.updateByPrimaryKeySelective(user);
    }

    public List<TpmUser> querySelective(String username, String mobile, Integer page, Integer size, String sort,
                                        String order) {
        TpmUserExample example = new TpmUserExample();
        TpmUserExample.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(username)) {
            criteria.andNicknameLike("%" + username + "%");
        }
        if (!StringUtils.isEmpty(mobile)) {
            criteria.andMobileEqualTo(mobile);
        }
        criteria.andDeletedEqualTo(false);

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        PageHelper.startPage(page, size);
        return userMapper.selectByExample(example);
    }

    public int count() {
        TpmUserExample example = new TpmUserExample();
        example.or().andDeletedEqualTo(false);

        return (int) userMapper.countByExample(example);
    }

    public List<TpmUser> queryByUsername(String username) {
        TpmUserExample example = new TpmUserExample();
        example.or().andUsernameEqualTo(username).andDeletedEqualTo(false);
        return userMapper.selectByExample(example);
    }

    public boolean checkByUsername(String username) {
        TpmUserExample example = new TpmUserExample();
        example.or().andUsernameEqualTo(username).andDeletedEqualTo(false);
        return userMapper.countByExample(example) != 0;
    }

    public List<TpmUser> queryByMobile(String mobile) {
        TpmUserExample example = new TpmUserExample();
        example.or().andMobileEqualTo(mobile).andDeletedEqualTo(false);
        return userMapper.selectByExample(example);
    }

    public List<TpmUser> queryByOpenid(String openid) {
        TpmUserExample example = new TpmUserExample();
        example.or().andWeixinOpenidEqualTo(openid).andDeletedEqualTo(false);
        return userMapper.selectByExample(example);
    }

    public void deleteById(Integer id) {
        userMapper.logicalDeleteByPrimaryKey(id);
    }

    /**
     * 审批代理申请
     *
     * @param userAccount
     */
    public void approveAgency(Integer userId, Integer settlementRate, String shareUrl) {
        //获取账户数据
        TpmUserAccountExample example = new TpmUserAccountExample();
        example.or().andUserIdEqualTo(userId);

        TpmUserAccount dbAccount = userAccountMapper.selectOneByExample(example);
        if (dbAccount == null) {
            throw new RuntimeException("申请账户不存在");
        }
        dbAccount.setShareUrl(shareUrl);
        if (!StringUtils.isEmpty(settlementRate)) {
            dbAccount.setSettlementRate(settlementRate);
        }
        dbAccount.setModifyTime(LocalDateTime.now());
        userAccountMapper.updateByPrimaryKey(dbAccount);

        //更新会员状态和类型
        TpmUser user = findById(userId);
        user.setUserLevel((byte) 2);//区域代理用户
        user.setStatus((byte) 0);//正常状态
        updateById(user);

    }

    public TpmUserAccount detailApproveByUserId(Integer userId) {
        // 获取账户数据
        TpmUserAccountExample example = new TpmUserAccountExample();
        example.or().andUserIdEqualTo(userId);

        TpmUserAccount dbAccount = userAccountMapper.selectOneByExample(example);
        return dbAccount;
    }

    public List<TpmUser> queryDtsUserListByNickname(String username, String mobile) {
        TpmUserExample example = new TpmUserExample();
        TpmUserExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(username)) {
            criteria.andNicknameLike("%" + username + "%");
        }
        if (!StringUtils.isEmpty(mobile)) {
            criteria.andMobileEqualTo(mobile);
        }
        criteria.andDeletedEqualTo(false);
        return userMapper.selectByExample(example);
    }

    public List<DayStatis> recentCount(int statisDaysRang) {
        return statMapper.statisIncreaseUserCnt(statisDaysRang);
    }

    public void updateUserInfo(TpmUser tpmUser) {
        if (Objects.isNull(tpmUser)) {
            return;
        }
        userMapper.updateByPrimaryKeySelective(tpmUser);

    }

    @Transactional(rollbackFor = Exception.class)
    public void manualChangeBalance(Integer userId, BigDecimal changeAmount, Boolean syncPoint) {
        log.info("手动修改用户余额，userId:{},changeAmount:{},isAddPoint:{}", userId, changeAmount, syncPoint);
        synchronized (this) {
            TpmUser user = this.findById(userId);
            if (Objects.isNull(user)) {
                throw new RuntimeException("用户不存在");
            }
            BigDecimal balance = user.getBalance();
            if (changeAmount.compareTo(BigDecimal.ZERO) < 0 && balance.compareTo(changeAmount) < 0) {
                throw new RuntimeException("操作失败，会员余额不足,当前余额:" + balance);
            }
            user.setBalance(balance.add(changeAmount));

            int i = userMapper.updateByPrimaryKeySelective(user);

            if (i < 1) {
                throw new RuntimeException("更新失败");
            }

//            if (syncPoint) {
//                TpmPointDto tpmPointDto = new TpmPointDto();
//                tpmPointDto.setUserId(userId);
//                tpmPointDto.setOrderSn("");
//                tpmPointDto.setAmount(changeAmount);
//                if (changeAmount.compareTo(BigDecimal.ZERO) >= 0) {
//                    tpmPointDto.setDescription("手动添加积分:" + changeAmount);
//                } else {
//                    tpmPointDto.setDescription("手动扣减积分:" + changeAmount.abs());
//                }
//                tpmPointService.addPoint(tpmPointDto);
//            }

            TpmBalance tpmBalance = new TpmBalance();
            tpmBalance.setLeftBalance(user.getBalance());
            tpmBalance.setUserId(userId);
            tpmBalance.setOperator("管理员");
            tpmBalance.setOrderSn("");
            tpmBalance.setStatus(StatusEnum.ENABLED.getKey());
            tpmBalance.setAddTime(LocalDateTime.now());
            tpmBalance.setUpdateTime(LocalDateTime.now());
            tpmBalance.setMobile(user.getMobile());
            tpmBalance.setDeleted(false);
            tpmBalance.setAmount(changeAmount);
            if (changeAmount.compareTo(BigDecimal.ZERO) >= 0) {
                tpmBalance.setRemark("手动添加余额:" + changeAmount);
            } else {
                tpmBalance.setRemark("手动扣减余额:" + changeAmount.abs());
            }
            tpmBalanceMapper.insert(tpmBalance);

        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void manualChangePoint(Integer userId, BigDecimal changeAmount) {
        log.info("手动修改用户积分，userId:{},changeAmount:{} ", userId, changeAmount);
        synchronized (this) {
            TpmUser user = this.findById(userId);
            if (Objects.isNull(user)) {
                throw new RuntimeException("用户不存在");
            }
            TpmPointDto tpmPointDto = new TpmPointDto();
            tpmPointDto.setUserId(userId);
            tpmPointDto.setOrderSn("");
            tpmPointDto.setAmount(changeAmount);
            if (changeAmount.compareTo(BigDecimal.ZERO) >= 0) {
                tpmPointDto.setDescription("手动添加积分:" + changeAmount);
            } else {
                tpmPointDto.setDescription("手动扣减积分:" + changeAmount.abs());
            }
            tpmPointService.addPoint(tpmPointDto);
        }
    }
}
