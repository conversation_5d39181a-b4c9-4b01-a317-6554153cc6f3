package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.bean.search.TpmVipSearch;
import com.pioneer.mall.db.dao.TpmVipMapper;
import com.pioneer.mall.db.domain.TpmVip;
import com.pioneer.mall.db.domain.TpmVipExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/24 11:15
 */
@Service
public class TpmVipService {
    @Autowired
    private TpmVipMapper tpmVipMapper;

    @Transactional(rollbackFor = Exception.class)
    public void add(TpmVip vip) {
        vip.setAddTime(LocalDateTime.now());
        vip.setUpdateTime(LocalDateTime.now());
        vip.setDeleted(false);
        tpmVipMapper.insert(vip);
    }

    public TpmVip findById(Integer id) {
        return tpmVipMapper.selectByPrimaryKey(id);
    }

    public List<TpmVip> querySelective(TpmVipSearch tpmVipSearch) {
        TpmVipExample tpmVipExample = new TpmVipExample();
        TpmVipExample.Criteria criteria = tpmVipExample.createCriteria();
        String vipName = tpmVipSearch.getVipName();
        if (Objects.nonNull(vipName)) {
            criteria.andVipNameLike("%" + vipName + "%");
        }
        if (Objects.nonNull(tpmVipSearch.getEnable())) {
            criteria.andEnableEqualTo(tpmVipSearch.getEnable());
        }
        criteria.andLogicalDeleted(false);
        // 排序
        String sort = tpmVipSearch.getSort();
        String order = tpmVipSearch.getOrder();
        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            tpmVipExample.setOrderByClause(sort + " " + order);
        }
        PageHelper.startPage(tpmVipSearch.getCurrentPage(), tpmVipSearch.getPageSize());
        return tpmVipMapper.selectByExample(tpmVipExample);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateById(TpmVip vip) {
        vip.setUpdateTime(LocalDateTime.now());
        return tpmVipMapper.updateByPrimaryKeySelective(vip);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteById(TpmVip tpmVip) {
        if (Objects.isNull(tpmVip) || Objects.isNull(tpmVip.getId())) {
            return 0;
        }
        return tpmVipMapper.deleteByPrimaryKey(tpmVip.getId());
    }

    /**
     * 查询一定积分下 能升到的最高VIP等级
     *
     * @param bigDecimal
     * @return
     */
    public TpmVip queryVipLevel(BigDecimal bigDecimal) {
        TpmVipExample tpmVipExample = new TpmVipExample();
        tpmVipExample.createCriteria().andEnableEqualTo(true).andNeedPointLessThanOrEqualTo(bigDecimal).andLogicalDeleted(false);
        List<TpmVip> tpmVipList = tpmVipMapper.selectByExample(tpmVipExample);
        if (tpmVipList.isEmpty()) {
            return null;
        }
        return tpmVipList.stream().sorted(Comparator.comparing(TpmVip::getNeedPoint).reversed()).findAny().orElse(null);
    }

    public static void main(String[] args) {
        TpmVip tpmVip = new TpmVip();
        TpmVip tpmVip1 = new TpmVip();
        TpmVip tpmVip2 = new TpmVip();
        TpmVip tpmVip3 = new TpmVip();
        tpmVip.setNeedPoint(BigDecimal.ONE);
        tpmVip1.setNeedPoint(BigDecimal.TEN);
        tpmVip2.setNeedPoint(BigDecimal.TEN.add(BigDecimal.TEN));
        tpmVip3.setNeedPoint(BigDecimal.TEN.add(BigDecimal.ONE));
        List<TpmVip> list = Arrays.asList(tpmVip, tpmVip1, tpmVip2, tpmVip3);
        TpmVip tpmVip4 = list.stream().sorted(Comparator.comparing(TpmVip::getNeedPoint).reversed()).findAny().orElse(null);
        System.out.println(tpmVip4.getNeedPoint());
    }
}
