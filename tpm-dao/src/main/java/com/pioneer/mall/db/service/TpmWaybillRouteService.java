package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmDeliveryRecordMapper;
import com.pioneer.mall.db.dao.TpmWaybillRouteMapper;
import com.pioneer.mall.db.domain.TpmWaybillRoute;
import com.pioneer.mall.db.domain.TpmWaybillRouteExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/18 21:53
 */
@Service
public class TpmWaybillRouteService {

    @Autowired
    private TpmWaybillRouteMapper tpmWaybillRouteMapper;

    public List<TpmWaybillRoute> findByTpmOrderId(Integer tpmOrderId) {
        TpmWaybillRouteExample example = new TpmWaybillRouteExample();
        example.or().andTpmOrderIdEqualTo(tpmOrderId);
        return tpmWaybillRouteMapper.selectByExample(example);
    }

    public List<TpmWaybillRoute> findByOrderId(String orderId) {
        TpmWaybillRouteExample example = new TpmWaybillRouteExample();
        example.or().andOrderIdEqualTo(orderId);
        return tpmWaybillRouteMapper.selectByExample(example);
    }

    public int add(TpmWaybillRoute tpmWaybillRoute) {
        return tpmWaybillRouteMapper.insertSelective(tpmWaybillRoute);
    }

    public int update(TpmWaybillRoute tpmWaybillRoute) {
        return tpmWaybillRouteMapper.updateByPrimaryKeySelective(tpmWaybillRoute);
    }

    public TpmWaybillRoute findById(Integer id) {
        return tpmWaybillRouteMapper.selectByPrimaryKey(id);
    }

    public int deleteById(Integer id) {
        return tpmWaybillRouteMapper.deleteByPrimaryKey(id);
    }

    public long countByTpmOrderId(Integer tpmOrderId) {
        TpmWaybillRouteExample example = new TpmWaybillRouteExample();
        example.or().andTpmOrderIdEqualTo(tpmOrderId);
        return tpmWaybillRouteMapper.countByExample(example);
    }

}
