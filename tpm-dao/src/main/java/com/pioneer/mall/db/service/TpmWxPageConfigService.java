package com.pioneer.mall.db.service;

import com.github.pagehelper.PageHelper;
import com.pioneer.mall.db.dao.TpmWxPageConfigMapper;
import com.pioneer.mall.db.domain.TpmWxPageConfig;
import com.pioneer.mall.db.domain.TpmWxPageConfigExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/4/29 17:29
 */
@Service
public class TpmWxPageConfigService {
    @Autowired
    private TpmWxPageConfigMapper tpmWxPageConfigMapper;

    public void add(TpmWxPageConfig pageConfig) {
        pageConfig.setAddTime(LocalDateTime.now());
        pageConfig.setUpdateTime(LocalDateTime.now());
        tpmWxPageConfigMapper.insertSelective(pageConfig);
    }

    public List<TpmWxPageConfig> querySelective(String menu, String type, String url, Integer page, Integer limit, String sort, String order) {
        TpmWxPageConfigExample example = new TpmWxPageConfigExample();
        TpmWxPageConfigExample.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(menu)) {
            criteria.andMenuLike("%" + menu + "%");
        }
        if (!StringUtils.isEmpty(type)) {
            criteria.andTypeEqualTo(type);
        }
        if (!StringUtils.isEmpty(url)) {
            criteria.andUrlLike("%" + url + "%");
        }
        criteria.andDeletedEqualTo(false);

        if (!StringUtils.isEmpty(sort) && !StringUtils.isEmpty(order)) {
            example.setOrderByClause(sort + " " + order);
        }

        PageHelper.startPage(page, limit);
        return tpmWxPageConfigMapper.selectByExample(example);
    }

    public List<TpmWxPageConfig> queryPageByMenu(String menu) {
        TpmWxPageConfigExample example = new TpmWxPageConfigExample();
        TpmWxPageConfigExample.Criteria criteria = example.createCriteria();
        criteria.andMenuEqualTo(menu).andTypeEqualTo("pic").andDeletedEqualTo(false);
        return tpmWxPageConfigMapper.selectByExample(example);
    }

    public List<TpmWxPageConfig> queryDescByMenu(String menu) {
        TpmWxPageConfigExample example = new TpmWxPageConfigExample();
        TpmWxPageConfigExample.Criteria criteria = example.createCriteria();
        criteria.andMenuEqualTo(menu).andTypeEqualTo("desc").andDeletedEqualTo(false);
        return tpmWxPageConfigMapper.selectByExampleWithBLOBs(example);
    }


        public TpmWxPageConfig findById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return tpmWxPageConfigMapper.selectByPrimaryKey(id);
    }

    public int update(TpmWxPageConfig tpmWxPageConfig) {
        tpmWxPageConfig.setUpdateTime(LocalDateTime.now());
        return tpmWxPageConfigMapper.updateByPrimaryKeySelective(tpmWxPageConfig);
    }

    public void deleteByKey(String key) {
        TpmWxPageConfigExample example = new TpmWxPageConfigExample();
        example.or().andKeyEqualTo(key);
        tpmWxPageConfigMapper.logicalDeleteByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKey(Integer id) {
        tpmWxPageConfigMapper.deleteByPrimaryKey(id);
    }
}
