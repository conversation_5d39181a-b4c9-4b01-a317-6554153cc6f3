package com.pioneer.mall.db.util;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum SaleRangeEnum {
    WM(0, 4, "外卖"),
    ZT(1, 1, "自提"),
    TS(2, 2, "堂食");

    private final Integer pageSaleRange;
    private final Integer dbSaleRange;
    private final String desc;


    public static boolean pageSaleRangeInDbSaleRange(Integer pageSaleRange, Integer dbSaleRange) {

        SaleRangeEnum saleRangeEnum = getEnumByPageSaleRange(pageSaleRange);
        if (saleRangeEnum == null) {
            return false;
        }
        Integer _dbSaleRange = saleRangeEnum.getDbSaleRange();

        return (dbSaleRange & _dbSaleRange) == _dbSaleRange;
    }

    public static SaleRangeEnum getEnumByDbSaleRange(Integer dbSaleRange) {
        for (SaleRangeEnum saleRangeEnum : SaleRangeEnum.values()) {
            if (Objects.equals(dbSaleRange, saleRangeEnum.getDbSaleRange())) {
                return saleRangeEnum;
            }
        }
        return null;
    }

    public static SaleRangeEnum getEnumByPageSaleRange(Integer pageSaleRange) {
        for (SaleRangeEnum saleRangeEnum : SaleRangeEnum.values()) {
            if (Objects.equals(pageSaleRange, saleRangeEnum.getPageSaleRange())) {
                return saleRangeEnum;
            }
        }
        return null;
    }


    /**
     * 将数据库中存储的saleRange转化为页面显示的saleRange
     *
     * @param dbSaleRange 销售范围(二进制存储)：1：自提；2：堂食；4：外送
     * @return 页面显示的saleRange ：0是外卖，1是自提，2是堂食
     */
    public static List<Integer> changeToPageSaleRangeByDb(Integer dbSaleRange) {

        if (dbSaleRange == null) {
            return new ArrayList<>();
        }
        List<Integer> pageSaleRangeList = new ArrayList<>();
        if ((dbSaleRange & SaleRangeEnum.WM.getDbSaleRange()) == SaleRangeEnum.WM.getDbSaleRange()) {
            pageSaleRangeList.add(SaleRangeEnum.WM.getPageSaleRange());
        }
        if ((dbSaleRange & SaleRangeEnum.TS.getDbSaleRange()) == SaleRangeEnum.TS.getDbSaleRange()) {
            pageSaleRangeList.add(SaleRangeEnum.TS.getPageSaleRange());
        }
        if ((dbSaleRange & SaleRangeEnum.ZT.getDbSaleRange()) == SaleRangeEnum.ZT.getDbSaleRange()) {
            pageSaleRangeList.add(SaleRangeEnum.ZT.getPageSaleRange());
        }
        return pageSaleRangeList;
    }

    /**
     * 将页面显示的saleRange转化为数据库中存储的saleRange
     *
     * @param pageSaleRangeList 页面显示的saleRange ：0是外卖，1是自提，2是堂食
     * @return 数据库中存储的saleRange：1：自提；2：堂食；4：外送
     */
    public static Integer changeToDbSaleRangeByPage(List<Integer> pageSaleRangeList) {

        if (pageSaleRangeList == null) {
            return null;
        }
        Integer dbSaleRange = 0;
        for (Integer pageSaleRange : pageSaleRangeList) {
            for (SaleRangeEnum saleRangeEnum : SaleRangeEnum.values()) {
                if (Objects.equals(pageSaleRange, saleRangeEnum.getPageSaleRange())) {
                    dbSaleRange = dbSaleRange | saleRangeEnum.getDbSaleRange();
                }
            }
        }
        return dbSaleRange;

    }
}
