package com.pioneer.mall.db.util;

import com.alibaba.fastjson.JSONArray;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.dto.TpmGoodsSpecificationsDto;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class SpecificationUtil {

    public static List<TpmGoodsSpecificationsDto> getSpecificationsDto(List<TpmGoodsAttributesDto> goodsAttributesDtos) {
        if (CollectionUtils.isEmpty(goodsAttributesDtos)) {
            return null;
        }

        return goodsAttributesDtos.stream()
                .flatMap(goodsAttributesDto -> goodsAttributesDto.getGoodsSpecifications().stream())
                .map(z -> {
                    if (z.getSelected() == null) {
                        z.setSelected(false);
                    }
                    return z;
                })
                .filter(TpmGoodsSpecificationsDto::getSelected)
                // 根据id排序
                .sorted(Comparator.comparingInt(TpmGoodsSpecificationsDto::getId))
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        String message = "[\n" +
                "    {\n" +
                "        \"id\": 1,\n" +
                "        \"value\": \"属性名1\",\n" +
                "        \"goodsSpecifications\": [\n" +
                "            {\n" +
                "                \"id\": 3,\n" +
                "                \"value\": \"规格名称dd2222\",\n" +
                "                \"price\": \"109.08\",\n" +
                "                \"required\": \"true\",\n" +
                "                \"enable\": \"true\",\n" +
                "                \"selected\": true\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"id\": 2,\n" +
                "        \"value\": \"属性名称2\",\n" +
                "        \"goodsSpecifications\": [\n" +
                "            {\n" +
                "                \"id\": 34,\n" +
                "                \"value\": \"规格名称3333dd\",\n" +
                "                \"price\": \"200.08\",\n" +
                "                \"required\": \"true\",\n" +
                "                \"enable\": \"true\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]";
        System.out.println(message);
        System.out.println(getSpecificationsValueJoinByAttributes(message));
    }

    public static String getSpecificationsValueJoinByAttributes(String goodsAttributesDtos) {

        if (goodsAttributesDtos == null) {
            return "";
        }
        List<TpmGoodsAttributesDto> goodsAttributesDtoList = JSONArray.parseArray(goodsAttributesDtos, TpmGoodsAttributesDto.class);
        List<TpmGoodsSpecificationsDto> tpmGoodsSpecificationsDtos = getSpecificationsDto(goodsAttributesDtoList);
        return getSpecificationsValueJoinBySpecifications(tpmGoodsSpecificationsDtos);

    }
    public static String getSpecificationsValueJoinByAttributes(String goodsAttributesDtos,String separator) {

        if (goodsAttributesDtos == null) {
            return "";
        }
        List<TpmGoodsAttributesDto> goodsAttributesDtoList = JSONArray.parseArray(goodsAttributesDtos, TpmGoodsAttributesDto.class);
        List<TpmGoodsSpecificationsDto> tpmGoodsSpecificationsDtos = getSpecificationsDto(goodsAttributesDtoList);
        return getSpecificationsValueJoinBySpecifications(tpmGoodsSpecificationsDtos,separator);

    }

    public static String getSpecificationsValueJoinByAttributes(List<TpmGoodsAttributesDto> goodsAttributesDtos) {
        if (CollectionUtils.isEmpty(goodsAttributesDtos)) {
            return "";
        }
        List<TpmGoodsSpecificationsDto> tpmGoodsSpecificationsDtos = getSpecificationsDto(goodsAttributesDtos);
        return getSpecificationsValueJoinBySpecifications(tpmGoodsSpecificationsDtos);

    }

    public static String getSpecificationsValueJoinBySpecifications(List<TpmGoodsSpecificationsDto> tpmGoodsSpecificationsDtos) {
        if (CollectionUtils.isEmpty(tpmGoodsSpecificationsDtos)) {
            return "";
        }
//        String number = tpmGoodsSpecificationsDtos.stream().filter(t -> Objects.nonNull(t.getNumber()))
//                .map(t -> t.getValue() + "*" + t.getNumber()).collect(Collectors.joining(","));
//        String value = tpmGoodsSpecificationsDtos.stream().filter(t -> Objects.isNull(t.getNumber()))
//                .map(TpmGoodsSpecificationsDto::getValue).collect(Collectors.joining(","));
//        return number+value;
//        return tpmGoodsSpecificationsDtos.stream()
//                    .map(TpmGoodsSpecificationsDto::getValue)
//                    .collect(Collectors.joining(", "));

        return tpmGoodsSpecificationsDtos.stream()
                    .map(t->{
                        if (Objects.nonNull(t.getNumber())){
                            return t.getValue()+"*"+t.getNumber();
                        }else {
                            return t.getValue();
                        }
                    })
                    .collect(Collectors.joining(","));

    }

    public static String getSpecificationsValueJoinBySpecifications(List<TpmGoodsSpecificationsDto> tpmGoodsSpecificationsDtos,String separator) {
        if (CollectionUtils.isEmpty(tpmGoodsSpecificationsDtos)) {
            return "";
        }
        return tpmGoodsSpecificationsDtos.stream()
                .map(TpmGoodsSpecificationsDto::getValue)
                .collect(Collectors.joining(separator));

    }


}
