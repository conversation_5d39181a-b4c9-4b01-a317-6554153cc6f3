package com.pioneer.mall.db.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
public class ThreadContextUtil {
    private static ThreadLocal<List<Integer>> userShopIdsThreadLocal = new ThreadLocal<>();


    public static List<Integer> getUserShopIdList() {
        List<Integer> userShopIdList = userShopIdsThreadLocal.get();
        log.info("[ThreadContextUtil-getUserShopIdsList ={}]", userShopIdList);
        return userShopIdList;
    }

    public static void setUserShopIdList(List<Integer> userShopIdList) {
        log.info("[ThreadContextUtil-setUserShopIdsList ={}]", userShopIdList);
        if (CollectionUtils.isEmpty(userShopIdList)) {
            return;
        }
        userShopIdsThreadLocal.set(userShopIdList);
    }

    public static void clear() {
        userShopIdsThreadLocal.remove();
    }
}
