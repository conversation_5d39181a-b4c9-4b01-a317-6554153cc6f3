<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmAddressMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="province_id" jdbcType="INTEGER" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="area_id" jdbcType="INTEGER" property="areaId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="address_name" jdbcType="VARCHAR" property="addressName" />
    <result column="address_location" jdbcType="VARCHAR" property="addressLocation" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, `name`, user_id, province_id, province_name, city_id, city_name, area_id, area_name, 
    address_detail, address_name, address_location, longitude, latitude, mobile, is_default, 
    business_type, add_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmAddressExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, user_id, province_id, province_name, city_id, city_name, area_id, area_name, 
          address_detail, address_name, address_location, longitude, latitude, mobile, is_default, 
          business_type, add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_address
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_address
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_address
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, user_id, province_id, province_name, city_id, city_name, area_id, area_name, 
          address_detail, address_name, address_location, longitude, latitude, mobile, is_default, 
          business_type, add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_address
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_address
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmAddressExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_address (`name`, user_id, province_id, 
      province_name, city_id, city_name, 
      area_id, area_name, address_detail, 
      address_name, address_location, longitude, 
      latitude, mobile, is_default, 
      business_type, add_time, update_time, 
      deleted)
    values (#{name,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, #{provinceId,jdbcType=INTEGER}, 
      #{provinceName,jdbcType=VARCHAR}, #{cityId,jdbcType=INTEGER}, #{cityName,jdbcType=VARCHAR}, 
      #{areaId,jdbcType=INTEGER}, #{areaName,jdbcType=VARCHAR}, #{addressDetail,jdbcType=VARCHAR}, 
      #{addressName,jdbcType=VARCHAR}, #{addressLocation,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, 
      #{latitude,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{isDefault,jdbcType=BIT}, 
      #{businessType,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="addressDetail != null">
        address_detail,
      </if>
      <if test="addressName != null">
        address_name,
      </if>
      <if test="addressLocation != null">
        address_location,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null">
        #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="addressName != null">
        #{addressName,jdbcType=VARCHAR},
      </if>
      <if test="addressLocation != null">
        #{addressLocation,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmAddressExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_address
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.provinceId != null">
        province_id = #{record.provinceId,jdbcType=INTEGER},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=INTEGER},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.areaId != null">
        area_id = #{record.areaId,jdbcType=INTEGER},
      </if>
      <if test="record.areaName != null">
        area_name = #{record.areaName,jdbcType=VARCHAR},
      </if>
      <if test="record.addressDetail != null">
        address_detail = #{record.addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.addressName != null">
        address_name = #{record.addressName,jdbcType=VARCHAR},
      </if>
      <if test="record.addressLocation != null">
        address_location = #{record.addressLocation,jdbcType=VARCHAR},
      </if>
      <if test="record.longitude != null">
        longitude = #{record.longitude,jdbcType=VARCHAR},
      </if>
      <if test="record.latitude != null">
        latitude = #{record.latitude,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.isDefault != null">
        is_default = #{record.isDefault,jdbcType=BIT},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_address
    set id = #{record.id,jdbcType=INTEGER},
      `name` = #{record.name,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=INTEGER},
      province_id = #{record.provinceId,jdbcType=INTEGER},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      city_id = #{record.cityId,jdbcType=INTEGER},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      area_id = #{record.areaId,jdbcType=INTEGER},
      area_name = #{record.areaName,jdbcType=VARCHAR},
      address_detail = #{record.addressDetail,jdbcType=VARCHAR},
      address_name = #{record.addressName,jdbcType=VARCHAR},
      address_location = #{record.addressLocation,jdbcType=VARCHAR},
      longitude = #{record.longitude,jdbcType=VARCHAR},
      latitude = #{record.latitude,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      is_default = #{record.isDefault,jdbcType=BIT},
      business_type = #{record.businessType,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_address
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null">
        address_detail = #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="addressName != null">
        address_name = #{addressName,jdbcType=VARCHAR},
      </if>
      <if test="addressLocation != null">
        address_location = #{addressLocation,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_address
    set `name` = #{name,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      province_id = #{provinceId,jdbcType=INTEGER},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=INTEGER},
      city_name = #{cityName,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=INTEGER},
      area_name = #{areaName,jdbcType=VARCHAR},
      address_detail = #{addressDetail,jdbcType=VARCHAR},
      address_name = #{addressName,jdbcType=VARCHAR},
      address_location = #{addressLocation,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT},
      business_type = #{businessType,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmAddressExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, user_id, province_id, province_name, city_id, city_name, area_id, area_name, 
          address_detail, address_name, address_location, longitude, latitude, mobile, is_default, 
          business_type, add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_address
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_address set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_address set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>