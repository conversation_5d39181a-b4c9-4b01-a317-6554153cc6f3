<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmAdminMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmAdmin">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="last_login_ip" jdbcType="VARCHAR" property="lastLoginIp" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="role_ids" jdbcType="VARCHAR" property="roleIds" typeHandler="com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler" />
    <result column="shop_ids" jdbcType="VARCHAR" property="shopIds" typeHandler="com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="mail" jdbcType="VARCHAR" property="mail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.roleIdsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.shopIdsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.roleIdsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.shopIdsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, username, `password`, last_login_ip, last_login_time, avatar, add_time, update_time, 
    deleted, role_ids, shop_ids, `desc`, tel, mail
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmAdminExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_admin
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, username, `password`, last_login_ip, last_login_time, avatar, add_time, update_time, 
          deleted, role_ids, shop_ids, `desc`, tel, mail
      </otherwise>
    </choose>
    from tpm_admin
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_admin
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_admin
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, username, `password`, last_login_ip, last_login_time, avatar, add_time, update_time, 
          deleted, role_ids, shop_ids, `desc`, tel, mail
      </otherwise>
    </choose>
    from tpm_admin
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_admin
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmAdminExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_admin
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmAdmin">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_admin (username, `password`, last_login_ip, 
      last_login_time, avatar, add_time, 
      update_time, deleted, role_ids, 
      shop_ids, 
      `desc`, tel, mail)
    values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{lastLoginIp,jdbcType=VARCHAR}, 
      #{lastLoginTime,jdbcType=TIMESTAMP}, #{avatar,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, #{roleIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}, 
      #{shopIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}, 
      #{desc,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, #{mail,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmAdmin">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_admin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        `password`,
      </if>
      <if test="lastLoginIp != null">
        last_login_ip,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="roleIds != null">
        role_ids,
      </if>
      <if test="shopIds != null">
        shop_ids,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="tel != null">
        tel,
      </if>
      <if test="mail != null">
        mail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginIp != null">
        #{lastLoginIp,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="roleIds != null">
        #{roleIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="shopIds != null">
        #{shopIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="mail != null">
        #{mail,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmAdminExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_admin
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_admin
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        `password` = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.lastLoginIp != null">
        last_login_ip = #{record.lastLoginIp,jdbcType=VARCHAR},
      </if>
      <if test="record.lastLoginTime != null">
        last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.avatar != null">
        avatar = #{record.avatar,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.roleIds != null">
        role_ids = #{record.roleIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="record.shopIds != null">
        shop_ids = #{record.shopIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="record.desc != null">
        `desc` = #{record.desc,jdbcType=VARCHAR},
      </if>
      <if test="record.tel != null">
        tel = #{record.tel,jdbcType=VARCHAR},
      </if>
      <if test="record.mail != null">
        mail = #{record.mail,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_admin
    set id = #{record.id,jdbcType=INTEGER},
      username = #{record.username,jdbcType=VARCHAR},
      `password` = #{record.password,jdbcType=VARCHAR},
      last_login_ip = #{record.lastLoginIp,jdbcType=VARCHAR},
      last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
      avatar = #{record.avatar,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      role_ids = #{record.roleIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      shop_ids = #{record.shopIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      `desc` = #{record.desc,jdbcType=VARCHAR},
      tel = #{record.tel,jdbcType=VARCHAR},
      mail = #{record.mail,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmAdmin">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_admin
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginIp != null">
        last_login_ip = #{lastLoginIp,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="roleIds != null">
        role_ids = #{roleIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="shopIds != null">
        shop_ids = #{shopIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        tel = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="mail != null">
        mail = #{mail,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmAdmin">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_admin
    set username = #{username,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      last_login_ip = #{lastLoginIp,jdbcType=VARCHAR},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      avatar = #{avatar,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      role_ids = #{roleIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      shop_ids = #{shopIds,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      `desc` = #{desc,jdbcType=VARCHAR},
      tel = #{tel,jdbcType=VARCHAR},
      mail = #{mail,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmAdminExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_admin
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, username, `password`, last_login_ip, last_login_time, avatar, add_time, update_time, 
          deleted, role_ids, shop_ids, `desc`, tel, mail
      </otherwise>
    </choose>
    from tpm_admin
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_admin set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_admin set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>