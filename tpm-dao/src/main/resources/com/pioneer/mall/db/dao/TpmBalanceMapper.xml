<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmBalanceMapper">
    <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmBalance">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="order_sn" jdbcType="VARCHAR" property="orderSn"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="left_balance" jdbcType="DECIMAL" property="leftBalance"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, user_id, mobile, order_sn, amount, left_balance, remark, `operator`, `status`,
        add_time, update_time, deleted
    </sql>
    <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmBalanceExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from tpm_balance
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <if test="example.distinct">
            distinct
        </if>
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.escapedColumnName}
                </foreach>
            </when>
            <otherwise>
                id, user_id, mobile, order_sn, amount, left_balance, remark, `operator`, `status`,
                add_time, update_time, deleted
            </otherwise>
        </choose>
        from tpm_balance
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from tpm_balance
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from tpm_balance
        where id = #{id,jdbcType=INTEGER}
        and deleted =
        <choose>
            <when test="andLogicalDeleted">
                1
            </when>
            <otherwise>
                0
            </otherwise>
        </choose>
    </select>
    <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.escapedColumnName}
                </foreach>
            </when>
            <otherwise>
                id, user_id, mobile, order_sn, amount, left_balance, remark, `operator`, `status`,
                add_time, update_time, deleted
            </otherwise>
        </choose>
        from tpm_balance
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tpm_balance
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmBalanceExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tpm_balance
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmBalance">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tpm_balance (user_id, mobile, order_sn,
        amount, left_balance, remark,
        `operator`, `status`, add_time,
        update_time, deleted)
        values (#{userId,jdbcType=INTEGER}, #{mobile,jdbcType=VARCHAR}, #{orderSn,jdbcType=VARCHAR},
        #{amount,jdbcType=DECIMAL}, #{leftBalance,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR},
        #{operator,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmBalance">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tpm_balance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="orderSn != null">
                order_sn,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="leftBalance != null">
                left_balance,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="orderSn != null">
                #{orderSn,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="leftBalance != null">
                #{leftBalance,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmBalanceExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from tpm_balance
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_balance
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=INTEGER},
            </if>
            <if test="record.mobile != null">
                mobile = #{record.mobile,jdbcType=VARCHAR},
            </if>
            <if test="record.orderSn != null">
                order_sn = #{record.orderSn,jdbcType=VARCHAR},
            </if>
            <if test="record.amount != null">
                amount = #{record.amount,jdbcType=DECIMAL},
            </if>
            <if test="record.leftBalance != null">
                left_balance = #{record.leftBalance,jdbcType=DECIMAL},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
            <if test="record.operator != null">
                `operator` = #{record.operator,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                `status` = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.addTime != null">
                add_time = #{record.addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.deleted != null">
                deleted = #{record.deleted,jdbcType=BIT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_balance
        set id = #{record.id,jdbcType=INTEGER},
        user_id = #{record.userId,jdbcType=INTEGER},
        mobile = #{record.mobile,jdbcType=VARCHAR},
        order_sn = #{record.orderSn,jdbcType=VARCHAR},
        amount = #{record.amount,jdbcType=DECIMAL},
        left_balance = #{record.leftBalance,jdbcType=DECIMAL},
        remark = #{record.remark,jdbcType=VARCHAR},
        `operator` = #{record.operator,jdbcType=VARCHAR},
        `status` = #{record.status,jdbcType=VARCHAR},
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        deleted = #{record.deleted,jdbcType=BIT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmBalance">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_balance
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="orderSn != null">
                order_sn = #{orderSn,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="leftBalance != null">
                left_balance = #{leftBalance,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmBalance">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_balance
        set user_id = #{userId,jdbcType=INTEGER},
        mobile = #{mobile,jdbcType=VARCHAR},
        order_sn = #{orderSn,jdbcType=VARCHAR},
        amount = #{amount,jdbcType=DECIMAL},
        left_balance = #{leftBalance,jdbcType=DECIMAL},
        remark = #{remark,jdbcType=VARCHAR},
        `operator` = #{operator,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=VARCHAR},
        add_time = #{addTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        deleted = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmBalanceExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <include refid="Base_Column_List"/>
        from tpm_balance
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        limit 1
    </select>
    <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.escapedColumnName}
                </foreach>
            </when>
            <otherwise>
                id, user_id, mobile, order_sn, amount, left_balance, remark, `operator`, `status`,
                add_time, update_time, deleted
            </otherwise>
        </choose>
        from tpm_balance
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
        limit 1
    </select>
    <update id="logicalDeleteByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        update tpm_balance set deleted = 1
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        update tpm_balance set deleted = 1
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="getBalanceListByOrderSn" resultType="com.pioneer.mall.db.domain.TpmBalance">
        select *
        from mt_balance o
        where o.ORDER_SN = #{orderSn}
    </select>
</mapper>