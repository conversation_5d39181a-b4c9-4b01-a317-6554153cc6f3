<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmBrandMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmBrand">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="sort_order" jdbcType="TINYINT" property="sortOrder" />
    <result column="floor_price" jdbcType="DECIMAL" property="floorPrice" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="share_url" jdbcType="VARCHAR" property="shareUrl" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="commpany" jdbcType="VARCHAR" property="commpany" />
    <result column="auto_update_good" jdbcType="BIT" property="autoUpdateGood" />
    <result column="shop_url" jdbcType="VARCHAR" property="shopUrl" />
    <result column="default_category_id" jdbcType="INTEGER" property="defaultCategoryId" />
    <result column="default_pages" jdbcType="INTEGER" property="defaultPages" />
    <result column="add_precent" jdbcType="INTEGER" property="addPrecent" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="longitude" jdbcType="DECIMAL" property="longitude" />
    <result column="latitude" jdbcType="DECIMAL" property="latitude" />
    <result column="fetch_time_rules" jdbcType="VARCHAR" property="fetchTimeRules" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, `name`, `desc`, pic_url, sort_order, floor_price, add_time, update_time, share_url, 
    admin_id, deleted, commpany, auto_update_good, shop_url, default_category_id, default_pages, 
    add_precent, address, longitude, latitude, fetch_time_rules
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmBrandExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, `desc`, pic_url, sort_order, floor_price, add_time, update_time, share_url, 
          admin_id, deleted, commpany, auto_update_good, shop_url, default_category_id, default_pages, 
          add_precent, address, longitude, latitude, fetch_time_rules
      </otherwise>
    </choose>
    from tpm_brand
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_brand
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_brand
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, `desc`, pic_url, sort_order, floor_price, add_time, update_time, share_url, 
          admin_id, deleted, commpany, auto_update_good, shop_url, default_category_id, default_pages, 
          add_precent, address, longitude, latitude, fetch_time_rules
      </otherwise>
    </choose>
    from tpm_brand
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_brand
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmBrandExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmBrand">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_brand (`name`, `desc`, pic_url, 
      sort_order, floor_price, add_time, 
      update_time, share_url, admin_id, 
      deleted, commpany, auto_update_good, 
      shop_url, default_category_id, default_pages, 
      add_precent, address, longitude, 
      latitude, fetch_time_rules)
    values (#{name,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, #{picUrl,jdbcType=VARCHAR}, 
      #{sortOrder,jdbcType=TINYINT}, #{floorPrice,jdbcType=DECIMAL}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{shareUrl,jdbcType=VARCHAR}, #{adminId,jdbcType=INTEGER}, 
      #{deleted,jdbcType=BIT}, #{commpany,jdbcType=VARCHAR}, #{autoUpdateGood,jdbcType=BIT}, 
      #{shopUrl,jdbcType=VARCHAR}, #{defaultCategoryId,jdbcType=INTEGER}, #{defaultPages,jdbcType=INTEGER}, 
      #{addPrecent,jdbcType=INTEGER}, #{address,jdbcType=VARCHAR}, #{longitude,jdbcType=DECIMAL}, 
      #{latitude,jdbcType=DECIMAL}, #{fetchTimeRules,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmBrand">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_brand
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="floorPrice != null">
        floor_price,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="shareUrl != null">
        share_url,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="commpany != null">
        commpany,
      </if>
      <if test="autoUpdateGood != null">
        auto_update_good,
      </if>
      <if test="shopUrl != null">
        shop_url,
      </if>
      <if test="defaultCategoryId != null">
        default_category_id,
      </if>
      <if test="defaultPages != null">
        default_pages,
      </if>
      <if test="addPrecent != null">
        add_precent,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="fetchTimeRules != null">
        fetch_time_rules,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=TINYINT},
      </if>
      <if test="floorPrice != null">
        #{floorPrice,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shareUrl != null">
        #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="commpany != null">
        #{commpany,jdbcType=VARCHAR},
      </if>
      <if test="autoUpdateGood != null">
        #{autoUpdateGood,jdbcType=BIT},
      </if>
      <if test="shopUrl != null">
        #{shopUrl,jdbcType=VARCHAR},
      </if>
      <if test="defaultCategoryId != null">
        #{defaultCategoryId,jdbcType=INTEGER},
      </if>
      <if test="defaultPages != null">
        #{defaultPages,jdbcType=INTEGER},
      </if>
      <if test="addPrecent != null">
        #{addPrecent,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=DECIMAL},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=DECIMAL},
      </if>
      <if test="fetchTimeRules != null">
        #{fetchTimeRules,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmBrandExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_brand
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.desc != null">
        `desc` = #{record.desc,jdbcType=VARCHAR},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.sortOrder != null">
        sort_order = #{record.sortOrder,jdbcType=TINYINT},
      </if>
      <if test="record.floorPrice != null">
        floor_price = #{record.floorPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.shareUrl != null">
        share_url = #{record.shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.adminId != null">
        admin_id = #{record.adminId,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.commpany != null">
        commpany = #{record.commpany,jdbcType=VARCHAR},
      </if>
      <if test="record.autoUpdateGood != null">
        auto_update_good = #{record.autoUpdateGood,jdbcType=BIT},
      </if>
      <if test="record.shopUrl != null">
        shop_url = #{record.shopUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultCategoryId != null">
        default_category_id = #{record.defaultCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.defaultPages != null">
        default_pages = #{record.defaultPages,jdbcType=INTEGER},
      </if>
      <if test="record.addPrecent != null">
        add_precent = #{record.addPrecent,jdbcType=INTEGER},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.longitude != null">
        longitude = #{record.longitude,jdbcType=DECIMAL},
      </if>
      <if test="record.latitude != null">
        latitude = #{record.latitude,jdbcType=DECIMAL},
      </if>
      <if test="record.fetchTimeRules != null">
        fetch_time_rules = #{record.fetchTimeRules,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_brand
    set id = #{record.id,jdbcType=INTEGER},
      `name` = #{record.name,jdbcType=VARCHAR},
      `desc` = #{record.desc,jdbcType=VARCHAR},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      sort_order = #{record.sortOrder,jdbcType=TINYINT},
      floor_price = #{record.floorPrice,jdbcType=DECIMAL},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      share_url = #{record.shareUrl,jdbcType=VARCHAR},
      admin_id = #{record.adminId,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      commpany = #{record.commpany,jdbcType=VARCHAR},
      auto_update_good = #{record.autoUpdateGood,jdbcType=BIT},
      shop_url = #{record.shopUrl,jdbcType=VARCHAR},
      default_category_id = #{record.defaultCategoryId,jdbcType=INTEGER},
      default_pages = #{record.defaultPages,jdbcType=INTEGER},
      add_precent = #{record.addPrecent,jdbcType=INTEGER},
      address = #{record.address,jdbcType=VARCHAR},
      longitude = #{record.longitude,jdbcType=DECIMAL},
      latitude = #{record.latitude,jdbcType=DECIMAL},
      fetch_time_rules = #{record.fetchTimeRules,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmBrand">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_brand
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=TINYINT},
      </if>
      <if test="floorPrice != null">
        floor_price = #{floorPrice,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shareUrl != null">
        share_url = #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="commpany != null">
        commpany = #{commpany,jdbcType=VARCHAR},
      </if>
      <if test="autoUpdateGood != null">
        auto_update_good = #{autoUpdateGood,jdbcType=BIT},
      </if>
      <if test="shopUrl != null">
        shop_url = #{shopUrl,jdbcType=VARCHAR},
      </if>
      <if test="defaultCategoryId != null">
        default_category_id = #{defaultCategoryId,jdbcType=INTEGER},
      </if>
      <if test="defaultPages != null">
        default_pages = #{defaultPages,jdbcType=INTEGER},
      </if>
      <if test="addPrecent != null">
        add_precent = #{addPrecent,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=DECIMAL},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=DECIMAL},
      </if>
      <if test="fetchTimeRules != null">
        fetch_time_rules = #{fetchTimeRules,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmBrand">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_brand
    set `name` = #{name,jdbcType=VARCHAR},
      `desc` = #{desc,jdbcType=VARCHAR},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      sort_order = #{sortOrder,jdbcType=TINYINT},
      floor_price = #{floorPrice,jdbcType=DECIMAL},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      share_url = #{shareUrl,jdbcType=VARCHAR},
      admin_id = #{adminId,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      commpany = #{commpany,jdbcType=VARCHAR},
      auto_update_good = #{autoUpdateGood,jdbcType=BIT},
      shop_url = #{shopUrl,jdbcType=VARCHAR},
      default_category_id = #{defaultCategoryId,jdbcType=INTEGER},
      default_pages = #{defaultPages,jdbcType=INTEGER},
      add_precent = #{addPrecent,jdbcType=INTEGER},
      address = #{address,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=DECIMAL},
      latitude = #{latitude,jdbcType=DECIMAL},
      fetch_time_rules = #{fetchTimeRules,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmBrandExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, `desc`, pic_url, sort_order, floor_price, add_time, update_time, share_url, 
          admin_id, deleted, commpany, auto_update_good, shop_url, default_category_id, default_pages, 
          add_precent, address, longitude, latitude, fetch_time_rules
      </otherwise>
    </choose>
    from tpm_brand
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_brand set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_brand set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>