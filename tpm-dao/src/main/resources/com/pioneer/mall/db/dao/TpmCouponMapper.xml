<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmCouponMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmCoupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="total" jdbcType="INTEGER" property="total" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="min" jdbcType="DECIMAL" property="min" />
    <result column="limit" jdbcType="SMALLINT" property="limit" />
    <result column="type" jdbcType="SMALLINT" property="type" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="goods_type" jdbcType="SMALLINT" property="goodsType" />
    <result column="goods_value" jdbcType="VARCHAR" property="goodsValue" typeHandler="com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="time_type" jdbcType="SMALLINT" property="timeType" />
    <result column="days" jdbcType="SMALLINT" property="days" />
    <result column="start_time" jdbcType="DATE" property="startTime" />
    <result column="end_time" jdbcType="DATE" property="endTime" />
    <result column="sort_order" jdbcType="SMALLINT" property="sortOrder" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.goodsValueCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.goodsValueCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, `name`, `desc`, pic_url, tag, total, discount, `min`, `limit`, `type`, `status`, 
    goods_type, goods_value, code, time_type, `days`, start_time, end_time, sort_order, 
    add_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmCouponExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, `desc`, pic_url, tag, total, discount, `min`, `limit`, `type`, `status`, 
          goods_type, goods_value, code, time_type, `days`, start_time, end_time, sort_order, 
          add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_coupon
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_coupon
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_coupon
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, `desc`, pic_url, tag, total, discount, `min`, `limit`, `type`, `status`, 
          goods_type, goods_value, code, time_type, `days`, start_time, end_time, sort_order, 
          add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_coupon
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_coupon
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmCouponExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmCoupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_coupon (`name`, `desc`, pic_url, 
      tag, total, discount, 
      `min`, `limit`, `type`, 
      `status`, goods_type, goods_value, 
      code, time_type, `days`, 
      start_time, end_time, sort_order, 
      add_time, update_time, deleted
      )
    values (#{name,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, #{picUrl,jdbcType=VARCHAR}, 
      #{tag,jdbcType=VARCHAR}, #{total,jdbcType=INTEGER}, #{discount,jdbcType=DECIMAL}, 
      #{min,jdbcType=DECIMAL}, #{limit,jdbcType=SMALLINT}, #{type,jdbcType=SMALLINT}, 
      #{status,jdbcType=SMALLINT}, #{goodsType,jdbcType=SMALLINT}, #{goodsValue,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler}, 
      #{code,jdbcType=VARCHAR}, #{timeType,jdbcType=SMALLINT}, #{days,jdbcType=SMALLINT}, 
      #{startTime,jdbcType=DATE}, #{endTime,jdbcType=DATE}, #{sortOrder,jdbcType=SMALLINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmCoupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="tag != null">
        tag,
      </if>
      <if test="total != null">
        total,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="min != null">
        `min`,
      </if>
      <if test="limit != null">
        `limit`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="goodsType != null">
        goods_type,
      </if>
      <if test="goodsValue != null">
        goods_value,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="timeType != null">
        time_type,
      </if>
      <if test="days != null">
        `days`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        #{total,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="min != null">
        #{min,jdbcType=DECIMAL},
      </if>
      <if test="limit != null">
        #{limit,jdbcType=SMALLINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=SMALLINT},
      </if>
      <if test="goodsValue != null">
        #{goodsValue,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="timeType != null">
        #{timeType,jdbcType=SMALLINT},
      </if>
      <if test="days != null">
        #{days,jdbcType=SMALLINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=SMALLINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmCouponExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_coupon
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.desc != null">
        `desc` = #{record.desc,jdbcType=VARCHAR},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.tag != null">
        tag = #{record.tag,jdbcType=VARCHAR},
      </if>
      <if test="record.total != null">
        total = #{record.total,jdbcType=INTEGER},
      </if>
      <if test="record.discount != null">
        discount = #{record.discount,jdbcType=DECIMAL},
      </if>
      <if test="record.min != null">
        `min` = #{record.min,jdbcType=DECIMAL},
      </if>
      <if test="record.limit != null">
        `limit` = #{record.limit,jdbcType=SMALLINT},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=SMALLINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.goodsType != null">
        goods_type = #{record.goodsType,jdbcType=SMALLINT},
      </if>
      <if test="record.goodsValue != null">
        goods_value = #{record.goodsValue,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.timeType != null">
        time_type = #{record.timeType,jdbcType=SMALLINT},
      </if>
      <if test="record.days != null">
        `days` = #{record.days,jdbcType=SMALLINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=DATE},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=DATE},
      </if>
      <if test="record.sortOrder != null">
        sort_order = #{record.sortOrder,jdbcType=SMALLINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_coupon
    set id = #{record.id,jdbcType=INTEGER},
      `name` = #{record.name,jdbcType=VARCHAR},
      `desc` = #{record.desc,jdbcType=VARCHAR},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      tag = #{record.tag,jdbcType=VARCHAR},
      total = #{record.total,jdbcType=INTEGER},
      discount = #{record.discount,jdbcType=DECIMAL},
      `min` = #{record.min,jdbcType=DECIMAL},
      `limit` = #{record.limit,jdbcType=SMALLINT},
      `type` = #{record.type,jdbcType=SMALLINT},
      `status` = #{record.status,jdbcType=SMALLINT},
      goods_type = #{record.goodsType,jdbcType=SMALLINT},
      goods_value = #{record.goodsValue,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      code = #{record.code,jdbcType=VARCHAR},
      time_type = #{record.timeType,jdbcType=SMALLINT},
      `days` = #{record.days,jdbcType=SMALLINT},
      start_time = #{record.startTime,jdbcType=DATE},
      end_time = #{record.endTime,jdbcType=DATE},
      sort_order = #{record.sortOrder,jdbcType=SMALLINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmCoupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_coupon
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        total = #{total,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="min != null">
        `min` = #{min,jdbcType=DECIMAL},
      </if>
      <if test="limit != null">
        `limit` = #{limit,jdbcType=SMALLINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=SMALLINT},
      </if>
      <if test="goodsType != null">
        goods_type = #{goodsType,jdbcType=SMALLINT},
      </if>
      <if test="goodsValue != null">
        goods_value = #{goodsValue,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="timeType != null">
        time_type = #{timeType,jdbcType=SMALLINT},
      </if>
      <if test="days != null">
        `days` = #{days,jdbcType=SMALLINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=DATE},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=SMALLINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmCoupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_coupon
    set `name` = #{name,jdbcType=VARCHAR},
      `desc` = #{desc,jdbcType=VARCHAR},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      tag = #{tag,jdbcType=VARCHAR},
      total = #{total,jdbcType=INTEGER},
      discount = #{discount,jdbcType=DECIMAL},
      `min` = #{min,jdbcType=DECIMAL},
      `limit` = #{limit,jdbcType=SMALLINT},
      `type` = #{type,jdbcType=SMALLINT},
      `status` = #{status,jdbcType=SMALLINT},
      goods_type = #{goodsType,jdbcType=SMALLINT},
      goods_value = #{goodsValue,jdbcType=VARCHAR,typeHandler=com.pioneer.mall.db.mybatis.JsonIntegerArrayTypeHandler},
      code = #{code,jdbcType=VARCHAR},
      time_type = #{timeType,jdbcType=SMALLINT},
      `days` = #{days,jdbcType=SMALLINT},
      start_time = #{startTime,jdbcType=DATE},
      end_time = #{endTime,jdbcType=DATE},
      sort_order = #{sortOrder,jdbcType=SMALLINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmCouponExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, `desc`, pic_url, tag, total, discount, `min`, `limit`, `type`, `status`, 
          goods_type, goods_value, code, time_type, `days`, start_time, end_time, sort_order, 
          add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_coupon
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_coupon set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_coupon set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>