<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmDeliveryRecordMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmDeliveryRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tpm_order_id" jdbcType="INTEGER" property="tpmOrderId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="delivery_status" jdbcType="VARCHAR" property="deliveryStatus" />
    <result column="tip_fee" jdbcType="INTEGER" property="tipFee" />
    <result column="freight_fee" jdbcType="INTEGER" property="freightFee" />
    <result column="courier_name" jdbcType="VARCHAR" property="courierName" />
    <result column="courier_phone" jdbcType="VARCHAR" property="courierPhone" />
    <result column="delivery_brand" jdbcType="VARCHAR" property="deliveryBrand" />
    <result column="delivery_distance" jdbcType="VARCHAR" property="deliveryDistance" />
    <result column="delivery_order_no" jdbcType="VARCHAR" property="deliveryOrderNo" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="from" jdbcType="INTEGER" property="from" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tpm_order_id, order_id, platform, delivery_status, tip_fee, freight_fee, courier_name, 
    courier_phone, delivery_brand, delivery_distance, delivery_order_no, reason, `from`, 
    add_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_delivery_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, tpm_order_id, order_id, platform, delivery_status, tip_fee, freight_fee, courier_name, 
          courier_phone, delivery_brand, delivery_distance, delivery_order_no, reason, `from`, 
          add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_delivery_record
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_delivery_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_delivery_record
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, tpm_order_id, order_id, platform, delivery_status, tip_fee, freight_fee, courier_name, 
          courier_phone, delivery_brand, delivery_distance, delivery_order_no, reason, `from`, 
          add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_delivery_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_delivery_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_delivery_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_delivery_record (tpm_order_id, order_id, platform, 
      delivery_status, tip_fee, freight_fee, 
      courier_name, courier_phone, delivery_brand, 
      delivery_distance, delivery_order_no, reason, 
      `from`, add_time, update_time, 
      deleted)
    values (#{tpmOrderId,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, 
      #{deliveryStatus,jdbcType=VARCHAR}, #{tipFee,jdbcType=INTEGER}, #{freightFee,jdbcType=INTEGER}, 
      #{courierName,jdbcType=VARCHAR}, #{courierPhone,jdbcType=VARCHAR}, #{deliveryBrand,jdbcType=VARCHAR}, 
      #{deliveryDistance,jdbcType=VARCHAR}, #{deliveryOrderNo,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{from,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_delivery_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tpmOrderId != null">
        tpm_order_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="deliveryStatus != null">
        delivery_status,
      </if>
      <if test="tipFee != null">
        tip_fee,
      </if>
      <if test="freightFee != null">
        freight_fee,
      </if>
      <if test="courierName != null">
        courier_name,
      </if>
      <if test="courierPhone != null">
        courier_phone,
      </if>
      <if test="deliveryBrand != null">
        delivery_brand,
      </if>
      <if test="deliveryDistance != null">
        delivery_distance,
      </if>
      <if test="deliveryOrderNo != null">
        delivery_order_no,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="from != null">
        `from`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tpmOrderId != null">
        #{tpmOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=VARCHAR},
      </if>
      <if test="tipFee != null">
        #{tipFee,jdbcType=INTEGER},
      </if>
      <if test="freightFee != null">
        #{freightFee,jdbcType=INTEGER},
      </if>
      <if test="courierName != null">
        #{courierName,jdbcType=VARCHAR},
      </if>
      <if test="courierPhone != null">
        #{courierPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryBrand != null">
        #{deliveryBrand,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDistance != null">
        #{deliveryDistance,jdbcType=VARCHAR},
      </if>
      <if test="deliveryOrderNo != null">
        #{deliveryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="from != null">
        #{from,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_delivery_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_delivery_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.tpmOrderId != null">
        tpm_order_id = #{record.tpmOrderId,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryStatus != null">
        delivery_status = #{record.deliveryStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.tipFee != null">
        tip_fee = #{record.tipFee,jdbcType=INTEGER},
      </if>
      <if test="record.freightFee != null">
        freight_fee = #{record.freightFee,jdbcType=INTEGER},
      </if>
      <if test="record.courierName != null">
        courier_name = #{record.courierName,jdbcType=VARCHAR},
      </if>
      <if test="record.courierPhone != null">
        courier_phone = #{record.courierPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryBrand != null">
        delivery_brand = #{record.deliveryBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryDistance != null">
        delivery_distance = #{record.deliveryDistance,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryOrderNo != null">
        delivery_order_no = #{record.deliveryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.from != null">
        `from` = #{record.from,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_delivery_record
    set id = #{record.id,jdbcType=INTEGER},
      tpm_order_id = #{record.tpmOrderId,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=VARCHAR},
      delivery_status = #{record.deliveryStatus,jdbcType=VARCHAR},
      tip_fee = #{record.tipFee,jdbcType=INTEGER},
      freight_fee = #{record.freightFee,jdbcType=INTEGER},
      courier_name = #{record.courierName,jdbcType=VARCHAR},
      courier_phone = #{record.courierPhone,jdbcType=VARCHAR},
      delivery_brand = #{record.deliveryBrand,jdbcType=VARCHAR},
      delivery_distance = #{record.deliveryDistance,jdbcType=VARCHAR},
      delivery_order_no = #{record.deliveryOrderNo,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      `from` = #{record.from,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_delivery_record
    <set>
      <if test="tpmOrderId != null">
        tpm_order_id = #{tpmOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        delivery_status = #{deliveryStatus,jdbcType=VARCHAR},
      </if>
      <if test="tipFee != null">
        tip_fee = #{tipFee,jdbcType=INTEGER},
      </if>
      <if test="freightFee != null">
        freight_fee = #{freightFee,jdbcType=INTEGER},
      </if>
      <if test="courierName != null">
        courier_name = #{courierName,jdbcType=VARCHAR},
      </if>
      <if test="courierPhone != null">
        courier_phone = #{courierPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryBrand != null">
        delivery_brand = #{deliveryBrand,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDistance != null">
        delivery_distance = #{deliveryDistance,jdbcType=VARCHAR},
      </if>
      <if test="deliveryOrderNo != null">
        delivery_order_no = #{deliveryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="from != null">
        `from` = #{from,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_delivery_record
    set tpm_order_id = #{tpmOrderId,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      delivery_status = #{deliveryStatus,jdbcType=VARCHAR},
      tip_fee = #{tipFee,jdbcType=INTEGER},
      freight_fee = #{freightFee,jdbcType=INTEGER},
      courier_name = #{courierName,jdbcType=VARCHAR},
      courier_phone = #{courierPhone,jdbcType=VARCHAR},
      delivery_brand = #{deliveryBrand,jdbcType=VARCHAR},
      delivery_distance = #{deliveryDistance,jdbcType=VARCHAR},
      delivery_order_no = #{deliveryOrderNo,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      `from` = #{from,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmDeliveryRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_delivery_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, tpm_order_id, order_id, platform, delivery_status, tip_fee, freight_fee, courier_name, 
          courier_phone, delivery_brand, delivery_distance, delivery_order_no, reason, `from`, 
          add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_delivery_record
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_delivery_record set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_delivery_record set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>