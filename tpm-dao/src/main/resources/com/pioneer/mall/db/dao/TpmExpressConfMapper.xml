<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmExpressConfMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmExpressConf">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="first_weight" jdbcType="DECIMAL" property="firstWeight" />
    <result column="shipping_fee" jdbcType="DECIMAL" property="shippingFee" />
    <result column="continued_weight" jdbcType="DECIMAL" property="continuedWeight" />
    <result column="continued_fee" jdbcType="DECIMAL" property="continuedFee" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="express_fee_type" jdbcType="VARCHAR" property="expressFeeType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="delivery_area" jdbcType="LONGVARCHAR" property="deliveryArea" />
    <result column="delivery_area_code" jdbcType="LONGVARCHAR" property="deliveryAreaCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, first_weight, shipping_fee, continued_weight, continued_fee, create_by, create_time, 
    update_by, update_time, deleted, express_fee_type
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delivery_area, delivery_area_code
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.pioneer.mall.db.domain.TpmExpressConfExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmExpressConfExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, first_weight, shipping_fee, continued_weight, continued_fee, create_by, create_time, 
          update_by, update_time, deleted, express_fee_type, delivery_area, delivery_area_code
          
      </otherwise>
    </choose>
    from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_express_conf
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_express_conf
    where id = #{id,jdbcType=BIGINT}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, first_weight, shipping_fee, continued_weight, continued_fee, create_by, create_time, 
          update_by, update_time, deleted, express_fee_type, delivery_area, delivery_area_code
          
      </otherwise>
    </choose>
    from tpm_express_conf
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_express_conf
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmExpressConfExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_express_conf (first_weight, shipping_fee, continued_weight, 
      continued_fee, create_by, create_time, 
      update_by, update_time, deleted, 
      express_fee_type, delivery_area, delivery_area_code
      )
    values (#{firstWeight,jdbcType=DECIMAL}, #{shippingFee,jdbcType=DECIMAL}, #{continuedWeight,jdbcType=DECIMAL}, 
      #{continuedFee,jdbcType=DECIMAL}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, 
      #{expressFeeType,jdbcType=VARCHAR}, #{deliveryArea,jdbcType=LONGVARCHAR}, #{deliveryAreaCode,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_express_conf
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="firstWeight != null">
        first_weight,
      </if>
      <if test="shippingFee != null">
        shipping_fee,
      </if>
      <if test="continuedWeight != null">
        continued_weight,
      </if>
      <if test="continuedFee != null">
        continued_fee,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="expressFeeType != null">
        express_fee_type,
      </if>
      <if test="deliveryArea != null">
        delivery_area,
      </if>
      <if test="deliveryAreaCode != null">
        delivery_area_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="firstWeight != null">
        #{firstWeight,jdbcType=DECIMAL},
      </if>
      <if test="shippingFee != null">
        #{shippingFee,jdbcType=DECIMAL},
      </if>
      <if test="continuedWeight != null">
        #{continuedWeight,jdbcType=DECIMAL},
      </if>
      <if test="continuedFee != null">
        #{continuedFee,jdbcType=DECIMAL},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="expressFeeType != null">
        #{expressFeeType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryArea != null">
        #{deliveryArea,jdbcType=LONGVARCHAR},
      </if>
      <if test="deliveryAreaCode != null">
        #{deliveryAreaCode,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmExpressConfExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_express_conf
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.firstWeight != null">
        first_weight = #{record.firstWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.shippingFee != null">
        shipping_fee = #{record.shippingFee,jdbcType=DECIMAL},
      </if>
      <if test="record.continuedWeight != null">
        continued_weight = #{record.continuedWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.continuedFee != null">
        continued_fee = #{record.continuedFee,jdbcType=DECIMAL},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.expressFeeType != null">
        express_fee_type = #{record.expressFeeType,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryArea != null">
        delivery_area = #{record.deliveryArea,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.deliveryAreaCode != null">
        delivery_area_code = #{record.deliveryAreaCode,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_express_conf
    set id = #{record.id,jdbcType=BIGINT},
      first_weight = #{record.firstWeight,jdbcType=DECIMAL},
      shipping_fee = #{record.shippingFee,jdbcType=DECIMAL},
      continued_weight = #{record.continuedWeight,jdbcType=DECIMAL},
      continued_fee = #{record.continuedFee,jdbcType=DECIMAL},
      create_by = #{record.createBy,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      express_fee_type = #{record.expressFeeType,jdbcType=VARCHAR},
      delivery_area = #{record.deliveryArea,jdbcType=LONGVARCHAR},
      delivery_area_code = #{record.deliveryAreaCode,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_express_conf
    set id = #{record.id,jdbcType=BIGINT},
      first_weight = #{record.firstWeight,jdbcType=DECIMAL},
      shipping_fee = #{record.shippingFee,jdbcType=DECIMAL},
      continued_weight = #{record.continuedWeight,jdbcType=DECIMAL},
      continued_fee = #{record.continuedFee,jdbcType=DECIMAL},
      create_by = #{record.createBy,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      express_fee_type = #{record.expressFeeType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_express_conf
    <set>
      <if test="firstWeight != null">
        first_weight = #{firstWeight,jdbcType=DECIMAL},
      </if>
      <if test="shippingFee != null">
        shipping_fee = #{shippingFee,jdbcType=DECIMAL},
      </if>
      <if test="continuedWeight != null">
        continued_weight = #{continuedWeight,jdbcType=DECIMAL},
      </if>
      <if test="continuedFee != null">
        continued_fee = #{continuedFee,jdbcType=DECIMAL},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="expressFeeType != null">
        express_fee_type = #{expressFeeType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryArea != null">
        delivery_area = #{deliveryArea,jdbcType=LONGVARCHAR},
      </if>
      <if test="deliveryAreaCode != null">
        delivery_area_code = #{deliveryAreaCode,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.pioneer.mall.db.domain.TpmExpressConfWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_express_conf
    set first_weight = #{firstWeight,jdbcType=DECIMAL},
      shipping_fee = #{shippingFee,jdbcType=DECIMAL},
      continued_weight = #{continuedWeight,jdbcType=DECIMAL},
      continued_fee = #{continuedFee,jdbcType=DECIMAL},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      express_fee_type = #{expressFeeType,jdbcType=VARCHAR},
      delivery_area = #{deliveryArea,jdbcType=LONGVARCHAR},
      delivery_area_code = #{deliveryAreaCode,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmExpressConf">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_express_conf
    set first_weight = #{firstWeight,jdbcType=DECIMAL},
      shipping_fee = #{shippingFee,jdbcType=DECIMAL},
      continued_weight = #{continuedWeight,jdbcType=DECIMAL},
      continued_fee = #{continuedFee,jdbcType=DECIMAL},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      express_fee_type = #{expressFeeType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmExpressConfExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleWithBLOBs" parameterType="com.pioneer.mall.db.domain.TpmExpressConfExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, first_weight, shipping_fee, continued_weight, continued_fee, create_by, create_time, 
          update_by, update_time, deleted, express_fee_type, delivery_area, delivery_area_code
          
      </otherwise>
    </choose>
    from tpm_express_conf
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_express_conf set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_express_conf set deleted = 1
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>