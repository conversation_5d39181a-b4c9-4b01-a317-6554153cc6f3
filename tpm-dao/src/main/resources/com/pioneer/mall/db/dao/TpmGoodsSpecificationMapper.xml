<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmGoodsSpecificationMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmGoodsSpecification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="goods_id" jdbcType="INTEGER" property="goodsId" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="required" jdbcType="BIT" property="required" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="attribute_id" jdbcType="INTEGER" property="attributeId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, goods_id, specification, `value`, pic_url, add_time, update_time, deleted, price,
    required, `enable`, attribute_id
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecificationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_goods_specification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, goods_id, specification, `value`, pic_url, add_time, update_time, deleted, price,
        required, `enable`, attribute_id
      </otherwise>
    </choose>
    from tpm_goods_specification
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_goods_specification
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_goods_specification
    where id = #{id,jdbcType=INTEGER}
    and deleted =
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, goods_id, specification, `value`, pic_url, add_time, update_time, deleted, price,
        required, `enable`, attribute_id
      </otherwise>
    </choose>
    from tpm_goods_specification
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_goods_specification
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecificationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_goods_specification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_goods_specification (goods_id, specification, `value`,
    pic_url, add_time, update_time,
    deleted, price, required, `enable`,
    attribute_id)
    values (#{goodsId,jdbcType=INTEGER}, #{specification,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR},
    #{picUrl,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
    #{deleted,jdbcType=BIT}, #{price,jdbcType=DECIMAL}, #{required,jdbcType=BIT}, #{enable,jdbcType=BIT},
    #{attributeId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_goods_specification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="value != null">
        `value`,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="required != null">
        required,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="attributeId != null">
        attribute_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="required != null">
        #{required,jdbcType=BIT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="attributeId != null">
        #{attributeId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecificationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_goods_specification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods_specification
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.specification != null">
        specification = #{record.specification,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        `value` = #{record.value,jdbcType=VARCHAR},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.required != null">
        required = #{record.required,jdbcType=BIT},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.attributeId != null">
        attribute_id = #{record.attributeId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods_specification
    set id = #{record.id,jdbcType=INTEGER},
    goods_id = #{record.goodsId,jdbcType=INTEGER},
    specification = #{record.specification,jdbcType=VARCHAR},
    `value` = #{record.value,jdbcType=VARCHAR},
    pic_url = #{record.picUrl,jdbcType=VARCHAR},
    add_time = #{record.addTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    deleted = #{record.deleted,jdbcType=BIT},
    price = #{record.price,jdbcType=DECIMAL},
    required = #{record.required,jdbcType=BIT},
    `enable` = #{record.enable,jdbcType=BIT},
    attribute_id = #{record.attributeId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods_specification
    <set>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="required != null">
        required = #{required,jdbcType=BIT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="attributeId != null">
        attribute_id = #{attributeId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods_specification
    set goods_id = #{goodsId,jdbcType=INTEGER},
    specification = #{specification,jdbcType=VARCHAR},
    `value` = #{value,jdbcType=VARCHAR},
    pic_url = #{picUrl,jdbcType=VARCHAR},
    add_time = #{addTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    deleted = #{deleted,jdbcType=BIT},
    price = #{price,jdbcType=DECIMAL},
    required = #{required,jdbcType=BIT},
    `enable` = #{enable,jdbcType=BIT},
    attribute_id = #{attributeId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmGoodsSpecificationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_goods_specification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, goods_id, specification, `value`, pic_url, add_time, update_time, deleted, price,
        required, `enable`, attribute_id
      </otherwise>
    </choose>
    from tpm_goods_specification
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_goods_specification set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_goods_specification set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>