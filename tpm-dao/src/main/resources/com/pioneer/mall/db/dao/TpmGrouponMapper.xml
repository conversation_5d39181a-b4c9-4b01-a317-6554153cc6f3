<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmGrouponMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmGroupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="groupon_id" jdbcType="INTEGER" property="grouponId" />
    <result column="rules_id" jdbcType="INTEGER" property="rulesId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="creator_user_id" jdbcType="INTEGER" property="creatorUserId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="share_url" jdbcType="VARCHAR" property="shareUrl" />
    <result column="payed" jdbcType="BIT" property="payed" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, groupon_id, rules_id, user_id, creator_user_id, add_time, update_time, 
    share_url, payed, deleted
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmGrouponExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_groupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, order_id, groupon_id, rules_id, user_id, creator_user_id, add_time, update_time, 
          share_url, payed, deleted
      </otherwise>
    </choose>
    from tpm_groupon
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_groupon
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_groupon
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, order_id, groupon_id, rules_id, user_id, creator_user_id, add_time, update_time, 
          share_url, payed, deleted
      </otherwise>
    </choose>
    from tpm_groupon
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_groupon
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmGrouponExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_groupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmGroupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_groupon (order_id, groupon_id, rules_id, 
      user_id, creator_user_id, add_time, 
      update_time, share_url, payed, 
      deleted)
    values (#{orderId,jdbcType=INTEGER}, #{grouponId,jdbcType=INTEGER}, #{rulesId,jdbcType=INTEGER}, 
      #{userId,jdbcType=INTEGER}, #{creatorUserId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{shareUrl,jdbcType=VARCHAR}, #{payed,jdbcType=BIT}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmGroupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_groupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="grouponId != null">
        groupon_id,
      </if>
      <if test="rulesId != null">
        rules_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="creatorUserId != null">
        creator_user_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="shareUrl != null">
        share_url,
      </if>
      <if test="payed != null">
        payed,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="grouponId != null">
        #{grouponId,jdbcType=INTEGER},
      </if>
      <if test="rulesId != null">
        #{rulesId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="creatorUserId != null">
        #{creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shareUrl != null">
        #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="payed != null">
        #{payed,jdbcType=BIT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmGrouponExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_groupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_groupon
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.grouponId != null">
        groupon_id = #{record.grouponId,jdbcType=INTEGER},
      </if>
      <if test="record.rulesId != null">
        rules_id = #{record.rulesId,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.creatorUserId != null">
        creator_user_id = #{record.creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.shareUrl != null">
        share_url = #{record.shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.payed != null">
        payed = #{record.payed,jdbcType=BIT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_groupon
    set id = #{record.id,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=INTEGER},
      groupon_id = #{record.grouponId,jdbcType=INTEGER},
      rules_id = #{record.rulesId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      creator_user_id = #{record.creatorUserId,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      share_url = #{record.shareUrl,jdbcType=VARCHAR},
      payed = #{record.payed,jdbcType=BIT},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmGroupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_groupon
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="grouponId != null">
        groupon_id = #{grouponId,jdbcType=INTEGER},
      </if>
      <if test="rulesId != null">
        rules_id = #{rulesId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="creatorUserId != null">
        creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shareUrl != null">
        share_url = #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="payed != null">
        payed = #{payed,jdbcType=BIT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmGroupon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_groupon
    set order_id = #{orderId,jdbcType=INTEGER},
      groupon_id = #{grouponId,jdbcType=INTEGER},
      rules_id = #{rulesId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      share_url = #{shareUrl,jdbcType=VARCHAR},
      payed = #{payed,jdbcType=BIT},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmGrouponExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_groupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, order_id, groupon_id, rules_id, user_id, creator_user_id, add_time, update_time, 
          share_url, payed, deleted
      </otherwise>
    </choose>
    from tpm_groupon
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_groupon set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_groupon set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>