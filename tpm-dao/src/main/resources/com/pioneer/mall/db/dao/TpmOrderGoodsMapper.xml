<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmOrderGoodsMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmOrderGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="brand_id" jdbcType="INTEGER" property="brandId" />
    <result column="goods_id" jdbcType="INTEGER" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_sn" jdbcType="VARCHAR" property="goodsSn" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="number" jdbcType="SMALLINT" property="number" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications"/>
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="comment" jdbcType="INTEGER" property="comment" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="refund_id" jdbcType="INTEGER" property="refundId" />
    <result column="settlement_money" jdbcType="DECIMAL" property="settlementMoney" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.specificationsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition}
                  #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition}
                  #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler} and
                  #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.specificationsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition}
                  #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition}
                  #{criterion.value,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler} and
                  #{criterion.secondValue,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, brand_id, goods_id, goods_name, goods_sn, product_id, `number`, price,
    specifications, pic_url, remark, `comment`, add_time, update_time, deleted, refund_id, 
    settlement_money
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderGoodsExample"
          resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, order_id, brand_id, goods_id, goods_name, goods_sn, product_id, `number`, price,
        specifications, pic_url, remark, `comment`, add_time, update_time, deleted, refund_id,
          settlement_money
      </otherwise>
    </choose>
    from tpm_order_goods
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_order_goods
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_order_goods
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, order_id, brand_id, goods_id, goods_name, goods_sn, product_id, `number`, price,
        specifications, pic_url, remark, `comment`, add_time, update_time, deleted, refund_id,
          settlement_money
      </otherwise>
    </choose>
    from tpm_order_goods
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_order_goods
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderGoodsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmOrderGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_order_goods (order_id, brand_id, goods_id, 
      goods_name, goods_sn, product_id,
    `number`, price, specifications,
    pic_url, remark, `comment`,
    add_time, update_time, deleted,
    refund_id, settlement_money)
    values (#{orderId,jdbcType=INTEGER}, #{brandId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER},
    #{goodsName,jdbcType=VARCHAR}, #{goodsSn,jdbcType=VARCHAR}, #{productId,jdbcType=INTEGER},
    #{number,jdbcType=SMALLINT}, #{price,jdbcType=DECIMAL},
    #{specifications,jdbcType=VARCHAR},
    #{picUrl,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{comment,jdbcType=INTEGER},
    #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT},
    #{refundId,jdbcType=INTEGER}, #{settlementMoney,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmOrderGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_order_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsSn != null">
        goods_sn,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="refundId != null">
        refund_id,
      </if>
      <if test="settlementMoney != null">
        settlement_money,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsSn != null">
        #{goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        #{number,jdbcType=SMALLINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="refundId != null">
        #{refundId,jdbcType=INTEGER},
      </if>
      <if test="settlementMoney != null">
        #{settlementMoney,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderGoodsExample"
          resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order_goods
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsSn != null">
        goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=INTEGER},
      </if>
      <if test="record.number != null">
        `number` = #{record.number,jdbcType=SMALLINT},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.specifications != null">
        specifications =
        #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.refundId != null">
        refund_id = #{record.refundId,jdbcType=INTEGER},
      </if>
      <if test="record.settlementMoney != null">
        settlement_money = #{record.settlementMoney,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order_goods
    set id = #{record.id,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=INTEGER},
      brand_id = #{record.brandId,jdbcType=INTEGER},
      goods_id = #{record.goodsId,jdbcType=INTEGER},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=INTEGER},
      `number` = #{record.number,jdbcType=SMALLINT},
      price = #{record.price,jdbcType=DECIMAL},
    specifications =
    #{record.specifications,jdbcType=VARCHAR},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
    remark = #{record.remark,jdbcType=VARCHAR},
      `comment` = #{record.comment,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      refund_id = #{record.refundId,jdbcType=INTEGER},
      settlement_money = #{record.settlementMoney,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmOrderGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order_goods
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsSn != null">
        goods_sn = #{goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=SMALLINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="specifications != null">
        specifications =
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="refundId != null">
        refund_id = #{refundId,jdbcType=INTEGER},
      </if>
      <if test="settlementMoney != null">
        settlement_money = #{settlementMoney,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmOrderGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order_goods
    set order_id = #{orderId,jdbcType=INTEGER},
      brand_id = #{brandId,jdbcType=INTEGER},
      goods_id = #{goodsId,jdbcType=INTEGER},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_sn = #{goodsSn,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      `number` = #{number,jdbcType=SMALLINT},
      price = #{price,jdbcType=DECIMAL},
    specifications =
    #{specifications,jdbcType=VARCHAR},
      pic_url = #{picUrl,jdbcType=VARCHAR},
    remark = #{remark,jdbcType=VARCHAR},
      `comment` = #{comment,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      refund_id = #{refundId,jdbcType=INTEGER},
      settlement_money = #{settlementMoney,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderGoodsExample"
          resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, order_id, brand_id, goods_id, goods_name, goods_sn, product_id, `number`, price,
        specifications, pic_url, remark, `comment`, add_time, update_time, deleted, refund_id,
          settlement_money
      </otherwise>
    </choose>
    from tpm_order_goods
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_order_goods set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_order_goods set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>