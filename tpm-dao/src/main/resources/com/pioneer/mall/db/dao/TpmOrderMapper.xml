<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmOrderMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="shop_id" jdbcType="INTEGER" property="shopId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="order_status" jdbcType="SMALLINT" property="orderStatus" />
    <result column="meal_code" jdbcType="VARCHAR" property="mealCode" />
    <result column="meal_qr_code" jdbcType="VARCHAR" property="mealQrCode" />
    <result column="meal_pickup_status" jdbcType="BIT" property="mealPickupStatus" />
    <result column="meal_pickup_time" jdbcType="TIMESTAMP" property="mealPickupTime" />
    <result column="consignee" jdbcType="VARCHAR" property="consignee" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="goods_price" jdbcType="DECIMAL" property="goodsPrice" />
    <result column="freight_price" jdbcType="DECIMAL" property="freightPrice" />
    <result column="packing_fee" jdbcType="DECIMAL" property="packingFee" />
    <result column="coupon_price" jdbcType="DECIMAL" property="couponPrice" />
    <result column="integral_price" jdbcType="DECIMAL" property="integralPrice" />
    <result column="groupon_price" jdbcType="DECIMAL" property="grouponPrice" />
    <result column="order_price" jdbcType="DECIMAL" property="orderPrice" />
    <result column="actual_price" jdbcType="DECIMAL" property="actualPrice" />
    <result column="pay_id" jdbcType="VARCHAR" property="payId" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="ship_sn" jdbcType="VARCHAR" property="shipSn" />
    <result column="ship_channel" jdbcType="VARCHAR" property="shipChannel" />
    <result column="ship_time" jdbcType="TIMESTAMP" property="shipTime" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="comments" jdbcType="SMALLINT" property="comments" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="settlement_money" jdbcType="DECIMAL" property="settlementMoney" />
    <result column="settlement_status" jdbcType="BIT" property="settlementStatus" />
    <result column="freight_type" jdbcType="TINYINT" property="freightType" />
    <result column="share_user_id" jdbcType="INTEGER" property="shareUserId" />
    <result column="fetch_code" jdbcType="VARCHAR" property="fetchCode" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="gift_send_time" jdbcType="TIMESTAMP" property="giftSendTime" />
    <result column="gift_receive_time" jdbcType="TIMESTAMP" property="giftReceiveTime" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="refund_status" jdbcType="VARCHAR" property="refundStatus" />
    <result column="lng_and_Lat" jdbcType="VARCHAR" property="lngAndLat" />
    <result column="delivery_status" jdbcType="VARCHAR" property="deliveryStatus" />
    <result column="delivery_order_id" jdbcType="VARCHAR" property="deliveryOrderId" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, shop_id, user_id, order_sn, order_status, meal_code, meal_qr_code, meal_pickup_status, 
    meal_pickup_time, consignee, mobile, address, message, goods_price, freight_price, 
    packing_fee, coupon_price, integral_price, groupon_price, order_price, actual_price, 
    pay_id, pay_time, ship_sn, ship_channel, ship_time, confirm_time, comments, end_time, 
    add_time, update_time, deleted, settlement_money, settlement_status, freight_type, 
    share_user_id, fetch_code, create_user_id, gift_send_time, gift_receive_time, pay_type, 
    refund_status, lng_and_Lat, delivery_status, delivery_order_id, business_type
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, shop_id, user_id, order_sn, order_status, meal_code, meal_qr_code, meal_pickup_status, 
          meal_pickup_time, consignee, mobile, address, message, goods_price, freight_price, 
          packing_fee, coupon_price, integral_price, groupon_price, order_price, actual_price, 
          pay_id, pay_time, ship_sn, ship_channel, ship_time, confirm_time, comments, end_time, 
          add_time, update_time, deleted, settlement_money, settlement_status, freight_type, 
          share_user_id, fetch_code, create_user_id, gift_send_time, gift_receive_time, pay_type, 
          refund_status, lng_and_Lat, delivery_status, delivery_order_id, business_type
      </otherwise>
    </choose>
    from tpm_order
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_order
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, shop_id, user_id, order_sn, order_status, meal_code, meal_qr_code, meal_pickup_status, 
          meal_pickup_time, consignee, mobile, address, message, goods_price, freight_price, 
          packing_fee, coupon_price, integral_price, groupon_price, order_price, actual_price, 
          pay_id, pay_time, ship_sn, ship_channel, ship_time, confirm_time, comments, end_time, 
          add_time, update_time, deleted, settlement_money, settlement_status, freight_type, 
          share_user_id, fetch_code, create_user_id, gift_send_time, gift_receive_time, pay_type, 
          refund_status, lng_and_Lat, delivery_status, delivery_order_id, business_type
      </otherwise>
    </choose>
    from tpm_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_order (shop_id, user_id, order_sn, 
      order_status, meal_code, meal_qr_code, 
      meal_pickup_status, meal_pickup_time, consignee, 
      mobile, address, message, 
      goods_price, freight_price, packing_fee, 
      coupon_price, integral_price, groupon_price, 
      order_price, actual_price, pay_id, 
      pay_time, ship_sn, ship_channel, 
      ship_time, confirm_time, comments, 
      end_time, add_time, update_time, 
      deleted, settlement_money, settlement_status, 
      freight_type, share_user_id, fetch_code, 
      create_user_id, gift_send_time, gift_receive_time, 
      pay_type, refund_status, lng_and_Lat, 
      delivery_status, delivery_order_id, business_type
      )
    values (#{shopId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{orderSn,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=SMALLINT}, #{mealCode,jdbcType=VARCHAR}, #{mealQrCode,jdbcType=VARCHAR}, 
      #{mealPickupStatus,jdbcType=BIT}, #{mealPickupTime,jdbcType=TIMESTAMP}, #{consignee,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR}, 
      #{goodsPrice,jdbcType=DECIMAL}, #{freightPrice,jdbcType=DECIMAL}, #{packingFee,jdbcType=DECIMAL}, 
      #{couponPrice,jdbcType=DECIMAL}, #{integralPrice,jdbcType=DECIMAL}, #{grouponPrice,jdbcType=DECIMAL}, 
      #{orderPrice,jdbcType=DECIMAL}, #{actualPrice,jdbcType=DECIMAL}, #{payId,jdbcType=VARCHAR}, 
      #{payTime,jdbcType=TIMESTAMP}, #{shipSn,jdbcType=VARCHAR}, #{shipChannel,jdbcType=VARCHAR}, 
      #{shipTime,jdbcType=TIMESTAMP}, #{confirmTime,jdbcType=TIMESTAMP}, #{comments,jdbcType=SMALLINT}, 
      #{endTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT}, #{settlementMoney,jdbcType=DECIMAL}, #{settlementStatus,jdbcType=BIT}, 
      #{freightType,jdbcType=TINYINT}, #{shareUserId,jdbcType=INTEGER}, #{fetchCode,jdbcType=VARCHAR}, 
      #{createUserId,jdbcType=INTEGER}, #{giftSendTime,jdbcType=TIMESTAMP}, #{giftReceiveTime,jdbcType=TIMESTAMP}, 
      #{payType,jdbcType=VARCHAR}, #{refundStatus,jdbcType=VARCHAR}, #{lngAndLat,jdbcType=VARCHAR}, 
      #{deliveryStatus,jdbcType=VARCHAR}, #{deliveryOrderId,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="orderSn != null">
        order_sn,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="mealCode != null">
        meal_code,
      </if>
      <if test="mealQrCode != null">
        meal_qr_code,
      </if>
      <if test="mealPickupStatus != null">
        meal_pickup_status,
      </if>
      <if test="mealPickupTime != null">
        meal_pickup_time,
      </if>
      <if test="consignee != null">
        consignee,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="goodsPrice != null">
        goods_price,
      </if>
      <if test="freightPrice != null">
        freight_price,
      </if>
      <if test="packingFee != null">
        packing_fee,
      </if>
      <if test="couponPrice != null">
        coupon_price,
      </if>
      <if test="integralPrice != null">
        integral_price,
      </if>
      <if test="grouponPrice != null">
        groupon_price,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="actualPrice != null">
        actual_price,
      </if>
      <if test="payId != null">
        pay_id,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="shipSn != null">
        ship_sn,
      </if>
      <if test="shipChannel != null">
        ship_channel,
      </if>
      <if test="shipTime != null">
        ship_time,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="settlementMoney != null">
        settlement_money,
      </if>
      <if test="settlementStatus != null">
        settlement_status,
      </if>
      <if test="freightType != null">
        freight_type,
      </if>
      <if test="shareUserId != null">
        share_user_id,
      </if>
      <if test="fetchCode != null">
        fetch_code,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="giftSendTime != null">
        gift_send_time,
      </if>
      <if test="giftReceiveTime != null">
        gift_receive_time,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="lngAndLat != null">
        lng_and_Lat,
      </if>
      <if test="deliveryStatus != null">
        delivery_status,
      </if>
      <if test="deliveryOrderId != null">
        delivery_order_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="orderSn != null">
        #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="mealCode != null">
        #{mealCode,jdbcType=VARCHAR},
      </if>
      <if test="mealQrCode != null">
        #{mealQrCode,jdbcType=VARCHAR},
      </if>
      <if test="mealPickupStatus != null">
        #{mealPickupStatus,jdbcType=BIT},
      </if>
      <if test="mealPickupTime != null">
        #{mealPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consignee != null">
        #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="goodsPrice != null">
        #{goodsPrice,jdbcType=DECIMAL},
      </if>
      <if test="freightPrice != null">
        #{freightPrice,jdbcType=DECIMAL},
      </if>
      <if test="packingFee != null">
        #{packingFee,jdbcType=DECIMAL},
      </if>
      <if test="couponPrice != null">
        #{couponPrice,jdbcType=DECIMAL},
      </if>
      <if test="integralPrice != null">
        #{integralPrice,jdbcType=DECIMAL},
      </if>
      <if test="grouponPrice != null">
        #{grouponPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="actualPrice != null">
        #{actualPrice,jdbcType=DECIMAL},
      </if>
      <if test="payId != null">
        #{payId,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shipSn != null">
        #{shipSn,jdbcType=VARCHAR},
      </if>
      <if test="shipChannel != null">
        #{shipChannel,jdbcType=VARCHAR},
      </if>
      <if test="shipTime != null">
        #{shipTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=SMALLINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="settlementMoney != null">
        #{settlementMoney,jdbcType=DECIMAL},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=BIT},
      </if>
      <if test="freightType != null">
        #{freightType,jdbcType=TINYINT},
      </if>
      <if test="shareUserId != null">
        #{shareUserId,jdbcType=INTEGER},
      </if>
      <if test="fetchCode != null">
        #{fetchCode,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="giftSendTime != null">
        #{giftSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="giftReceiveTime != null">
        #{giftReceiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="lngAndLat != null">
        #{lngAndLat,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=VARCHAR},
      </if>
      <if test="deliveryOrderId != null">
        #{deliveryOrderId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.orderSn != null">
        order_sn = #{record.orderSn,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.mealCode != null">
        meal_code = #{record.mealCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mealQrCode != null">
        meal_qr_code = #{record.mealQrCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mealPickupStatus != null">
        meal_pickup_status = #{record.mealPickupStatus,jdbcType=BIT},
      </if>
      <if test="record.mealPickupTime != null">
        meal_pickup_time = #{record.mealPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.consignee != null">
        consignee = #{record.consignee,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsPrice != null">
        goods_price = #{record.goodsPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.freightPrice != null">
        freight_price = #{record.freightPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.packingFee != null">
        packing_fee = #{record.packingFee,jdbcType=DECIMAL},
      </if>
      <if test="record.couponPrice != null">
        coupon_price = #{record.couponPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.integralPrice != null">
        integral_price = #{record.integralPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.grouponPrice != null">
        groupon_price = #{record.grouponPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.orderPrice != null">
        order_price = #{record.orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.actualPrice != null">
        actual_price = #{record.actualPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.payId != null">
        pay_id = #{record.payId,jdbcType=VARCHAR},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.shipSn != null">
        ship_sn = #{record.shipSn,jdbcType=VARCHAR},
      </if>
      <if test="record.shipChannel != null">
        ship_channel = #{record.shipChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.shipTime != null">
        ship_time = #{record.shipTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmTime != null">
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.comments != null">
        comments = #{record.comments,jdbcType=SMALLINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.settlementMoney != null">
        settlement_money = #{record.settlementMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.settlementStatus != null">
        settlement_status = #{record.settlementStatus,jdbcType=BIT},
      </if>
      <if test="record.freightType != null">
        freight_type = #{record.freightType,jdbcType=TINYINT},
      </if>
      <if test="record.shareUserId != null">
        share_user_id = #{record.shareUserId,jdbcType=INTEGER},
      </if>
      <if test="record.fetchCode != null">
        fetch_code = #{record.fetchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=INTEGER},
      </if>
      <if test="record.giftSendTime != null">
        gift_send_time = #{record.giftSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.giftReceiveTime != null">
        gift_receive_time = #{record.giftReceiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=VARCHAR},
      </if>
      <if test="record.refundStatus != null">
        refund_status = #{record.refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lngAndLat != null">
        lng_and_Lat = #{record.lngAndLat,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryStatus != null">
        delivery_status = #{record.deliveryStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryOrderId != null">
        delivery_order_id = #{record.deliveryOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order
    set id = #{record.id,jdbcType=INTEGER},
      shop_id = #{record.shopId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      order_sn = #{record.orderSn,jdbcType=VARCHAR},
      order_status = #{record.orderStatus,jdbcType=SMALLINT},
      meal_code = #{record.mealCode,jdbcType=VARCHAR},
      meal_qr_code = #{record.mealQrCode,jdbcType=VARCHAR},
      meal_pickup_status = #{record.mealPickupStatus,jdbcType=BIT},
      meal_pickup_time = #{record.mealPickupTime,jdbcType=TIMESTAMP},
      consignee = #{record.consignee,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      message = #{record.message,jdbcType=VARCHAR},
      goods_price = #{record.goodsPrice,jdbcType=DECIMAL},
      freight_price = #{record.freightPrice,jdbcType=DECIMAL},
      packing_fee = #{record.packingFee,jdbcType=DECIMAL},
      coupon_price = #{record.couponPrice,jdbcType=DECIMAL},
      integral_price = #{record.integralPrice,jdbcType=DECIMAL},
      groupon_price = #{record.grouponPrice,jdbcType=DECIMAL},
      order_price = #{record.orderPrice,jdbcType=DECIMAL},
      actual_price = #{record.actualPrice,jdbcType=DECIMAL},
      pay_id = #{record.payId,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      ship_sn = #{record.shipSn,jdbcType=VARCHAR},
      ship_channel = #{record.shipChannel,jdbcType=VARCHAR},
      ship_time = #{record.shipTime,jdbcType=TIMESTAMP},
      confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      comments = #{record.comments,jdbcType=SMALLINT},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      settlement_money = #{record.settlementMoney,jdbcType=DECIMAL},
      settlement_status = #{record.settlementStatus,jdbcType=BIT},
      freight_type = #{record.freightType,jdbcType=TINYINT},
      share_user_id = #{record.shareUserId,jdbcType=INTEGER},
      fetch_code = #{record.fetchCode,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=INTEGER},
      gift_send_time = #{record.giftSendTime,jdbcType=TIMESTAMP},
      gift_receive_time = #{record.giftReceiveTime,jdbcType=TIMESTAMP},
      pay_type = #{record.payType,jdbcType=VARCHAR},
      refund_status = #{record.refundStatus,jdbcType=VARCHAR},
      lng_and_Lat = #{record.lngAndLat,jdbcType=VARCHAR},
      delivery_status = #{record.deliveryStatus,jdbcType=VARCHAR},
      delivery_order_id = #{record.deliveryOrderId,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="orderSn != null">
        order_sn = #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="mealCode != null">
        meal_code = #{mealCode,jdbcType=VARCHAR},
      </if>
      <if test="mealQrCode != null">
        meal_qr_code = #{mealQrCode,jdbcType=VARCHAR},
      </if>
      <if test="mealPickupStatus != null">
        meal_pickup_status = #{mealPickupStatus,jdbcType=BIT},
      </if>
      <if test="mealPickupTime != null">
        meal_pickup_time = #{mealPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consignee != null">
        consignee = #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="goodsPrice != null">
        goods_price = #{goodsPrice,jdbcType=DECIMAL},
      </if>
      <if test="freightPrice != null">
        freight_price = #{freightPrice,jdbcType=DECIMAL},
      </if>
      <if test="packingFee != null">
        packing_fee = #{packingFee,jdbcType=DECIMAL},
      </if>
      <if test="couponPrice != null">
        coupon_price = #{couponPrice,jdbcType=DECIMAL},
      </if>
      <if test="integralPrice != null">
        integral_price = #{integralPrice,jdbcType=DECIMAL},
      </if>
      <if test="grouponPrice != null">
        groupon_price = #{grouponPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="actualPrice != null">
        actual_price = #{actualPrice,jdbcType=DECIMAL},
      </if>
      <if test="payId != null">
        pay_id = #{payId,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shipSn != null">
        ship_sn = #{shipSn,jdbcType=VARCHAR},
      </if>
      <if test="shipChannel != null">
        ship_channel = #{shipChannel,jdbcType=VARCHAR},
      </if>
      <if test="shipTime != null">
        ship_time = #{shipTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=SMALLINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="settlementMoney != null">
        settlement_money = #{settlementMoney,jdbcType=DECIMAL},
      </if>
      <if test="settlementStatus != null">
        settlement_status = #{settlementStatus,jdbcType=BIT},
      </if>
      <if test="freightType != null">
        freight_type = #{freightType,jdbcType=TINYINT},
      </if>
      <if test="shareUserId != null">
        share_user_id = #{shareUserId,jdbcType=INTEGER},
      </if>
      <if test="fetchCode != null">
        fetch_code = #{fetchCode,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="giftSendTime != null">
        gift_send_time = #{giftSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="giftReceiveTime != null">
        gift_receive_time = #{giftReceiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="lngAndLat != null">
        lng_and_Lat = #{lngAndLat,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        delivery_status = #{deliveryStatus,jdbcType=VARCHAR},
      </if>
      <if test="deliveryOrderId != null">
        delivery_order_id = #{deliveryOrderId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_order
    set shop_id = #{shopId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      order_sn = #{orderSn,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=SMALLINT},
      meal_code = #{mealCode,jdbcType=VARCHAR},
      meal_qr_code = #{mealQrCode,jdbcType=VARCHAR},
      meal_pickup_status = #{mealPickupStatus,jdbcType=BIT},
      meal_pickup_time = #{mealPickupTime,jdbcType=TIMESTAMP},
      consignee = #{consignee,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      goods_price = #{goodsPrice,jdbcType=DECIMAL},
      freight_price = #{freightPrice,jdbcType=DECIMAL},
      packing_fee = #{packingFee,jdbcType=DECIMAL},
      coupon_price = #{couponPrice,jdbcType=DECIMAL},
      integral_price = #{integralPrice,jdbcType=DECIMAL},
      groupon_price = #{grouponPrice,jdbcType=DECIMAL},
      order_price = #{orderPrice,jdbcType=DECIMAL},
      actual_price = #{actualPrice,jdbcType=DECIMAL},
      pay_id = #{payId,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      ship_sn = #{shipSn,jdbcType=VARCHAR},
      ship_channel = #{shipChannel,jdbcType=VARCHAR},
      ship_time = #{shipTime,jdbcType=TIMESTAMP},
      confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      comments = #{comments,jdbcType=SMALLINT},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      settlement_money = #{settlementMoney,jdbcType=DECIMAL},
      settlement_status = #{settlementStatus,jdbcType=BIT},
      freight_type = #{freightType,jdbcType=TINYINT},
      share_user_id = #{shareUserId,jdbcType=INTEGER},
      fetch_code = #{fetchCode,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=INTEGER},
      gift_send_time = #{giftSendTime,jdbcType=TIMESTAMP},
      gift_receive_time = #{giftReceiveTime,jdbcType=TIMESTAMP},
      pay_type = #{payType,jdbcType=VARCHAR},
      refund_status = #{refundStatus,jdbcType=VARCHAR},
      lng_and_Lat = #{lngAndLat,jdbcType=VARCHAR},
      delivery_status = #{deliveryStatus,jdbcType=VARCHAR},
      delivery_order_id = #{deliveryOrderId,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmOrderExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, shop_id, user_id, order_sn, order_status, meal_code, meal_qr_code, meal_pickup_status, 
          meal_pickup_time, consignee, mobile, address, message, goods_price, freight_price, 
          packing_fee, coupon_price, integral_price, groupon_price, order_price, actual_price, 
          pay_id, pay_time, ship_sn, ship_channel, ship_time, confirm_time, comments, end_time, 
          add_time, update_time, deleted, settlement_money, settlement_status, freight_type, 
          share_user_id, fetch_code, create_user_id, gift_send_time, gift_receive_time, pay_type, 
          refund_status, lng_and_Lat, delivery_status, delivery_order_id, business_type
      </otherwise>
    </choose>
    from tpm_order
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_order set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_order set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>