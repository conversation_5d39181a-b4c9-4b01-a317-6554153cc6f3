<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmRechargeConfigMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmRechargeConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="subtitle" jdbcType="VARCHAR" property="subtitle" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="give_amount" jdbcType="DECIMAL" property="giveAmount" />
    <result column="give_point" jdbcType="DECIMAL" property="givePoint" />
    <result column="picurl" jdbcType="VARCHAR" property="picurl" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, title, subtitle, amount, give_amount, give_point, picurl, sort, `status`, add_time, 
    update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_recharge_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, title, subtitle, amount, give_amount, give_point, picurl, sort, `status`, add_time, 
          update_time, deleted
      </otherwise>
    </choose>
    from tpm_recharge_config
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_recharge_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_recharge_config
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, title, subtitle, amount, give_amount, give_point, picurl, sort, `status`, add_time, 
          update_time, deleted
      </otherwise>
    </choose>
    from tpm_recharge_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_recharge_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_recharge_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_recharge_config (title, subtitle, amount, 
      give_amount, give_point, picurl, 
      sort, `status`, add_time, 
      update_time, deleted)
    values (#{title,jdbcType=VARCHAR}, #{subtitle,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{giveAmount,jdbcType=DECIMAL}, #{givePoint,jdbcType=DECIMAL}, #{picurl,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_recharge_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="title != null">
        title,
      </if>
      <if test="subtitle != null">
        subtitle,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="giveAmount != null">
        give_amount,
      </if>
      <if test="givePoint != null">
        give_point,
      </if>
      <if test="picurl != null">
        picurl,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="subtitle != null">
        #{subtitle,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="giveAmount != null">
        #{giveAmount,jdbcType=DECIMAL},
      </if>
      <if test="givePoint != null">
        #{givePoint,jdbcType=DECIMAL},
      </if>
      <if test="picurl != null">
        #{picurl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_recharge_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_recharge_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.subtitle != null">
        subtitle = #{record.subtitle,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.giveAmount != null">
        give_amount = #{record.giveAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.givePoint != null">
        give_point = #{record.givePoint,jdbcType=DECIMAL},
      </if>
      <if test="record.picurl != null">
        picurl = #{record.picurl,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_recharge_config
    set id = #{record.id,jdbcType=INTEGER},
      title = #{record.title,jdbcType=VARCHAR},
      subtitle = #{record.subtitle,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DECIMAL},
      give_amount = #{record.giveAmount,jdbcType=DECIMAL},
      give_point = #{record.givePoint,jdbcType=DECIMAL},
      picurl = #{record.picurl,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_recharge_config
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="subtitle != null">
        subtitle = #{subtitle,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="giveAmount != null">
        give_amount = #{giveAmount,jdbcType=DECIMAL},
      </if>
      <if test="givePoint != null">
        give_point = #{givePoint,jdbcType=DECIMAL},
      </if>
      <if test="picurl != null">
        picurl = #{picurl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_recharge_config
    set title = #{title,jdbcType=VARCHAR},
      subtitle = #{subtitle,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      give_amount = #{giveAmount,jdbcType=DECIMAL},
      give_point = #{givePoint,jdbcType=DECIMAL},
      picurl = #{picurl,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmRechargeConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_recharge_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, title, subtitle, amount, give_amount, give_point, picurl, sort, `status`, add_time, 
          update_time, deleted
      </otherwise>
    </choose>
    from tpm_recharge_config
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_recharge_config set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_recharge_config set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>