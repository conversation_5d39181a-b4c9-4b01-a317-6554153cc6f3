<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmUserMapper">
    <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmUser">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="gender" jdbcType="TINYINT" property="gender"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime"/>
        <result column="last_login_ip" jdbcType="VARCHAR" property="lastLoginIp"/>
        <result column="user_level" jdbcType="TINYINT" property="userLevel"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="weixin_openid" jdbcType="VARCHAR" property="weixinOpenid"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="points" jdbcType="DECIMAL" property="points"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="membership_level" jdbcType="VARCHAR" property="membershipLevel"/>
        <result column="membership_id" jdbcType="INTEGER" property="membershipId"/>
        <result column="membership_code" jdbcType="VARCHAR" property="membershipCode"/>
        <result column="accumulative_points" jdbcType="DECIMAL" property="accumulativePoints"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="share_user_id" jdbcType="INTEGER" property="shareUserId"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, username, `password`, gender, birthday, last_login_time, last_login_ip, user_level,
        nickname, mobile, avatar, weixin_openid, `status`, points, balance, membership_level,
        membership_id, membership_code, accumulative_points, add_time, update_time, deleted,
        share_user_id
    </sql>
    <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmUserExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from tpm_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <if test="example.distinct">
            distinct
        </if>
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.escapedColumnName}
                </foreach>
            </when>
            <otherwise>
                id, username, `password`, gender, birthday, last_login_time, last_login_ip, user_level,
                nickname, mobile, avatar, weixin_openid, `status`, points, balance, membership_level,
                membership_id, membership_code, accumulative_points, add_time, update_time, deleted,
                share_user_id
            </otherwise>
        </choose>
        from tpm_user
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from tpm_user
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from tpm_user
        where id = #{id,jdbcType=INTEGER}
        and deleted =
        <choose>
            <when test="andLogicalDeleted">
                1
            </when>
            <otherwise>
                0
            </otherwise>
        </choose>
    </select>
    <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.escapedColumnName}
                </foreach>
            </when>
            <otherwise>
                id, username, `password`, gender, birthday, last_login_time, last_login_ip, user_level,
                nickname, mobile, avatar, weixin_openid, `status`, points, balance, membership_level,
                membership_id, membership_code, accumulative_points, add_time, update_time, deleted,
                share_user_id
            </otherwise>
        </choose>
        from tpm_user
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tpm_user
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmUserExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tpm_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmUser">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tpm_user (username, `password`, gender,
        birthday, last_login_time, last_login_ip,
        user_level, nickname, mobile,
        avatar, weixin_openid, `status`,
        points, balance, membership_level,
        membership_id, membership_code, accumulative_points,
        add_time, update_time, deleted,
        share_user_id)
        values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{gender,jdbcType=TINYINT},
        #{birthday,jdbcType=DATE}, #{lastLoginTime,jdbcType=TIMESTAMP}, #{lastLoginIp,jdbcType=VARCHAR},
        #{userLevel,jdbcType=TINYINT}, #{nickname,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR},
        #{avatar,jdbcType=VARCHAR}, #{weixinOpenid,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
        #{points,jdbcType=DECIMAL}, #{balance,jdbcType=DECIMAL}, #{membershipLevel,jdbcType=VARCHAR},
        #{membershipId,jdbcType=INTEGER}, #{membershipCode,jdbcType=VARCHAR}, #{accumulativePoints,jdbcType=DECIMAL},
        #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT},
        #{shareUserId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmUser">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tpm_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null">
                username,
            </if>
            <if test="password != null">
                `password`,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="lastLoginTime != null">
                last_login_time,
            </if>
            <if test="lastLoginIp != null">
                last_login_ip,
            </if>
            <if test="userLevel != null">
                user_level,
            </if>
            <if test="nickname != null">
                nickname,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="weixinOpenid != null">
                weixin_openid,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="points != null">
                points,
            </if>
            <if test="balance != null">
                balance,
            </if>
            <if test="membershipLevel != null">
                membership_level,
            </if>
            <if test="membershipId != null">
                membership_id,
            </if>
            <if test="membershipCode != null">
                membership_code,
            </if>
            <if test="accumulativePoints != null">
                accumulative_points,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="shareUserId != null">
                share_user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=TINYINT},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="lastLoginTime != null">
                #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLoginIp != null">
                #{lastLoginIp,jdbcType=VARCHAR},
            </if>
            <if test="userLevel != null">
                #{userLevel,jdbcType=TINYINT},
            </if>
            <if test="nickname != null">
                #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="weixinOpenid != null">
                #{weixinOpenid,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="points != null">
                #{points,jdbcType=DECIMAL},
            </if>
            <if test="balance != null">
                #{balance,jdbcType=DECIMAL},
            </if>
            <if test="membershipLevel != null">
                #{membershipLevel,jdbcType=VARCHAR},
            </if>
            <if test="membershipId != null">
                #{membershipId,jdbcType=INTEGER},
            </if>
            <if test="membershipCode != null">
                #{membershipCode,jdbcType=VARCHAR},
            </if>
            <if test="accumulativePoints != null">
                #{accumulativePoints,jdbcType=DECIMAL},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="shareUserId != null">
                #{shareUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmUserExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from tpm_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_user
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.username != null">
                username = #{record.username,jdbcType=VARCHAR},
            </if>
            <if test="record.password != null">
                `password` = #{record.password,jdbcType=VARCHAR},
            </if>
            <if test="record.gender != null">
                gender = #{record.gender,jdbcType=TINYINT},
            </if>
            <if test="record.birthday != null">
                birthday = #{record.birthday,jdbcType=DATE},
            </if>
            <if test="record.lastLoginTime != null">
                last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.lastLoginIp != null">
                last_login_ip = #{record.lastLoginIp,jdbcType=VARCHAR},
            </if>
            <if test="record.userLevel != null">
                user_level = #{record.userLevel,jdbcType=TINYINT},
            </if>
            <if test="record.nickname != null">
                nickname = #{record.nickname,jdbcType=VARCHAR},
            </if>
            <if test="record.mobile != null">
                mobile = #{record.mobile,jdbcType=VARCHAR},
            </if>
            <if test="record.avatar != null">
                avatar = #{record.avatar,jdbcType=VARCHAR},
            </if>
            <if test="record.weixinOpenid != null">
                weixin_openid = #{record.weixinOpenid,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                `status` = #{record.status,jdbcType=TINYINT},
            </if>
            <if test="record.points != null">
                points = #{record.points,jdbcType=DECIMAL},
            </if>
            <if test="record.balance != null">
                balance = #{record.balance,jdbcType=DECIMAL},
            </if>
            <if test="record.membershipLevel != null">
                membership_level = #{record.membershipLevel,jdbcType=VARCHAR},
            </if>
            <if test="record.membershipId != null">
                membership_id = #{record.membershipId,jdbcType=INTEGER},
            </if>
            <if test="record.membershipCode != null">
                membership_code = #{record.membershipCode,jdbcType=VARCHAR},
            </if>
            <if test="record.accumulativePoints != null">
                accumulative_points = #{record.accumulativePoints,jdbcType=DECIMAL},
            </if>
            <if test="record.addTime != null">
                add_time = #{record.addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.deleted != null">
                deleted = #{record.deleted,jdbcType=BIT},
            </if>
            <if test="record.shareUserId != null">
                share_user_id = #{record.shareUserId,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_user
        set id = #{record.id,jdbcType=INTEGER},
        username = #{record.username,jdbcType=VARCHAR},
        `password` = #{record.password,jdbcType=VARCHAR},
        gender = #{record.gender,jdbcType=TINYINT},
        birthday = #{record.birthday,jdbcType=DATE},
        last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
        last_login_ip = #{record.lastLoginIp,jdbcType=VARCHAR},
        user_level = #{record.userLevel,jdbcType=TINYINT},
        nickname = #{record.nickname,jdbcType=VARCHAR},
        mobile = #{record.mobile,jdbcType=VARCHAR},
        avatar = #{record.avatar,jdbcType=VARCHAR},
        weixin_openid = #{record.weixinOpenid,jdbcType=VARCHAR},
        `status` = #{record.status,jdbcType=TINYINT},
        points = #{record.points,jdbcType=DECIMAL},
        balance = #{record.balance,jdbcType=DECIMAL},
        membership_level = #{record.membershipLevel,jdbcType=VARCHAR},
        membership_id = #{record.membershipId,jdbcType=INTEGER},
        membership_code = #{record.membershipCode,jdbcType=VARCHAR},
        accumulative_points = #{record.accumulativePoints,jdbcType=DECIMAL},
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        deleted = #{record.deleted,jdbcType=BIT},
        share_user_id = #{record.shareUserId,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmUser">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_user
        <set>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                `password` = #{password,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=TINYINT},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLoginIp != null">
                last_login_ip = #{lastLoginIp,jdbcType=VARCHAR},
            </if>
            <if test="userLevel != null">
                user_level = #{userLevel,jdbcType=TINYINT},
            </if>
            <if test="nickname != null">
                nickname = #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="weixinOpenid != null">
                weixin_openid = #{weixinOpenid,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="points != null">
                points = #{points,jdbcType=DECIMAL},
            </if>
            <if test="balance != null">
                balance = #{balance,jdbcType=DECIMAL},
            </if>
            <if test="membershipLevel != null">
                membership_level = #{membershipLevel,jdbcType=VARCHAR},
            </if>
            <if test="membershipId != null">
                membership_id = #{membershipId,jdbcType=INTEGER},
            </if>
            <if test="membershipCode != null">
                membership_code = #{membershipCode,jdbcType=VARCHAR},
            </if>
            <if test="accumulativePoints != null">
                accumulative_points = #{accumulativePoints,jdbcType=DECIMAL},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="shareUserId != null">
                share_user_id = #{shareUserId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmUser">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tpm_user
        set username = #{username,jdbcType=VARCHAR},
        `password` = #{password,jdbcType=VARCHAR},
        gender = #{gender,jdbcType=TINYINT},
        birthday = #{birthday,jdbcType=DATE},
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
        last_login_ip = #{lastLoginIp,jdbcType=VARCHAR},
        user_level = #{userLevel,jdbcType=TINYINT},
        nickname = #{nickname,jdbcType=VARCHAR},
        mobile = #{mobile,jdbcType=VARCHAR},
        avatar = #{avatar,jdbcType=VARCHAR},
        weixin_openid = #{weixinOpenid,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=TINYINT},
        points = #{points,jdbcType=DECIMAL},
        balance = #{balance,jdbcType=DECIMAL},
        membership_level = #{membershipLevel,jdbcType=VARCHAR},
        membership_id = #{membershipId,jdbcType=INTEGER},
        membership_code = #{membershipCode,jdbcType=VARCHAR},
        accumulative_points = #{accumulativePoints,jdbcType=DECIMAL},
        add_time = #{addTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        deleted = #{deleted,jdbcType=BIT},
        share_user_id = #{shareUserId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmUserExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <include refid="Base_Column_List"/>
        from tpm_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        limit 1
    </select>
    <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        select
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.escapedColumnName}
                </foreach>
            </when>
            <otherwise>
                id, username, `password`, gender, birthday, last_login_time, last_login_ip, user_level,
                nickname, mobile, avatar, weixin_openid, `status`, points, balance, membership_level,
                membership_id, membership_code, accumulative_points, add_time, update_time, deleted,
                share_user_id
            </otherwise>
        </choose>
        from tpm_user
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
        limit 1
    </select>
    <update id="logicalDeleteByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        update tpm_user set deleted = 1
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        update tpm_user set deleted = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateUserBalance">
        update tpm_user t set t.balance = (t.balance + #{amount}) where t.status = 'A'
        <if test="userIds != null and userIds.size &gt; 0">
            AND t.ID IN
            <foreach close=")" collection="userIds" index="index" item="userId" open="(" separator=",">
                #{userId}
            </foreach>
        </if>
    </update>
</mapper>