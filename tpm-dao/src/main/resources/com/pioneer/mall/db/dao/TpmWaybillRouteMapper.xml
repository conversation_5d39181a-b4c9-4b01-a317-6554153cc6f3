<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.TpmWaybillRouteMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmWaybillRoute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tpm_order_id" jdbcType="INTEGER" property="tpmOrderId" />
    <result column="mail_no" jdbcType="VARCHAR" property="mailNo" />
    <result column="reason_name" jdbcType="VARCHAR" property="reasonName" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="accept_time" jdbcType="VARCHAR" property="acceptTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="op_code" jdbcType="VARCHAR" property="opCode" />
    <result column="reason_code" jdbcType="VARCHAR" property="reasonCode" />
    <result column="first_status_code" jdbcType="VARCHAR" property="firstStatusCode" />
    <result column="first_status_name" jdbcType="VARCHAR" property="firstStatusName" />
    <result column="secondary_status_code" jdbcType="VARCHAR" property="secondaryStatusCode" />
    <result column="secondary_status_name" jdbcType="VARCHAR" property="secondaryStatusName" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.pioneer.mall.db.domain.TpmWaybillRoute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="accept_address" jdbcType="LONGVARCHAR" property="acceptAddress" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tpm_order_id, mail_no, reason_name, order_id, accept_time, remark, op_code, reason_code, 
    first_status_code, first_status_name, secondary_status_code, secondary_status_name, 
    add_time, update_time, deleted
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    accept_address
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.pioneer.mall.db.domain.TpmWaybillRouteExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.pioneer.mall.db.domain.TpmWaybillRouteExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, tpm_order_id, mail_no, reason_name, order_id, accept_time, remark, op_code, reason_code, 
          first_status_code, first_status_name, secondary_status_code, secondary_status_name, 
          add_time, update_time, deleted, accept_address
      </otherwise>
    </choose>
    from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_waybill_route
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_waybill_route
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, tpm_order_id, mail_no, reason_name, order_id, accept_time, remark, op_code, reason_code, 
          first_status_code, first_status_name, secondary_status_code, secondary_status_name, 
          add_time, update_time, deleted, accept_address
      </otherwise>
    </choose>
    from tpm_waybill_route
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_waybill_route
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.db.domain.TpmWaybillRouteExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.db.domain.TpmWaybillRoute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_waybill_route (tpm_order_id, mail_no, reason_name, 
      order_id, accept_time, remark, 
      op_code, reason_code, first_status_code, 
      first_status_name, secondary_status_code, secondary_status_name, 
      add_time, update_time, deleted, 
      accept_address)
    values (#{tpmOrderId,jdbcType=INTEGER}, #{mailNo,jdbcType=VARCHAR}, #{reasonName,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{acceptTime,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{opCode,jdbcType=VARCHAR}, #{reasonCode,jdbcType=VARCHAR}, #{firstStatusCode,jdbcType=VARCHAR}, 
      #{firstStatusName,jdbcType=VARCHAR}, #{secondaryStatusCode,jdbcType=VARCHAR}, #{secondaryStatusName,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, 
      #{acceptAddress,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.db.domain.TpmWaybillRoute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_waybill_route
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tpmOrderId != null">
        tpm_order_id,
      </if>
      <if test="mailNo != null">
        mail_no,
      </if>
      <if test="reasonName != null">
        reason_name,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="acceptTime != null">
        accept_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="opCode != null">
        op_code,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="firstStatusCode != null">
        first_status_code,
      </if>
      <if test="firstStatusName != null">
        first_status_name,
      </if>
      <if test="secondaryStatusCode != null">
        secondary_status_code,
      </if>
      <if test="secondaryStatusName != null">
        secondary_status_name,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="acceptAddress != null">
        accept_address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tpmOrderId != null">
        #{tpmOrderId,jdbcType=INTEGER},
      </if>
      <if test="mailNo != null">
        #{mailNo,jdbcType=VARCHAR},
      </if>
      <if test="reasonName != null">
        #{reasonName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="acceptTime != null">
        #{acceptTime,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="opCode != null">
        #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="firstStatusCode != null">
        #{firstStatusCode,jdbcType=VARCHAR},
      </if>
      <if test="firstStatusName != null">
        #{firstStatusName,jdbcType=VARCHAR},
      </if>
      <if test="secondaryStatusCode != null">
        #{secondaryStatusCode,jdbcType=VARCHAR},
      </if>
      <if test="secondaryStatusName != null">
        #{secondaryStatusName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="acceptAddress != null">
        #{acceptAddress,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.db.domain.TpmWaybillRouteExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_waybill_route
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.tpmOrderId != null">
        tpm_order_id = #{record.tpmOrderId,jdbcType=INTEGER},
      </if>
      <if test="record.mailNo != null">
        mail_no = #{record.mailNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonName != null">
        reason_name = #{record.reasonName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.acceptTime != null">
        accept_time = #{record.acceptTime,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.opCode != null">
        op_code = #{record.opCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonCode != null">
        reason_code = #{record.reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="record.firstStatusCode != null">
        first_status_code = #{record.firstStatusCode,jdbcType=VARCHAR},
      </if>
      <if test="record.firstStatusName != null">
        first_status_name = #{record.firstStatusName,jdbcType=VARCHAR},
      </if>
      <if test="record.secondaryStatusCode != null">
        secondary_status_code = #{record.secondaryStatusCode,jdbcType=VARCHAR},
      </if>
      <if test="record.secondaryStatusName != null">
        secondary_status_name = #{record.secondaryStatusName,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.acceptAddress != null">
        accept_address = #{record.acceptAddress,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_waybill_route
    set id = #{record.id,jdbcType=INTEGER},
      tpm_order_id = #{record.tpmOrderId,jdbcType=INTEGER},
      mail_no = #{record.mailNo,jdbcType=VARCHAR},
      reason_name = #{record.reasonName,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      accept_time = #{record.acceptTime,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      op_code = #{record.opCode,jdbcType=VARCHAR},
      reason_code = #{record.reasonCode,jdbcType=VARCHAR},
      first_status_code = #{record.firstStatusCode,jdbcType=VARCHAR},
      first_status_name = #{record.firstStatusName,jdbcType=VARCHAR},
      secondary_status_code = #{record.secondaryStatusCode,jdbcType=VARCHAR},
      secondary_status_name = #{record.secondaryStatusName,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      accept_address = #{record.acceptAddress,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_waybill_route
    set id = #{record.id,jdbcType=INTEGER},
      tpm_order_id = #{record.tpmOrderId,jdbcType=INTEGER},
      mail_no = #{record.mailNo,jdbcType=VARCHAR},
      reason_name = #{record.reasonName,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      accept_time = #{record.acceptTime,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      op_code = #{record.opCode,jdbcType=VARCHAR},
      reason_code = #{record.reasonCode,jdbcType=VARCHAR},
      first_status_code = #{record.firstStatusCode,jdbcType=VARCHAR},
      first_status_name = #{record.firstStatusName,jdbcType=VARCHAR},
      secondary_status_code = #{record.secondaryStatusCode,jdbcType=VARCHAR},
      secondary_status_name = #{record.secondaryStatusName,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.db.domain.TpmWaybillRoute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_waybill_route
    <set>
      <if test="tpmOrderId != null">
        tpm_order_id = #{tpmOrderId,jdbcType=INTEGER},
      </if>
      <if test="mailNo != null">
        mail_no = #{mailNo,jdbcType=VARCHAR},
      </if>
      <if test="reasonName != null">
        reason_name = #{reasonName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="acceptTime != null">
        accept_time = #{acceptTime,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="opCode != null">
        op_code = #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="firstStatusCode != null">
        first_status_code = #{firstStatusCode,jdbcType=VARCHAR},
      </if>
      <if test="firstStatusName != null">
        first_status_name = #{firstStatusName,jdbcType=VARCHAR},
      </if>
      <if test="secondaryStatusCode != null">
        secondary_status_code = #{secondaryStatusCode,jdbcType=VARCHAR},
      </if>
      <if test="secondaryStatusName != null">
        secondary_status_name = #{secondaryStatusName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="acceptAddress != null">
        accept_address = #{acceptAddress,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.pioneer.mall.db.domain.TpmWaybillRoute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_waybill_route
    set tpm_order_id = #{tpmOrderId,jdbcType=INTEGER},
      mail_no = #{mailNo,jdbcType=VARCHAR},
      reason_name = #{reasonName,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      accept_time = #{acceptTime,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      op_code = #{opCode,jdbcType=VARCHAR},
      reason_code = #{reasonCode,jdbcType=VARCHAR},
      first_status_code = #{firstStatusCode,jdbcType=VARCHAR},
      first_status_name = #{firstStatusName,jdbcType=VARCHAR},
      secondary_status_code = #{secondaryStatusCode,jdbcType=VARCHAR},
      secondary_status_name = #{secondaryStatusName,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      accept_address = #{acceptAddress,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.db.domain.TpmWaybillRoute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_waybill_route
    set tpm_order_id = #{tpmOrderId,jdbcType=INTEGER},
      mail_no = #{mailNo,jdbcType=VARCHAR},
      reason_name = #{reasonName,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      accept_time = #{acceptTime,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      op_code = #{opCode,jdbcType=VARCHAR},
      reason_code = #{reasonCode,jdbcType=VARCHAR},
      first_status_code = #{firstStatusCode,jdbcType=VARCHAR},
      first_status_name = #{firstStatusName,jdbcType=VARCHAR},
      secondary_status_code = #{secondaryStatusCode,jdbcType=VARCHAR},
      secondary_status_name = #{secondaryStatusName,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.db.domain.TpmWaybillRouteExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleWithBLOBs" parameterType="com.pioneer.mall.db.domain.TpmWaybillRouteExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, tpm_order_id, mail_no, reason_name, order_id, accept_time, remark, op_code, reason_code, 
          first_status_code, first_status_name, secondary_status_code, secondary_status_name, 
          add_time, update_time, deleted, accept_address
      </otherwise>
    </choose>
    from tpm_waybill_route
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_waybill_route set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_waybill_route set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>