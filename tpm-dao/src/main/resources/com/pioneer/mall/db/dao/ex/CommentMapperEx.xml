<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.ex.CommentMapperEx">

  <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmComment">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="value_id" jdbcType="INTEGER" property="valueId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="has_picture" jdbcType="BIT" property="hasPicture" />
    <result column="pic_urls" jdbcType="VARCHAR" property="picUrls" typeHandler="com.pioneer.mall.db.mybatis.JsonStringArrayTypeHandler" />
    <result column="star" jdbcType="SMALLINT" property="star" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
 
  <sql id="Base_Column_List">
    o.id, o.value_id, o.`type`, o.content, o.user_id, o.has_picture, o.pic_urls, o.star, o.add_time, o.update_time, 
    o.deleted
  </sql>

  <select id="queryBrandComment" resultMap="BaseResultMap" parameterType="map">
    select distinct 
    <include refid="Base_Column_List" />
    from tpm_comment o
	join tpm_goods g on o.value_id=g.id
	and o.deleted = 0 
	<if test="type != null and type != ''">
        and o.`type` = #{type,jdbcType=TINYINT} 
    </if>
    <if test="userId != null and userId != ''">
        and o.user_id = #{userId} 
    </if>
    <if test="valueId != null and valueId != ''">
        and o.value_id = #{valueId} 
    </if>
    <if test="brandIdsSql != null and brandIdsSql != ''">
        ${brandIdsSql} 
    </if>
    <if test="orderBySql != null">
      order by ${orderBySql}
    </if>
  </select>
</mapper>