<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.ex.GoodsInventoryMapper">
    <update id="addInventory" parameterType="map">
        update tpm_goods
        set inventory_num = inventory_num + #{num,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="reduceInventory" parameterType="map">
        update tpm_goods
        set inventory_num = inventory_num - #{num,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=INTEGER} and inventory_num >= #{num,jdbcType=INTEGER}
    </update>

</mapper>