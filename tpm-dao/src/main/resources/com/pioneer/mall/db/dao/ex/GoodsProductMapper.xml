<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.ex.GoodsProductMapper">
    <update id="addStock" parameterType="map">
        update tpm_goods_product
        set number = number + #{num,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <update id="reduceStock" parameterType="map">
        update tpm_goods_product
        set number = number - #{num,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=INTEGER} and number >= #{num,jdbcType=INTEGER}
    </update>
    
    <update id="addBrowse" parameterType="map">
        update tpm_goods
        set browse = browse + #{num,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=INTEGER} 
    </update>
    
    <update id="addSales" parameterType="map">
        update tpm_goods
        set sales = sales + #{num,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=INTEGER} 
    </update>
</mapper>