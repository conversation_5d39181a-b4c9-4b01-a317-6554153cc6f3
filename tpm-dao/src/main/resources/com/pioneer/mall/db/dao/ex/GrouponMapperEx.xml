<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.ex.GrouponMapperEx">
  <resultMap id="RuleResultMap" type="com.pioneer.mall.db.domain.TpmGrouponRules">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="discount_member" jdbcType="INTEGER" property="discountMember" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
 
  <sql id="Rule_Column_List">
    o.id, o.goods_id, o.goods_name, o.pic_url, o.discount, o.discount_member, o.add_time, o.update_time, 
    o.expire_time, o.deleted
  </sql>

  <select id="queryBrandGrouponRules" resultMap="RuleResultMap" parameterType="map">
    select distinct 
    <include refid="Rule_Column_List" />
    from tpm_groupon_rules o
	join tpm_goods g on o.goods_id=g.id
	and o.deleted = 0 
    <if test="goodsId != null and goodsId != ''">
        and o.goods_id = #{goodsId} 
    </if>
    <if test="brandIdsSql != null and brandIdsSql != ''">
        ${brandIdsSql} 
    </if>
    <if test="orderBySql != null">
      order by ${orderBySql}
    </if>
  </select>
  
   <resultMap id="GrouponsResultMap" type="com.pioneer.mall.db.domain.TpmGroupon">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="groupon_id" jdbcType="INTEGER" property="grouponId" />
    <result column="rules_id" jdbcType="INTEGER" property="rulesId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="creator_user_id" jdbcType="INTEGER" property="creatorUserId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="share_url" jdbcType="VARCHAR" property="shareUrl" />
    <result column="payed" jdbcType="BIT" property="payed" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  
  <sql id="Groupons_Column_List">
    o.id, o.order_id, o.groupon_id, o.rules_id, o.user_id, o.creator_user_id, o.add_time, o.update_time, 
    o.share_url, o.payed, o.deleted
  </sql>
  
  <select id="queryBrandGroupons" resultMap="GrouponsResultMap" parameterType="map">
    select distinct 
    <include refid="Groupons_Column_List" />
    from tpm_groupon o
    join tpm_groupon_rules r on o.rules_id = r.id
    join tpm_goods g on r.goods_id=g.id
	and o.deleted = 0 
    <if test="rulesId != null and rulesId != ''">
        and o.rules_id = #{rulesId} 
    </if>
    <if test="brandIdsSql != null and brandIdsSql != ''">
        ${brandIdsSql} 
    </if>
    <if test="orderBySql != null">
      order by ${orderBySql}
    </if>
  </select>
  
</mapper>