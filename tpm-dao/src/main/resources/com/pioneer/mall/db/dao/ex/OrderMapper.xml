<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.db.dao.ex.OrderMapper">

    <resultMap id="BaseResultMap" type="com.pioneer.mall.db.domain.TpmOrder">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="order_sn" jdbcType="VARCHAR" property="orderSn"/>
        <result column="order_status" jdbcType="SMALLINT" property="orderStatus"/>
        <result column="consignee" jdbcType="VARCHAR" property="consignee"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="message" jdbcType="VARCHAR" property="message"/>
        <result column="goods_price" jdbcType="DECIMAL" property="goodsPrice"/>
        <result column="freight_price" jdbcType="DECIMAL" property="freightPrice"/>
        <result column="coupon_price" jdbcType="DECIMAL" property="couponPrice"/>
        <result column="integral_price" jdbcType="DECIMAL" property="integralPrice"/>
        <result column="groupon_price" jdbcType="DECIMAL" property="grouponPrice"/>
        <result column="order_price" jdbcType="DECIMAL" property="orderPrice"/>
        <result column="actual_price" jdbcType="DECIMAL" property="actualPrice"/>
        <result column="pay_id" jdbcType="VARCHAR" property="payId"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="ship_sn" jdbcType="VARCHAR" property="shipSn"/>
        <result column="ship_channel" jdbcType="VARCHAR" property="shipChannel"/>
        <result column="ship_time" jdbcType="TIMESTAMP" property="shipTime"/>
        <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime"/>
        <result column="comments" jdbcType="SMALLINT" property="comments"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="settlement_money" jdbcType="DECIMAL" property="settlementMoney"/>
        <result column="settlement_status" jdbcType="BIT" property="settlementStatus"/>
        <result column="freight_type" jdbcType="TINYINT" property="freightType"/>
        <result column="share_user_id" jdbcType="INTEGER" property="shareUserId"/>
        <result column="fetch_code" jdbcType="VARCHAR" property="fetchCode"/>
        <result column="create_user_id" jdbcType="INTEGER" property="createUserId"/>
        <result column="gift_send_time" jdbcType="TIMESTAMP" property="giftSendTime"/>
        <result column="gift_receive_time" jdbcType="TIMESTAMP" property="giftReceiveTime"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="refund_status" jdbcType="VARCHAR" property="refundStatus"/>
    </resultMap>

    <update id="updateWithOptimisticLocker" parameterType="map">
        update tpm_order
        <set>
            <if test="order.id != null">
                id = #{order.id,jdbcType=INTEGER},
            </if>
            <if test="order.userId != null">
                user_id = #{order.userId,jdbcType=INTEGER},
            </if>
            <if test="order.orderSn != null">
                order_sn = #{order.orderSn,jdbcType=VARCHAR},
            </if>
            <if test="order.orderStatus != null">
                order_status = #{order.orderStatus,jdbcType=SMALLINT},
            </if>
            <if test="order.consignee != null">
                consignee = #{order.consignee,jdbcType=VARCHAR},
            </if>
            <if test="order.mobile != null">
                mobile = #{order.mobile,jdbcType=VARCHAR},
            </if>
            <if test="order.address != null">
                address = #{order.address,jdbcType=VARCHAR},
            </if>
            <if test="order.message != null">
                message = #{order.message,jdbcType=VARCHAR},
            </if>
            <if test="order.goodsPrice != null">
                goods_price = #{order.goodsPrice,jdbcType=DECIMAL},
            </if>
            <if test="order.freightPrice != null">
                freight_price = #{order.freightPrice,jdbcType=DECIMAL},
            </if>
            <if test="order.couponPrice != null">
                coupon_price = #{order.couponPrice,jdbcType=DECIMAL},
            </if>
            <if test="order.integralPrice != null">
                integral_price = #{order.integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="order.grouponPrice != null">
                groupon_price = #{order.grouponPrice,jdbcType=DECIMAL},
            </if>
            <if test="order.orderPrice != null">
                order_price = #{order.orderPrice,jdbcType=DECIMAL},
            </if>
            <if test="order.actualPrice != null">
                actual_price = #{order.actualPrice,jdbcType=DECIMAL},
            </if>
            <if test="order.payId != null">
                pay_id = #{order.payId,jdbcType=VARCHAR},
            </if>
            <if test="order.payTime != null">
                pay_time = #{order.payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.shipSn != null">
                ship_sn = #{order.shipSn,jdbcType=VARCHAR},
            </if>
            <if test="order.shipChannel != null">
                ship_channel = #{order.shipChannel,jdbcType=VARCHAR},
            </if>
            <if test="order.shipTime != null">
                ship_time = #{order.shipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.confirmTime != null">
                confirm_time = #{order.confirmTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.comments != null">
                comments = #{order.comments,jdbcType=SMALLINT},
            </if>
            <if test="order.endTime != null">
                end_time = #{order.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.addTime != null">
                add_time = #{order.addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.updateTime != null">
                update_time = #{order.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.deleted != null">
                deleted = #{order.deleted,jdbcType=BIT},
            </if>
            <if test="order.payType != null">
                pay_type = #{order.payType,jdbcType=VARCHAR},
            </if>
            <if test="order.refundStatus != null">
                refund_status = #{order.refundStatus,jdbcType=VARCHAR},
            </if>
            <if test="order.deliveryOrderId != null">
                delivery_order_id = #{order.deliveryOrderId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{order.id,jdbcType=INTEGER} and update_time = #{lastUpdateTime,jdbcType=INTEGER}
    </update>

    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        o.id, o.user_id, o.order_sn, o.order_status, o.consignee, o.mobile, o.address, o.message, o.goods_price,
        o.freight_price, o.coupon_price, o.integral_price, o.groupon_price, o.order_price, o.actual_price,
        o.pay_id, o.pay_time, o.ship_sn, o.ship_channel, o.ship_time, o.confirm_time, o.comments, o.end_time,
        o.add_time, o.update_time, o.deleted, o.settlement_money, o.settlement_status, o.business_type
    </sql>

    <select id="selectBrandOrdersByExample" resultMap="BaseResultMap" parameterType="map">
        select distinct
        <include refid="Base_Column_List"/>
        from tpm_order o
        join tpm_order_goods g on o.id=g.order_id
        and o.deleted = 0
        <if test="userId != null and userId != ''">
            and o.user_id = #{userId}
        </if>
        <if test="businessType != null and businessType != ''">
            and o.business_type = #{businessType}
        </if>
        <if test="freightType != null and freightType != ''">
            and o.freight_type = #{freightType}
        </if>
        <if test="orderSn != null and orderSn != ''">
            and o.order_sn = #{orderSn}
        </if>
        <if test="shopIdList != null and shopIdList.size() > 0">
            and o.shop_id in
            <foreach item="item" index="index" collection="shopIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderStatusSql != null and orderStatusSql != ''">
            ${orderStatusSql}
        </if>
        <if test="brandIdsSql != null and brandIdsSql != ''">
            ${brandIdsSql}
        </if>
        <if test="orderBySql != null">
            order by ${orderBySql}
        </if>
    </select>

    <select id="countByTypeToday" resultType="java.lang.Integer" parameterType="map">
        SELECT COUNT(*)
        FROM tpm_order
        WHERE freight_type = #{freightType}
          and DATE(add_time) = CURDATE();
    </select>
</mapper>