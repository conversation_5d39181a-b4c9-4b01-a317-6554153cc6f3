package com.pioneer.mall.wx.bean.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/29 16:36
 */
@Data
public class TpmAddressAddUpdateReqVo implements Serializable {
    //用户id
    private Integer userId;
    //默认
    private Boolean isDefault;
    //添加就不传 更新必传
    private Integer id;
    //姓名
    private String name;
    //手机号
    private String mobile;
    //省份id
    private Integer provinceId;
    //身份名
    private String provinceName;
    //城市id
    private Integer cityId;
    //城市名
    private String cityName;
    //地区id
    private Integer areaId;
    //地区名
    private String areaName;
    //详细地址
    private String address;
    //经度
    private String longitude;
    //纬度
    private String latitude;
    private String addressName;
    private String addressLocation;
    private String addressDetail;

    private Integer businessType;
}
