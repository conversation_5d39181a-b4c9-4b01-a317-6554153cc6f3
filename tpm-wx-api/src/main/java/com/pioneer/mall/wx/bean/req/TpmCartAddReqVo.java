package com.pioneer.mall.wx.bean.req;

import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/28 23:49
 */
@Data
public class TpmCartAddReqVo implements Serializable {
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 店铺id
     */
    private Integer shopId;
    /**
     * 数量
     */
    private Integer number;
    /**
     * 库存
     */
    private Integer productId;
    /**
     * 货品id
     */
    private Integer goodsId;
    /**
     * 购物车id
     */
    private Integer id;
    /**
     * 属性
     */
    private List<TpmGoodsAttributesDto> goodsAttributes;

    private Integer businessType;
}
