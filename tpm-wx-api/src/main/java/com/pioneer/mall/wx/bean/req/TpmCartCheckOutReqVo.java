package com.pioneer.mall.wx.bean.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/26 22:48
 */
@Data
public class TpmCartCheckOutReqVo implements Serializable {
    private Integer shopId;
    /**
     * 用户id
     */
    private Integer userId;
    private Integer addressId;
    /**
     * 订单类型 0是外卖，1是自提，2是堂食
     */
    private Byte freightType;
    /**
     * 购物车id
     */
    private List<Integer> cartIdList;

    private Integer couponUserId;

}
