package com.pioneer.mall.wx.bean.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/28 23:49
 */
@Data
public class TpmCartCheckedReqVo implements Serializable {
    /**
     * 用户id
     */
    private Integer userId;
//    /**
//     * 数量
//     */
//    private Integer number;
//    /**
//     * 库存
//     */
//    private Integer productId;
    /**
     * 店铺id
     */
    private Integer shopId;
    /**
     * 是否选中 1选中 0未选中
     */
    //    private Integer goodsId;
    private Integer isChecked;
    /**
     * 购物车id
     */
    private List<Integer> idList;
}
