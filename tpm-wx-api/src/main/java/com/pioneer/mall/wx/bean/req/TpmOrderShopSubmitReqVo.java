package com.pioneer.mall.wx.bean.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 提交订单对象
 * @date 2024/3/25 22:07
 */
@Data
public class TpmOrderShopSubmitReqVo implements Serializable {
    private Integer shopId;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 冷链
     */
    private TpmOrderSubmitReqVo coldChainSubmit;
    /**
     * 普快
     */
    private TpmOrderSubmitReqVo commonSubmit;
    /**
     * 地址id
     */
    private Integer addressId;
    /**
     * 订单备注
     */
    private String remark;
    /**
     * 运费
     */
    private BigDecimal freightPrice;
    /**
     * 打包费
     */
    private BigDecimal packingFee;
    /**
     * 联系电话
     */
    private String telephone;
}
