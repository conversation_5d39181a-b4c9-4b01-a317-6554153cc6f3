package com.pioneer.mall.wx.bean.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 提交订单对象
 * @date 2024/3/25 22:07
 */
@Data
public class TpmOrderSubmitReqVo implements Serializable {
    private Integer shopId;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 购物车选中品的id
     */
    private Map<Integer, String> cartIdRemarkMap;
    /**
     * 地址id
     */
    private Integer addressId;

    //距离，单位米
    private BigDecimal distance;
    /**
     * 优惠券user-id
     */
    private Integer couponUserId;
    /**
     * 订单备注
     */
    private String remark;
    /**
     * 订单类型 0是外卖，1是自提，2是堂食
     */
    private Byte freightType;
    /**
     * 运费
     */
    private BigDecimal freightPrice;
    /**
     * 打包费
     */
    private BigDecimal packingFee;
    /**
     * 联系电话
     */
    private String telephone;
    /**
     * 优惠口令
     */
    private String couponCode;

    /**
     * 预约取餐或配送时间（时间格式 HH:mm）
     * 适用于所有订单类型：
     * - 外卖订单：预约配送时间
     * - 自提订单：预约取餐时间
     * - 堂食订单：预约用餐时间
     *
     * 示例："20:00" 表示当天晚上8点
     * 格式：HH:mm（24小时制）
     */
    private String scheduledTime;
}
