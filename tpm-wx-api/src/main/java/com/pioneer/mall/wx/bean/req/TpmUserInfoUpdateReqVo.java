package com.pioneer.mall.wx.bean.req;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/4/2 23:01
 */
@Data
public class TpmUserInfoUpdateReqVo implements Serializable {
    private Integer userId;
    //昵称
    private String nickName;
    //姓名
    private String userName;
    //头像
    private String avatar;
    //性别：0 未知， 1男， 2女
    private Integer gender;
    //手机号
    private String telephone;
    //生日
    private LocalDate birthday;
    //会员卡号
    private String membershipCode;
    //加入会员日期
    private String joinMemberDateDesc;
}
