package com.pioneer.mall.wx.bean.res;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 腾讯地图距离矩阵接口返回结果
 * @date 2025/7/7 21:19
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true) // 忽略未知字段
public class DistanceMatrixResponse {
    private int status;
    private String message;
    private Result result;

    /**
     * 结果核心数据
     */
    @Data
    public static class Result {
        private List<Row> rows;
    }

    /**
     * 单个起点对应的所有终点结果
     */
    @Data
    public static class Row {
        private List<Element> elements;
    }

    /**
     * 单个起点到单个终点的距离信息
     */
    @Data
    public static class Element {
        private int distance; // 距离（单位：米）
        private int duration; // 耗时（单位：秒，可选）

    }
}
