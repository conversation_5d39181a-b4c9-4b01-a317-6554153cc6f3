package com.pioneer.mall.wx.bean.res;

import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/21 21:28
 */
@Data
public class TpmAddressResVo {
    private Integer id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 是否默认
     */
    private Boolean isDefault;
    /**
     * 地址名称
     */
    private String addressName;
    /**
     * 地址区域
     */
    private String addressLocation;
    /**
     * 详细地址
     */
    private String addressDetail;
    /**
     * 省份id
     */
    private Integer provinceId;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区县id
     */
    private Integer areaId;
    /**
     * 区县名称
     */
    private String areaName;
    /**
     * 详细地址
     */
    private String address;

    //经度
    private String longitude;
    //纬度
    private String latitude;
    //是否需要选择地址 true==要强制选择地址 false==不需要强制选择地址
    private Boolean needChooseAddress;

}
