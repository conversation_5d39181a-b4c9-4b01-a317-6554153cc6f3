package com.pioneer.mall.wx.bean.res;

import com.pioneer.mall.db.domain.TpmAddress;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/23 11:27
 */
@Data
public class TpmCartCheckoutResVo {
    private int isMultiOrderModel;
    private BigDecimal goodsTotalPrice;
    private BigDecimal freightPrice;
    private List<TpmCartResVo> checkedGoodsList;
    private int addressId;
    private String telephone;
    private TpmAddress checkedAddress;
    //距离，单位米
    private BigDecimal distance;
    private int couponId;
    private int couponUserId;
    private int availableCouponLength;
    private BigDecimal couponPrice;
    private String couponName;
    //订单总价：goodsTotalPrice + totalFreightPrice - couponPrice
    private BigDecimal orderTotalPrice;
    //打包费
    private BigDecimal packingFee;
    //订单实际付款金额：orderTotalPrice - integralPrice
    private BigDecimal actualPrice;
    private boolean allowSubmitOrder;
    private String reason;
}
