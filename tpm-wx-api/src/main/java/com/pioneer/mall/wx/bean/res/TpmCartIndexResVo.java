package com.pioneer.mall.wx.bean.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/21 13:16
 */
@Data
public class TpmCartIndexResVo {
    private CartTotal cartTotal;
//    private List<TpmCart> cartList;
    private List<TpmCartResVo> errorList;
    private List<TpmCartResVo> cartList;
    private boolean allowSubmitOrder;
    private String reason;
    private String note;
    //是否超过配送距离
    private boolean overDeliveryDistance = false;
    private BigDecimal freightPrice;

    @Data
    public static class CartTotal {
        /**
         * 购物车中商品总数
         */
        private Integer goodsCount;
        /**
         * 购物车中商品总价
         */
        private BigDecimal goodsAmount;
    }
}
