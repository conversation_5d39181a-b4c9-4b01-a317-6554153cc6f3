package com.pioneer.mall.wx.bean.res;

import com.pioneer.mall.db.domain.TpmCart;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class TpmCartResVo extends TpmCart {
    private String errorMsg;
    private List<TpmGoodsAttributesDto> goodsAttributes;
    private Integer categoryId;

    private Boolean discountFlag;

    private BigDecimal discountPrice;
    //运输类型
    private Integer transportType;
}
