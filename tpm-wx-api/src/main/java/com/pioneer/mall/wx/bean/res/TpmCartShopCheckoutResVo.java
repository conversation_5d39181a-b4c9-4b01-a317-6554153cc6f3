package com.pioneer.mall.wx.bean.res;

import com.pioneer.mall.db.domain.TpmAddress;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/23 11:27
 */
@Data
public class TpmCartShopCheckoutResVo {
    //是否多订单模式
    private int isMultiOrderModel;
    private int addressId;
    private String telephone;
    private TpmAddress checkedAddress;
    //商品总价
    private BigDecimal totalGoodsPrice;
    //总运费
    private BigDecimal totalFreightPrice;
    //总打包费
    private BigDecimal totalPackingFee;
    //订单实际付款金额
    private BigDecimal totalActualPrice;
    //是否允许下单
    private boolean allowSubmitOrder;
    private String reason;
    // 运输类型-购物车对象
//    private Map<Integer,TpmCartCheckoutResVo> transCartMap;
    private TpmCartCheckoutResVo coldChainResVo;
    private TpmCartCheckoutResVo commonResVo;
}
