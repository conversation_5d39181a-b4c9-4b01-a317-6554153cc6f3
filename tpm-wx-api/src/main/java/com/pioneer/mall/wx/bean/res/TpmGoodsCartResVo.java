package com.pioneer.mall.wx.bean.res;

import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/4/8 15:27
 */
@Data
public class TpmGoodsCartResVo implements Serializable {
    private Integer id;

    private String goodsSn;

    private String name;
    /**
     * 类目id
     */
    private Integer categoryId;

    private String keywords;

    private String brief;

    private Boolean isOnSale;

    private Short sortOrder;

    private String picUrl;

    private String shareUrl;

    private Boolean isNew;

    private Boolean isHot;

    private Boolean isSellOut;

    private String unit;

    private BigDecimal counterPrice;

    private BigDecimal retailPrice;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    private Integer browse;

    private Integer sales;

    private Boolean deleted;

    private String commpany;

    private BigDecimal wholesalePrice;

    private Byte approveStatus;

    private String approveMsg;

    private Byte brokerageType;

    private BigDecimal brokeragePrice;

    private String detail;
    //库存
    private Integer inventoryNum;
    //库存提示
    private String inventoryNotify;

    //已选中数量
    private Integer checkedCount;

    private List<TpmGoodsAttributesDto> goodsAttributes;

    private Boolean discountFlag;

    private BigDecimal discountPrice;

    /**
     * 快递方式
     */
    private Integer transportType;
}
