package com.pioneer.mall.wx.bean.res;

import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.service.TpmGoodsSpecificationService;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/21 15:22
 */
@Data
public class TpmGoodsDetailResVo {
    private TpmGoods info;
    private int userHasCollect;
    private List<TpmIssue> issue;
    private List<TpmCommentResVo> comment;
    private List<TpmGoodsSpecificationService.VO> specificationList;
    private List<TpmGoodsProduct> productList;
    private List<TpmGoodsAttribute> attribute;
    private List<TpmGoodsAttributesDto> goodsAttributes;
    private TpmBrand brand;
//    private List<TpmGrouponRules> groupon;
}
