package com.pioneer.mall.wx.bean.res;

import com.pioneer.mall.core.vo.TpmOrderGoodsResVo;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/21 22:13
 */
@Data
public class TpmOrderResVo {
    private long id;
    private String orderSn;
    private LocalDateTime addTime;
    private String consignee;
    private String mobile;
    private String address;
    private BigDecimal goodsPrice;
    private BigDecimal freightPrice;
    private BigDecimal discountPrice;
    private BigDecimal packingFee;
    private BigDecimal actualPrice;
    private Integer orderStatus;
    private String orderStatusText;
    private Object handleOption;
    private String expCode;
    private String expNo;
    private boolean isGroupin;
//    private List<TpmOrderGoods> goodsList;
    private List<TpmOrderGoodsResVo> goodsList;
    //  0是外卖，1是自提，2是堂食
    private Integer freightType;
    // 支付方式 微信/余额
    private String payType;
    // 订单积分
    private BigDecimal point;
    //取餐号
    private String mealCode;
    //取餐二维码
    private String mealQrCode;
    //取餐状态
    private Boolean mealPickupStatus;
    private LocalDateTime mealPickupTime;
    private Boolean showMealQrCode;
    private String shopPhone;
    /**
     * 店铺id
     */
    private Integer shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 店铺经度
     */
    private BigDecimal longitude;
    /**
     * 店铺纬度
     */
    private BigDecimal latitude;
}
