package com.pioneer.mall.wx.bean.res;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class TpmPointExchangeConfigResVo {

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_point_exchange_config.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_point_exchange_config.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_point_exchange_config.amount
     *
     * @mbg.generated
     */
    private BigDecimal amount;

    /**
     * 优惠券名称
     *
     * @mbg.generated
     */
    private String couponName;

    /**
     * 优惠券图片
     *
     * @mbg.generated
     */
    private String couponPic;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_point_exchange_config.exchange_num
     *
     * @mbg.generated
     */
    private Integer exchangeNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_point_exchange_config.description
     *
     * @mbg.generated
     */
    private String description;


    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_point_exchange_config.start_time
     *
     * @mbg.generated
     */
    private LocalDate startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tpm_point_exchange_config.end_time
     *
     * @mbg.generated
     */
    private LocalDate endTime;

    /**
     * 能否兑换
     */
    private boolean canExchanged;

}
