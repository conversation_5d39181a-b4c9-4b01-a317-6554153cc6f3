package com.pioneer.mall.wx.bean.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/4/2 23:01
 */
@Data
public class TpmUserDetailResVo implements Serializable {
    private Integer id;
    //余额
    private BigDecimal balance;
    //积分
    private BigDecimal points;
    //昵称
    private String nickName;
    //头像
    private String avatar;
    //会员等级
    private String membershipLevel;
    //会员卡号
    private String membershipCode;
    //会员卡面
    private String membershipCardUrl;
    //优惠券计数
    private Integer couponCount;
}
