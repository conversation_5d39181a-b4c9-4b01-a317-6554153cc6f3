package com.pioneer.mall.wx.bean.res;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/18 22:08
 */
@Data
public class TpmWaybillRouteResVo {
    private Integer tpmOrderId;
    // 运单号
    private String mailNo;
    //收货地址
    private String acceptAddress;
    //异常描述
    private String reasonName;
    //客户订单号
    private String orderId;
    //收货时间
    private String acceptTime;
    //备注
    private String remark;
    //操作码
    private String opCode;
    //异常编码
    private String reasonCode;
    //一级状态码
    private String firstStatusCode;
    //一级状态码描述
    private String firstStatusName;
    //二级状态码
    private String secondaryStatusCode;
    //二级状态码描述
    private String secondaryStatusName;
    //添加时间
    private LocalDateTime addTime;
}
