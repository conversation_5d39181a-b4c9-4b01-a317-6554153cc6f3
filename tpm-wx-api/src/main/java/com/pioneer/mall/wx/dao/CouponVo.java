package com.pioneer.mall.wx.dao;

import lombok.Data;

import java.time.LocalDate;

@Data
public class CouponVo {
	private Integer couponUserId;
	private Integer id;
	private String name;
	private String desc;
	private String tag;
	private String min;
	private String discount;
	private LocalDate startTime;
	private LocalDate endTime;
	private Integer goodsType;
	//二维码 status=0的时候再展示
	private String couponQrCode;
	//使用类型
	private String usedTypeDesc;
	//适用商品
	private String applicableGoods;
	private Short status;
	private String statusDesc;
}
