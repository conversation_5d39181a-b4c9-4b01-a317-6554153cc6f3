package com.pioneer.mall.wx.service;

import com.pioneer.mall.db.domain.TpmRegion;
import com.pioneer.mall.db.service.TpmRegionService;
import com.pioneer.mall.db.service.TpmRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.0
 * @date 2017-04-11 11:07
 **/
@Component
public class GetRegionService {

	@Autowired
	private TpmRegionService regionService;

	private static List<TpmRegion> TpmRegions;

	protected List<TpmRegion> getTpmRegions() {
		if (TpmRegions == null) {
			createRegion();
		}
		return TpmRegions;
	}

	private synchronized void createRegion() {
		if (TpmRegions == null) {
			TpmRegions = regionService.getAll();
		}
	}
}
