package com.pioneer.mall.wx.service;

import com.alibaba.fastjson.JSON;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.wx.bean.res.TpmCartResVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/11/5 20:32
 */
@Service
@Slf4j
public class PackingFeeService {

    public BigDecimal calculatePackingFee(List<TpmCartResVo> checkedGoodsList,Integer shopId) {
        log.info("checkedGoodsList:"+JSON.toJSONString(checkedGoodsList));
        List<Integer> drinkPackingFeeCategory = SystemConfig.getDrinkPackingFeeCategory(shopId);
        log.info("打包费分类: " + JSON.toJSONString(drinkPackingFeeCategory));
        int foodPackingFee = calculateFoodPackingFee(checkedGoodsList, drinkPackingFeeCategory);
        int drinkPackingFee = calculateDrinkPackingFee(checkedGoodsList, drinkPackingFeeCategory);
        log.info("食物打包费: " + foodPackingFee + " 元");
        log.info("饮品打包费: " + drinkPackingFee + " 元");
        return BigDecimal.valueOf(foodPackingFee+drinkPackingFee);
    }

    public static int calculateFoodPackingFee(List<TpmCartResVo> checkedGoodsList, List<Integer> drinkPackingFeeCategory) {
        int foodCount = 0;
        for (TpmCartResVo item : checkedGoodsList) {
            if (!drinkPackingFeeCategory.contains(item.getCategoryId())) {
                foodCount += item.getNumber();
            }
        }
        log.info("商品总数: " + foodCount);
        int foodFee = 0;
        if (foodCount > 4) {
            if (foodCount >= 8) {
                foodFee = 2;
            } else {
                foodFee = 1;
            }
        }
        return foodFee;
    }

    public static int calculateDrinkPackingFee(List<TpmCartResVo> checkedGoodsList, List<Integer> drinkPackingFeeCategory) {
        int drinkCount = 0;
        for (TpmCartResVo item : checkedGoodsList) {
            if (drinkPackingFeeCategory.contains(item.getCategoryId())) {
                drinkCount += item.getNumber();
            }
        }
        log.info("商品总数: " + drinkCount);
        int drinkFee = 0;
        if (drinkCount >= 1) {
            if (drinkCount <= 4) {
                drinkFee = 1;
            } else {
                drinkFee = (drinkCount-1) / 4 +1;
            }
        }
        return drinkFee;
    }

    public static void main(String[] args) {
        int drinkCount = 2;

        int drinkFee = 0;
        if (drinkCount >= 4) {
            if (drinkCount <= 8) {
                drinkFee = 1;
            } else {
                // 8件之后，每4件多收1元
                drinkFee = 1 + ((drinkCount - 8) / 4)+1;
            }
        }
        System.out.println(drinkFee);
    }
}
