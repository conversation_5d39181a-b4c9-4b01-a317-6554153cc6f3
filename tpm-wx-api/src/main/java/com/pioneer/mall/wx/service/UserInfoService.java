package com.pioneer.mall.wx.service;

import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.service.TpmUserService;
import com.pioneer.mall.wx.dao.UserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class UserInfoService {
	@Autowired
	private TpmUserService userService;

	public UserInfo getInfo(Integer userId) {
		TpmUser user = userService.findById(userId);
		Assert.state(user != null, "用户不存在");
		UserInfo userInfo = new UserInfo();
		userInfo.setNickName(user.getNickname());
		userInfo.setAvatarUrl(user.getAvatar());
		return userInfo;
	}
}
