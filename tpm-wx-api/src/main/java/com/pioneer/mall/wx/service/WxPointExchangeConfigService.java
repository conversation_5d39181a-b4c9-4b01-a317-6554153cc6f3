package com.pioneer.mall.wx.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.WechatNotifyUtils;
import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCouponUser;
import com.pioneer.mall.db.domain.TpmPointExchangeConfig;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.dto.request.PointPagingReq;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.wx.bean.req.TpmPointExchangeSubmitReqVo;
import com.pioneer.mall.wx.bean.res.TpmPointExchangeConfigResVo;
import com.pioneer.mall.wx.bean.res.TpmPointHisListResVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class WxPointExchangeConfigService {

    @Resource
    private TpmPointService tpmPointService;
    @Resource
    private TpmPointExchangeConfigService tpmPointExchangeConfigService;
    @Resource
    private TpmUserService tpmUserService;
    @Resource
    private TpmCouponService tpmCouponService;
    @Resource
    private TpmCouponUserService tpmCouponUserService;

    public List<TpmPointHisListResVo> getHisList(Integer userId, Integer page, Integer size) {

        PointPagingReq pointPagingReq = new PointPagingReq();
        pointPagingReq.setUserId(Lists.newArrayList(userId));
        pointPagingReq.setPageNum(page);
        pointPagingReq.setPageSize(size);

        List<TpmPointDto> tpmPointDtoList = tpmPointService.paging(pointPagingReq);
        if (CollectionUtils.isEmpty(tpmPointDtoList)) {
            return Collections.emptyList();
        }
        // 转换 tpmPointDtoList TpmPointHisListResVo
        List<TpmPointHisListResVo> pointHisListResVoList = JSON.parseArray(JSONObject.toJSONString(tpmPointDtoList), TpmPointHisListResVo.class);
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月");

        pointHisListResVoList.forEach(t->{
            if (Objects.nonNull(t.getCreateTime())){
                // 使用格式化器格式化日期
                String formattedDate = t.getCreateTime().format(formatter);
                t.setYyyyMM(formattedDate);
            }
        });
        return pointHisListResVoList;
    }

    public List<TpmPointExchangeConfigResVo> getPointExchangeConfigList(Integer userId) {

        // 获取用户积分
        TpmUser tpmUser = tpmUserService.findById(userId);
        if (tpmUser == null) {
            return Collections.emptyList();
        }
        BigDecimal userPoint = tpmUser.getPoints();

        // 获取积分配置
        List<TpmPointExchangeConfig> tpmPointExchangeConfigList = tpmPointExchangeConfigService.getAllPointExchangeConfigList();

        if (CollectionUtils.isEmpty(tpmPointExchangeConfigList)) {
            return Collections.emptyList();
        }
        return tpmPointExchangeConfigList.stream()
                .map(z -> {
                    TpmPointExchangeConfigResVo tpmPointExchangeConfigResVo = new TpmPointExchangeConfigResVo();
                    tpmPointExchangeConfigResVo.setId(z.getId());
                    tpmPointExchangeConfigResVo.setAmount(z.getAmount());

                    tpmPointExchangeConfigResVo.setExchangeNum(z.getExchangeNum());
                    tpmPointExchangeConfigResVo.setName(z.getName());
                    tpmPointExchangeConfigResVo.setDescription(z.getDescription());
                    // 获取优惠券信息
                    TpmCoupon tpmCoupon = tpmCouponService.findById(z.getCouponId());
                    if (tpmCoupon != null) {
                        LocalDate realStartTime = tpmCouponService.getRealStartTime(tpmCoupon);
                        LocalDate realEndTime = tpmCouponService.getRealEndTime(tpmCoupon);
                        tpmPointExchangeConfigResVo.setStartTime(realStartTime);
                        tpmPointExchangeConfigResVo.setEndTime(realEndTime);
                        tpmPointExchangeConfigResVo.setCouponName(tpmCoupon.getName());
                        tpmPointExchangeConfigResVo.setCouponPic(tpmCoupon.getPicUrl());
                    }
                    // 定义是否能够积分兑换
                    tpmPointExchangeConfigResVo.setCanExchanged(userPoint.compareTo(z.getAmount()) >= 0);
                    return tpmPointExchangeConfigResVo;
                }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void exchangeSubmit(TpmPointExchangeSubmitReqVo pointPagingReq) {
        Integer userId = pointPagingReq.getUserId();
        if (Objects.isNull(userId)){
            throw new RuntimeException("用户不存在");
        }
        // 获取用户积分
        TpmUser tpmUser = tpmUserService.findById(userId);
        if (tpmUser == null) {
            throw new RuntimeException("用户不存在");
        }
        BigDecimal userPoint = tpmUser.getPoints();
        TpmPointExchangeConfig tpmPointExchangeConfig = tpmPointExchangeConfigService.findById(pointPagingReq.getId());
        if (tpmPointExchangeConfig == null) {
            throw new RuntimeException("积分兑换配置不存在");
        }
        TpmCoupon tpmCoupon = tpmCouponService.findById(tpmPointExchangeConfig.getCouponId());
        if (Objects.isNull(tpmCoupon)){
            throw new RuntimeException("关联优惠券不存在");
        }
        // 需要的总积分
        BigDecimal totalPoint = tpmPointExchangeConfig.getAmount().multiply(new BigDecimal(pointPagingReq.getNum()));
        if (userPoint.compareTo(totalPoint) < 0) {
            throw new RuntimeException("积分不足");
        }
        // 减少积分 并添加积分记录
        TpmPointDto tpmPointDto = new TpmPointDto();
        tpmPointDto.setUserId(userId);
        tpmPointDto.setAmount(totalPoint.multiply(BigDecimal.valueOf(-1)));
        tpmPointDto.setCreateTime(LocalDateTime.now());
        tpmPointDto.setUpdateTime(LocalDateTime.now());
        tpmPointDto.setDescription("积分兑换:"+tpmPointExchangeConfig.getName()+"扣减:"+totalPoint+"积分");
        tpmPointService.addPoint(tpmPointDto);
        // 添加用户下的优惠券，这里可能用户兑换多张券
        for (int i = 1; i <= pointPagingReq.getNum(); i++) {
            TpmCouponUser couponUser = new TpmCouponUser();
            couponUser.setUserId(userId);
            couponUser.setCouponId(tpmPointExchangeConfig.getCouponId());
            couponUser.setUpdateTime(LocalDateTime.now());
            couponUser.setAddTime(LocalDateTime.now());
            // 优惠券的开始和结束时间
            LocalDate realStartTime = tpmCouponService.getRealStartTime(tpmCoupon);
            LocalDate realEndTime = tpmCouponService.getRealEndTime(tpmCoupon);
            couponUser.setStartTime(realStartTime);
            couponUser.setEndTime(realEndTime);
            couponUser.setCouponQrCode(UUID.randomUUID().toString().replaceAll("-", ""));
            tpmCouponUserService.add(couponUser);
        }
    }
}
