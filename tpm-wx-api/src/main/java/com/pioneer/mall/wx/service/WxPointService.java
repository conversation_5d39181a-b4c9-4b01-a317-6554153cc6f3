package com.pioneer.mall.wx.service;

import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.service.TpmPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class WxPointService {

    @Autowired
    private TpmPointService pointService;

    public List<TpmPointDto> list(Integer userId){
        if (Objects.isNull(userId)) {
            return new ArrayList<>();
        }
        return pointService.listByUserId(userId);
    }
}
