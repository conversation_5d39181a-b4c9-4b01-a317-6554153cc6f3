package com.pioneer.mall.wx.util;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/7/17 21:08
 */
public class PolygonUtils {
    /**
     * 39.942556,116.296214
     * 39.977528,116.296214
     * 39.977528,116.240995
     * 39.942556,116.240995
     * @param args
     */
    public static void main(String[] args) {
        PolygonUtils polygonUtils = new PolygonUtils();
//        List<Point> polygon = Arrays.asList(
//                new Point(39.942556, 116.296214),
//                new Point(39.977528,116.296214),
//                new Point(39.977528,116.240995),
//                new Point(39.942556,116.240995));

        // 定义一个包含10个顶点的复杂非凸多边形
        List<PolygonUtils.Point> polygon = Arrays.asList(
                new PolygonUtils.Point(39.950000, 116.280000), // 顶点1
                new PolygonUtils.Point(39.970000, 116.290000), // 顶点2
                new PolygonUtils.Point(39.965000, 116.310000), // 顶点3
                new PolygonUtils.Point(39.945000, 116.300000), // 顶点4
                new PolygonUtils.Point(39.955000, 116.320000), // 顶点5
                new PolygonUtils.Point(39.975000, 116.330000), // 顶点6
                new PolygonUtils.Point(39.980000, 116.300000), // 顶点7
                new PolygonUtils.Point(39.960000, 116.270000), // 顶点8
                new PolygonUtils.Point(39.940000, 116.260000), // 顶点9
                new PolygonUtils.Point(39.935000, 116.275000)  // 顶点10
        );

        // 测试用例
        List<TestCase> testCases = Arrays.asList(
                // 点在多边形内部
                new TestCase("点在多边形内部", 39.970000, 116.300000, true),
                // 点在多边形外部
                new TestCase("点在多边形外部", 39.990000, 116.350000, false),
                // 点在多边形边上（边：(39.950000, 116.280000) -> (39.970000, 116.290000)）
                new TestCase("点在多边形边上", 39.960000, 116.285000, true),
                // 点在多边形顶点上（顶点：(39.950000, 116.280000)）
                new TestCase("点在多边形顶点上", 39.950000, 116.280000, true),
                // 无效点（纬度超出范围）
                new TestCase("无效纬度", 91.000000, 116.290000, false, true),
                // 无效点（经度超出范围）
                new TestCase("无效经度", 39.960000, 181.000000, false, true),
                // 点在凹陷区域
                new TestCase("点在凹陷区域边缘", 39.950000, 116.310000, true),
                new TestCase("点在凹陷区域内部", 39.955000, 116.310000, true),
                new TestCase("点在凹陷区域外部", 39.945000, 116.310000, false),
                // 点靠近边界但在内部
                new TestCase("点靠近边界但在外部", 39.955000, 116.290000, false),
                // 点靠近边界但在外部
                new TestCase("点靠近边界但在内部", 39.939000, 116.270000, true),
                // 点在另一条边上（边：(39.940000, 116.260000) -> (39.935000, 116.275000)）
                new TestCase("点在另一条边上", 39.937500, 116.267500, true)
        );

        // 运行测试
        for (TestCase testCase : testCases) {
            System.out.println("测试用例: " + testCase.description);
            try {
                boolean result = polygonUtils.isPointInPolygon(testCase.latitude, testCase.longitude, polygon);
                System.out.println("输入: 纬度=" + testCase.latitude + ", 经度=" + testCase.longitude);
                System.out.println("预期结果: " + testCase.expected);
                System.out.println("实际结果: " + result);
                System.out.println("测试" + (result == testCase.expected && !testCase.shouldThrow ? "通过" : "失败"));
            } catch (IllegalArgumentException e) {
                if (testCase.shouldThrow) {
                    System.out.println("预期异常: " + e.getMessage());
                    System.out.println("测试通过");
                } else {
                    System.out.println("意外异常: " + e.getMessage());
                    System.out.println("测试失败");
                }
            }
            System.out.println("------------------------");
        }
    }

    /**
     * Check if a point (lat, lon) lies inside a (possibly non-convex) polygon.
     * Throws IllegalArgumentException for invalid inputs.
     */
    public boolean isPointInPolygon(double latitude, double longitude, List<Point> polygon) {
        // 输入验证
        if (polygon == null || polygon.isEmpty()) {
            throw new IllegalArgumentException("Polygon cannot be null or empty.");
        }
        if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
            throw new IllegalArgumentException("Invalid latitude [-90, 90] or longitude [-180, 180].");
        }
        for (Point p : polygon) {
            if (p == null || p.getLatitude() < -90 || p.getLatitude() > 90 ||
                    p.getLongitude() < -180 || p.getLongitude() > 180) {
                throw new IllegalArgumentException("Invalid polygon point coordinates.");
            }
        }

        int intersectCount = 0;
        int n = polygon.size();
        for (int i = 0; i < n; i++) {
            Point p1 = polygon.get(i);
            Point p2 = polygon.get((i + 1) % n); // 闭合多边形

            // 点在顶点上
            if (Math.abs(latitude - p1.getLatitude()) < 1e-10 && Math.abs(longitude - p1.getLongitude()) < 1e-10) {
                return true;
            }

            // 排除水平边（纬度相同）
            if (Math.abs(p1.getLatitude() - p2.getLatitude()) < 1e-10) {
                continue;
            }

            // 检查射线是否与边相交（只考虑单向交叉）
            if ((p1.getLongitude() <= longitude && p2.getLongitude() > longitude) ||
                    (p2.getLongitude() <= longitude && p1.getLongitude() > longitude)) {
                // 计算交点的纬度
                double intersectLat = p1.getLatitude() + (longitude - p1.getLongitude()) *
                        (p2.getLatitude() - p1.getLatitude()) /
                        (p2.getLongitude() - p1.getLongitude());

                // 点在边上（考虑浮点精度）
                if (Math.abs(intersectLat - latitude) < 1e-10) {
                    return true;
                }

                // 确保点的纬度在边的纬度范围内（包含端点）
                double minLat = Math.min(p1.getLatitude(), p2.getLatitude());
                double maxLat = Math.max(p1.getLatitude(), p2.getLatitude());
                if (latitude < minLat || latitude > maxLat) {
                    continue;
                }

                // 顶点处理：只计数较低顶点的边
                if (Math.abs(intersectLat - latitude) >= 1e-10 && intersectLat <= latitude) {
                    intersectCount++;
                }
            }
        }

        return intersectCount % 2 == 1;
    }

    // 假设的 Point 类
    static class Point {
        private double latitude;
        private double longitude;

        public Point(double latitude, double longitude) {
            this.latitude = latitude;
            this.longitude = longitude;
        }

        public double getLatitude() { return latitude; }
        public double getLongitude() { return longitude; }
    }

    static class TestCase {
        String description;
        double latitude;
        double longitude;
        boolean expected;
        boolean shouldThrow;

        TestCase(String description, double latitude, double longitude, boolean expected) {
            this(description, latitude, longitude, expected, false);
        }

        TestCase(String description, double latitude, double longitude, boolean expected, boolean shouldThrow) {
            this.description = description;
            this.latitude = latitude;
            this.longitude = longitude;
            this.expected = expected;
            this.shouldThrow = shouldThrow;
        }
    }
}
