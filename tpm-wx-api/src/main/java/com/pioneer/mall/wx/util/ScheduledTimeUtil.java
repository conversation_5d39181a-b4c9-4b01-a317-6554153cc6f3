package com.pioneer.mall.wx.util;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 预约时间处理工具类
 * 用于处理 HH:mm 格式的预约时间转换
 */
@Slf4j
public class ScheduledTimeUtil {

    /**
     * 将 HH:mm 格式的时间字符串转换为当天的 LocalDateTime
     * 不会自动调整日期，预约时间必须晚于当前时间
     *
     * @param timeStr 时间字符串，格式为 HH:mm（如 "20:00"）
     * @return LocalDateTime 对象，如果解析失败或时间无效返回 null
     */
    public static LocalDateTime parseScheduledTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }

        try {
            String[] timeParts = timeStr.trim().split(":");

            if (timeParts.length != 2) {
                log.warn("预约时间格式无效，应为 HH:mm 格式: {}", timeStr);
                return null;
            }

            int hour = Integer.parseInt(timeParts[0]);
            int minute = Integer.parseInt(timeParts[1]);

            // 验证时间格式的有效性
            if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
                log.warn("预约时间格式无效，小时或分钟超出范围: {}", timeStr);
                return null;
            }

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime scheduledDateTime = now.withHour(hour).withMinute(minute).withSecond(0).withNano(0);

            // 校验预约时间不能早于当前时间
            if (scheduledDateTime.isBefore(now)) {
                log.warn("预约时间不能早于当前时间: {} < {}", scheduledDateTime, now);
                return null;
            }

            return scheduledDateTime;

        } catch (NumberFormatException e) {
            log.error("预约时间解析失败: {}", timeStr, e);
            return null;
        }
    }

    /**
     * 校验预约时间是否有效（不早于当前时间）
     *
     * @param timeStr 时间字符串，格式为 HH:mm
     * @return true 如果时间有效且不早于当前时间，false 否则
     */
    public static boolean isValidScheduledTime(String timeStr) {
        LocalDateTime scheduledTime = parseScheduledTime(timeStr);
        return scheduledTime != null;
    }

    /**
     * 验证时间格式是否为有效的 HH:mm 格式
     * 
     * @param timeStr 时间字符串
     * @return true 如果格式有效，false 否则
     */
    public static boolean isValidTimeFormat(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return false;
        }
        
        try {
            String[] parts = timeStr.trim().split(":");
            if (parts.length != 2) {
                return false;
            }
            
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            
            return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 将 LocalDateTime 转换为 HH:mm 格式的字符串
     * 
     * @param dateTime LocalDateTime 对象
     * @return HH:mm 格式的时间字符串
     */
    public static String formatToTimeString(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    /**
     * 检查预约时间是否在营业时间内
     * 
     * @param timeStr 预约时间字符串 HH:mm
     * @param openTime 营业开始时间 HH:mm
     * @param closeTime 营业结束时间 HH:mm
     * @return true 如果在营业时间内，false 否则
     */
    public static boolean isWithinBusinessHours(String timeStr, String openTime, String closeTime) {
        if (!isValidTimeFormat(timeStr) || !isValidTimeFormat(openTime) || !isValidTimeFormat(closeTime)) {
            return false;
        }
        
        try {
            int scheduledMinutes = parseTimeToMinutes(timeStr);
            int openMinutes = parseTimeToMinutes(openTime);
            int closeMinutes = parseTimeToMinutes(closeTime);
            
            // 处理跨天营业的情况（如 22:00 - 02:00）
            if (closeMinutes < openMinutes) {
                return scheduledMinutes >= openMinutes || scheduledMinutes <= closeMinutes;
            } else {
                return scheduledMinutes >= openMinutes && scheduledMinutes <= closeMinutes;
            }
        } catch (Exception e) {
            log.error("检查营业时间失败", e);
            return false;
        }
    }

    /**
     * 将 HH:mm 格式的时间转换为分钟数（从00:00开始计算）
     * 
     * @param timeStr 时间字符串 HH:mm
     * @return 分钟数
     */
    private static int parseTimeToMinutes(String timeStr) {
        String[] parts = timeStr.split(":");
        int hour = Integer.parseInt(parts[0]);
        int minute = Integer.parseInt(parts[1]);
        return hour * 60 + minute;
    }

    /**
     * 获取建议的预约时间列表
     * 根据当前时间和营业时间生成可选的预约时间
     * 
     * @param openTime 营业开始时间 HH:mm
     * @param closeTime 营业结束时间 HH:mm
     * @param intervalMinutes 时间间隔（分钟）
     * @param minAdvanceMinutes 最少提前分钟数
     * @return 可选的预约时间列表
     */
    public static java.util.List<String> getSuggestedScheduledTimes(String openTime, String closeTime, 
                                                                   int intervalMinutes, int minAdvanceMinutes) {
        java.util.List<String> suggestedTimes = new java.util.ArrayList<>();
        
        if (!isValidTimeFormat(openTime) || !isValidTimeFormat(closeTime)) {
            return suggestedTimes;
        }
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime earliestTime = now.plusMinutes(minAdvanceMinutes);
            
            int openMinutes = parseTimeToMinutes(openTime);
            int closeMinutes = parseTimeToMinutes(closeTime);
            
            // 处理跨天营业的情况
            if (closeMinutes < openMinutes) {
                closeMinutes += 24 * 60; // 加一天
            }
            
            // 生成时间选项
            for (int minutes = openMinutes; minutes <= closeMinutes; minutes += intervalMinutes) {
                int actualMinutes = minutes % (24 * 60);
                int hour = actualMinutes / 60;
                int minute = actualMinutes % 60;
                
                LocalDateTime optionTime = now.withHour(hour).withMinute(minute).withSecond(0).withNano(0);
                
                // 如果时间已过，调整为明天
                if (optionTime.isBefore(earliestTime)) {
                    optionTime = optionTime.plusDays(1);
                }
                
                // 只添加今天和明天的时间选项
                if (optionTime.isBefore(now.plusDays(2))) {
                    suggestedTimes.add(String.format("%02d:%02d", hour, minute));
                }
            }
            
        } catch (Exception e) {
            log.error("生成建议预约时间失败", e);
        }
        
        return suggestedTimes;
    }

    /**
     * 获取餐饮行业常用的预约时间选项
     * 
     * @return 常用预约时间列表
     */
    public static java.util.List<String> getCommonRestaurantTimes() {
        java.util.List<String> commonTimes = new java.util.ArrayList<>();
        
        // 早餐时间段 (7:00 - 10:00)
        String[] breakfastTimes = {"07:00", "07:30", "08:00", "08:30", "09:00", "09:30", "10:00"};
        
        // 午餐时间段 (11:00 - 14:00)
        String[] lunchTimes = {"11:00", "11:30", "12:00", "12:30", "13:00", "13:30", "14:00"};
        
        // 晚餐时间段 (17:00 - 21:00)
        String[] dinnerTimes = {"17:00", "17:30", "18:00", "18:30", "19:00", "19:30", "20:00", "20:30", "21:00"};
        
        commonTimes.addAll(java.util.Arrays.asList(breakfastTimes));
        commonTimes.addAll(java.util.Arrays.asList(lunchTimes));
        commonTimes.addAll(java.util.Arrays.asList(dinnerTimes));
        
        return commonTimes;
    }

    /**
     * 格式化预约时间为用户友好的显示格式
     * 
     * @param timeStr 时间字符串 HH:mm
     * @return 用户友好的时间显示
     */
    public static String formatForDisplay(String timeStr) {
        if (!isValidTimeFormat(timeStr)) {
            return timeStr;
        }
        
        try {
            String[] parts = timeStr.split(":");
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            
            String period;
            String displayHour;
            
            if (hour == 0) {
                period = "凌晨";
                displayHour = "12";
            } else if (hour < 6) {
                period = "凌晨";
                displayHour = String.valueOf(hour);
            } else if (hour < 12) {
                period = "上午";
                displayHour = String.valueOf(hour);
            } else if (hour == 12) {
                period = "中午";
                displayHour = "12";
            } else if (hour < 18) {
                period = "下午";
                displayHour = String.valueOf(hour - 12);
            } else {
                period = "晚上";
                displayHour = String.valueOf(hour - 12);
            }
            
            if (minute == 0) {
                return String.format("%s%s点", period, displayHour);
            } else {
                return String.format("%s%s点%d分", period, displayHour, minute);
            }
            
        } catch (Exception e) {
            log.error("格式化显示时间失败: {}", timeStr, e);
            return timeStr;
        }
    }
}
