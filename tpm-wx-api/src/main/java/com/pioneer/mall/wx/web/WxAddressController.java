package com.pioneer.mall.wx.web;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.RegexUtil;
import com.pioneer.mall.core.util.WechatNotifyUtils;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.domain.TpmAddress;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.service.TpmAddressService;
import com.pioneer.mall.wx.annotation.LoginUser;
import com.pioneer.mall.wx.bean.req.TpmAddressAddUpdateReqVo;
import com.pioneer.mall.wx.bean.req.TpmAddressDeleteReqVo;
import com.pioneer.mall.wx.bean.req.TpmAddressDetailReqVo;
import com.pioneer.mall.wx.bean.res.TpmAddressResVo;
import com.pioneer.mall.wx.service.GetRegionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 用户收货地址服务
 */
@RestController
@RequestMapping("/wx/address")
@Validated
public class WxAddressController extends GetRegionService {
	private static final Logger logger = LoggerFactory.getLogger(WxAddressController.class);

	@Autowired
	private TpmAddressService addressService;

//	@Autowired
//	private TpmRegionService regionService;

	private final static ArrayBlockingQueue<Runnable> WORK_QUEUE = new ArrayBlockingQueue<>(6);

	private final static RejectedExecutionHandler HANDLER = new ThreadPoolExecutor.CallerRunsPolicy();

	private static ThreadPoolExecutor executorService = new ThreadPoolExecutor(3, 6, 1000, TimeUnit.MILLISECONDS,
			WORK_QUEUE, HANDLER);

	/**
	 * 用户收货地址列表
	 *
	 * @param userId
	 *            用户ID
	 * @return 收货地址列表
	 */
	@GetMapping("list")
	public WxResult<List<TpmAddressResVo>> list(@LoginUser @RequestParam Integer userId,
												@RequestParam(name = "businessType",defaultValue = "1") Integer businessType) {
		logger.info("【请求开始】获取收货地址列表,请求参数,userId：{}", userId);
		if (userId == null) {
			return WxResult.unlogin();
		}
		List<TpmAddress> addressList = addressService.queryByUid(userId,businessType);
		List<TpmAddressResVo> addressVoList = new ArrayList<>(addressList.size());
		for (TpmAddress address : addressList) {
			TpmAddressResVo tpmAddressResVo = new TpmAddressResVo();
			tpmAddressResVo.setId(address.getId());
			tpmAddressResVo.setName(address.getName());
			tpmAddressResVo.setMobile(address.getMobile());
			tpmAddressResVo.setIsDefault(address.getIsDefault());
			tpmAddressResVo.setProvinceName(address.getProvinceName());
			tpmAddressResVo.setCityName(address.getCityName());
			tpmAddressResVo.setAreaName(address.getAreaName());
			tpmAddressResVo.setAddressName(address.getAddressName());
			tpmAddressResVo.setAddressLocation(address.getAddressLocation());
			tpmAddressResVo.setAddressDetail(address.getAddressDetail());
			tpmAddressResVo.setLatitude(address.getLatitude());
			tpmAddressResVo.setLongitude(address.getLongitude());
			addressVoList.add(tpmAddressResVo);
		}
		logger.info("【请求结束】获取收货地址列表,响应结果:{}", JSONObject.toJSONString(addressVoList));
		return WxResult.success(addressVoList);
	}

	/**
	 * 收货地址详情
	 *
	 * @param userId
	 *            用户ID
	 * @param id
	 *            收货地址ID
	 * @return 收货地址详情
	 */
	@PostMapping("detail")
	public WxResult<TpmAddressResVo> detail(@RequestBody TpmAddressDetailReqVo reqVo) {
		logger.info("【请求开始】获取收货地址详情,请求参数,reqVo:{}", JSONObject.toJSONString(reqVo));
		Integer userId = reqVo.getUserId();
		if (userId == null) {
			return WxResult.unlogin();
		}
		Integer id = reqVo.getId();
		if (id == null){
			return WxResult.badArgument();
		}
		TpmAddress address = addressService.findById(id);
		if (address == null) {
			return WxResult.badArgument();
		}
		TpmAddressResVo tpmAddressResVo = new TpmAddressResVo();
		tpmAddressResVo.setId(address.getId());
		tpmAddressResVo.setName(address.getName());
		tpmAddressResVo.setMobile(address.getMobile());
		tpmAddressResVo.setIsDefault(address.getIsDefault());
		tpmAddressResVo.setProvinceId(address.getProvinceId());
		tpmAddressResVo.setProvinceName(address.getProvinceName());
		tpmAddressResVo.setCityId(address.getCityId());
		tpmAddressResVo.setCityName(address.getCityName());
		tpmAddressResVo.setAreaId(address.getAreaId());
		tpmAddressResVo.setAreaName(address.getAreaName());
		tpmAddressResVo.setAddressName(address.getAddressName());
		tpmAddressResVo.setAddressLocation(address.getAddressLocation());
		tpmAddressResVo.setAddressDetail(address.getAddressDetail());
		tpmAddressResVo.setLongitude(address.getLongitude());
		tpmAddressResVo.setLatitude(address.getLatitude());
		logger.info("【请求结束】用户{} 地址id{} 获取收货地址详情,响应结果：{}", userId, id, JSONObject.toJSONString(tpmAddressResVo));
		return WxResult.success(tpmAddressResVo);
	}

	private WxResult validate(TpmAddress address) {
		String name = address.getName();
		if (StringUtils.isEmpty(name)) {
			return WxResult.badArgument();
		}

		// 测试收货手机号码是否正确
		String mobile = address.getMobile();
		if (StringUtils.isEmpty(mobile)) {
			return WxResult.badArgument();
		}
		if (!RegexUtil.isMobileExact(mobile)) {
			return WxResult.error(-1,"手机号格式不对");
		}
		if (Objects.isNull(address.getLongitude()) || Objects.isNull(address.getLatitude())) {
			return WxResult.error(-1, "未选择正确地址");
		}
		if (StringUtils.isEmpty(address.getAddressName()) || StringUtils.isEmpty(address.getAddressLocation())) {
			return WxResult.error(-1, "请选择配送地址");
		}
		if (StringUtils.isEmpty(address.getAddressDetail())) {
			return WxResult.error(-1, "请填写详细地址");
		}

//		Integer pid = address.getProvinceId();
//		if (pid == null) {
//			return WxResult.badArgument();
//		}
//		if (regionService.findById(pid) == null) {
//			return WxResult.badArgumentValue();
//		}
//
//		Integer cid = address.getCityId();
//		if (cid == null) {
//			return WxResult.badArgument();
//		}
//		if (regionService.findById(cid) == null) {
//			return WxResult.badArgumentValue();
//		}
//
//		Integer aid = address.getAreaId();
//		if (aid == null) {
//			return WxResult.badArgument();
//		}
//		if (regionService.findById(aid) == null) {
//			return WxResult.badArgumentValue();
//		}
		Boolean isDefault = address.getIsDefault();
		if (isDefault == null) {
			return WxResult.badArgument();
		}
		return null;
	}

	/**
	 * 添加或更新收货地址
	 *
	 * @param userId
	 *            用户ID
	 * @param address
	 *            用户收货地址
	 * @return 添加或更新操作结果
	 */
	@PostMapping("save")
	public WxResult<Integer> save( @RequestBody TpmAddressAddUpdateReqVo reqVo) {
		logger.info("【请求开始】添加或更新收货地址,请求参数,req:{}", JSONObject.toJSONString(reqVo));
		Integer userId = reqVo.getUserId();
		if (userId == null) {
			return WxResult.unlogin();
		}
		if (Objects.isNull(reqVo.getBusinessType())){
			reqVo.setBusinessType(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode());
		}
		TpmAddress address = new TpmAddress();
		BeanUtils.copyProperties(reqVo,address);
		WxResult error = validate(address);
		if (error != null) {
			return error;
		}

		if (address.getIsDefault()) {// 如果设置本次地址为默认地址，则需要重置其他收货地址的默认选项
			addressService.resetDefault(userId);
		}
		if (Objects.equals(reqVo.getBusinessType(), TpmBusinessTypeEnums.EXPRESS.getCode())){
			//逆解析
			//经度
			String longitude = reqVo.getLongitude();
			//维度
			String latitude = reqVo.getLatitude();
			try {
				String cityId = getCodeFromReverseGeocoding(longitude, latitude);
				logger.info("逆解析地址：{}", cityId);
				address.setCityId(Integer.valueOf(cityId));
			} catch (Exception e) {
				logger.error("逆解析地址失败：{}", e.getMessage(),e);
				WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatShopNotifyUrl(1),null, "逆解析地址失败：" + e.getMessage());
            }
        }
		if (address.getId() == null || address.getId().equals(0)) {
			address.setId(null);
			address.setUserId(userId);
			addressService.add(address);
		} else { // 更新地址
			address.setUserId(userId);
			if (addressService.update(address) == 0) {
				return WxResult.updatedDataFailed();
			}
		}
		logger.info("【请求结束】添加或更新收货地址,响应结果：{}", address.getId());
		return WxResult.success(address.getId());
	}

	@GetMapping("nijiexi")
	public String nijiexi(@RequestParam("zuobiao")String zuobiao) throws Exception {
		String[] split = zuobiao.split(",");
		String latitude = split[0];
		String longitude = split[1];
		String key = "EB7BZ-W7X6J-KL7FS-X7M53-K63S5-WTFLA";// 需要替换为真实的开发密钥
		String url = "https://apis.map.qq.com/ws/geocoder/v1/?location=" + latitude + "," + longitude + "&key=" + key + "&get_poi=0";
		String response = HttpUtil.get(url);
		JSONObject jsonResponse = JSONObject.parseObject(response);
		String city = null;
		String substring = null;
		if (jsonResponse.getInteger("status") == 0) {
			JSONObject result = jsonResponse.getJSONObject("result");
			JSONObject adInfo = result.getJSONObject("ad_info");
			logger.info("adInfo={}", JSONObject.toJSONString(adInfo));
			city = adInfo.getString("city");
			String cityCode = adInfo.getString("city_code");
			String nationCode = adInfo.getString("nation_code");
			if (cityCode.startsWith(nationCode)) {
				substring = cityCode.substring(nationCode.length());
				logger.info("截取后的 city_code: " + substring);
			} else {
				logger.error("city_code 不包含 nation_code，无法进行截取操作");
				throw new Exception("无法获取行政区划代码");
			}
		}
		return city+substring;
	}

	private static String getCodeFromReverseGeocoding(String longitude, String latitude) throws Exception {
		String key = "EB7BZ-W7X6J-KL7FS-X7M53-K63S5-WTFLA";// 需要替换为真实的开发密钥
		String url = "https://apis.map.qq.com/ws/geocoder/v1/?location=" + latitude + "," + longitude + "&key=" + key + "&get_poi=0";
		String response = HttpUtil.get(url);
		JSONObject jsonResponse = JSONObject.parseObject(response);
		if (jsonResponse.getInteger("status") == 0) {
			JSONObject result = jsonResponse.getJSONObject("result");
			JSONObject adInfo = result.getJSONObject("ad_info");
			logger.info("adInfo={}", JSONObject.toJSONString(adInfo));
			String cityCode = adInfo.getString("city_code");
			String nationCode = adInfo.getString("nation_code");
			if (cityCode.startsWith(nationCode)) {
				String substring = cityCode.substring(nationCode.length());
				logger.info("截取后的 city_code: " + substring);
				return substring;
			} else {
				logger.error("city_code 不包含 nation_code，无法进行截取操作");
				throw new Exception("无法获取行政区划代码");
			}
		}
		throw new Exception("逆解析失败");
	}

	/**
	 * 删除收货地址
	 *
	 * @param userId
	 *            用户ID
	 * @param address
	 *            用户收货地址，{ id: xxx }
	 * @return 删除操作结果
	 */
	@PostMapping("delete")
	public WxResult<String> delete(@RequestBody TpmAddressDeleteReqVo reqVo) {
		logger.info("【请求开始】删除收货地址,请求参数,reqVo:{}", JSONObject.toJSONString(reqVo));
		Integer userId = reqVo.getUserId();
		if (userId == null) {
			return WxResult.unlogin();
		}
		Integer id = reqVo.getId();
		if (id == null) {
			return WxResult.badArgument();
		}
		addressService.delete(id);
		logger.info("【请求结束】删除收货地址,响应结果：成功");
		return WxResult.success("删除成功");
	}
}