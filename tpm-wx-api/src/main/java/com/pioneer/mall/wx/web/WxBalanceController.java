package com.pioneer.mall.wx.web;

import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.bean.search.TpmBalanceSearch;
import com.pioneer.mall.db.domain.TpmBalance;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.service.TpmBalanceService;
import com.pioneer.mall.db.service.TpmOrderService;
import com.pioneer.mall.db.service.TpmUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 余额接口controller
 */
//@Api(tags="会员端-余额相关接口")
@RestController
@RequestMapping(value = "/wx/balance")
public class WxBalanceController {

    /**
     * 配置服务接口
     * */
//    private SettingService settingService;

    /**
     * 余额服务接口
     * */
    @Autowired
    private TpmBalanceService tpmBalanceService;

    /**
     * 支付服务接口
     * */
//    private PaymentService paymentService;

    /**
     * 订单服务接口
     * */
    @Autowired
    private TpmOrderService orderService;

    /**
     * 会员服务接口
     */
    @Autowired
    private TpmUserService userService;


    /**
     * 余额明细
     * @param tpmBalanceSearch
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @CrossOrigin
    public WxResult<List<TpmBalance>> list(@RequestBody TpmBalanceSearch tpmBalanceSearch) {
        List<TpmBalance> tpmBalanceList = tpmBalanceService.querySelective(tpmBalanceSearch);
        return WxResult.success(tpmBalanceList);
    }

    @RequestMapping("/getUserBalance")
    @CrossOrigin
    public WxResult<BigDecimal> getUserBalance(@RequestParam(value = "userId") Integer userId) {
        TpmUser tpmUser = userService.findById(userId);
        BigDecimal balance = tpmUser.getBalance();
        return WxResult.success(balance);
    }
}
