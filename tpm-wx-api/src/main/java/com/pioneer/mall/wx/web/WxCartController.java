package com.pioneer.mall.wx.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pioneer.mall.core.config.EveningDiscountConfigDTO;
import com.pioneer.mall.core.system.DiscountService;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.ConvertUtil;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.bean.dto.CartCheckOutCouponInfoDTO;
import com.pioneer.mall.db.bean.dto.TpmGoodsFullInfoDTO;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.dto.TpmShopOpenStatusDTO;
import com.pioneer.mall.db.dto.TpmSpecificationsResultDto;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.enums.TpmTransportTypeEnums;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.util.SaleRangeEnum;
import com.pioneer.mall.wx.annotation.LoginUser;
import com.pioneer.mall.wx.bean.req.*;
import com.pioneer.mall.wx.bean.res.*;
import com.pioneer.mall.wx.service.PackingFeeService;
import com.pioneer.mall.wx.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pioneer.mall.db.util.WxResponseCode.GOODS_NO_STOCK;
import static com.pioneer.mall.db.util.WxResponseCode.GOODS_UNSHELVE;

/**
 * 用户购物车服务
 */
@Slf4j
@RestController
@RequestMapping("/wx/cart")
@Validated
public class WxCartController {

    @Autowired
    private TpmCartService cartService;
    @Autowired
    private TpmGoodsService goodsService;
    @Autowired
    private TpmGoodsProductService productService;
    @Autowired
    private TpmAddressService addressService;
    @Autowired
    private TpmGrouponRulesService grouponRulesService;
    @Autowired
    private TpmCouponUserService couponUserService;
    @Autowired
    private CouponVerifyService couponVerifyService;
    @Autowired
    private TpmBrandService brandService;
    @Autowired
    private TpmFreightStepPriceService tpmFreightStepPriceService;
    @Autowired
    private TpmUserService tpmUserService;
    @Autowired
    private TpmAttributeSpecificationService attributeSpecificationService;
    @Autowired
    private TpmShopHoursService tpmShopHoursService;
    @Autowired
    private TpmShopInfoService tpmShopInfoService;
    @Autowired
    private PackingFeeService packingFeeService;
    @Autowired
    private DiscountService discountService;
    @Autowired
    private TpmExpressConfService tpmExpressConfService;

    /**
     * 用户购物车信息
     *
     * @param userId 用户ID
     * @return 用户购物车信息
     */
    @GetMapping("/index")
    @Transactional(rollbackFor = Exception.class)
    public WxResult<TpmCartIndexResVo> index(@LoginUser @RequestParam Integer userId,
                                             @RequestParam(name = "addressId") Integer addressId,
                                             @RequestParam(name = "shopId") Integer shopId,
                                             @RequestParam(name = "businessType", defaultValue = "1") Integer businessType,
                                             @RequestParam(name = "freightType", defaultValue = "") Integer saleRange) {
        log.info("【请求开始】用户购物车信息列表,请求参数,userId:{}", userId);
        if (userId == null) {
            log.error("获取用户购物车信息列表失败:用户未登录！！！");
            return WxResult.unlogin();
        }
        if (Objects.isNull(shopId)){
            log.error("获取用户购物车信息列表失败:店铺id为空！");
            return WxResult.error("请先选择店铺");
        }
        TpmCartIndexResVo result = new TpmCartIndexResVo();
        if (Objects.nonNull(addressId)){
            TpmAddress checkedAddress = addressService.findById(addressId);
            if (Objects.isNull(checkedAddress)){
                return WxResult.error("收货地址信息有误，请刷新后重试");
            }
            if (!Objects.equals(checkedAddress.getUserId(), userId)){
                return WxResult.error("收货地址信息错误，请重新选择");
            }
            // 尝试获取特定店铺的经纬度信息
            TpmShopInfo shopInfo = tpmShopInfoService.getShopInfoById(shopId);
            String shopLongitude = null;
            String shopLatitude = null;
            if (shopInfo != null) {
                if (shopInfo.getLongitude() != null) {
                    shopLongitude = shopInfo.getLongitude().toString();
                }
                if (shopInfo.getLatitude() != null) {
                    shopLatitude = shopInfo.getLatitude().toString();
                }
            }

            String from = shopLatitude + "," + shopLongitude;
            String to = checkedAddress.getLatitude() + "," + checkedAddress.getLongitude();
            Integer eBicyclingDistance;
            try {
                eBicyclingDistance = MapUtils.getEBicyclingDistance(from, to);
            } catch (Exception e) {
                log.error("计算电动车配送距离失败");
                result.setAllowSubmitOrder(false);
                result.setReason("计算配送距离失败");
                result.setOverDeliveryDistance(true);
                return WxResult.success(result);
            }
            try {
                TpmFreightStepPrice price = tpmFreightStepPriceService.getFreightPriceByShopCodeAndDistance(shopId,eBicyclingDistance);
                if (Objects.nonNull(price)) {
                    result.setFreightPrice(price.getPrice());
                } else {
                    result.setAllowSubmitOrder(false);
                    result.setReason("计算配送距离失败");
                    result.setOverDeliveryDistance(true);
                    return WxResult.success(result);
                }
            } catch (Exception e) {
                log.error("下单失败:" + e.getMessage());
                result.setAllowSubmitOrder(false);
                result.setReason(e.getMessage());
                result.setOverDeliveryDistance(true);
                return WxResult.success(result);
            }
        }
        List<TpmCart> cartList = cartService.queryByUidAndBusinessTypeAndShopId(userId, businessType,shopId);
        List<Integer> goodsIdList = cartList.stream().map(TpmCart::getGoodsId).distinct().collect(Collectors.toList());
//        List<TpmGoods> goodsList = goodsService.findById(goodsIdList);
        List<TpmGoodsFullInfoDTO> fullInfoDTOByIds = goodsService.findFullInfoDTOByIds(goodsIdList);
        List<TpmGoods> goodsList = fullInfoDTOByIds.stream().map(TpmGoodsFullInfoDTO::getTpmGoods).collect(Collectors.toList());
        Map<Integer, TpmGoods> goodsIdMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));
//        Map<Integer, TpmGoodsFullInfoDTO> goodsIdFullInfoMap = fullInfoDTOByIds.stream().collect(Collectors.toMap(fullInfo -> fullInfo.getTpmGoods().getId(), fullInfo -> fullInfo));
        Map<Integer, List<TpmGoodsAttributesDto>> goodsIdAttributesMap = fullInfoDTOByIds.stream().collect(Collectors.toMap(fullInfo -> fullInfo.getTpmGoods().getId(), fullInfo -> fullInfo.getGoodsAttributes()));

        List<TpmCartResVo> tpmCartResVoList = new ArrayList<>();
        for (TpmCart cart : cartList) {
            TpmCartResVo tpmCartResVo = new TpmCartResVo();
            BeanUtils.copyProperties(cart, tpmCartResVo);
            tpmCartResVoList.add(tpmCartResVo);
        }

        List<TpmCartResVo> errorList = new ArrayList<>();
        List<TpmCartResVo> noErrorList = new ArrayList<>();

        for (TpmCartResVo cart : tpmCartResVoList) {
            if (!goodsIdMap.containsKey(cart.getGoodsId())) {
                cart.setErrorMsg("商品已下架");
                errorList.add(cart);
                continue;
            }
            TpmGoods tpmGoods = goodsIdMap.get(cart.getGoodsId());
            if (Objects.isNull(tpmGoods)) {
                cart.setErrorMsg("商品已下架");
                errorList.add(cart);
                continue;
            }
            // 商品的销售范围是否包含当前
            Boolean inDbSaleRange = SaleRangeEnum.pageSaleRangeInDbSaleRange(saleRange, tpmGoods.getSaleRange());
            if (!inDbSaleRange) {
                cart.setErrorMsg("商品不在当前销售范围");
                errorList.add(cart);
                continue;
            }

            if (!tpmGoods.getIsOnSale()) {
                cart.setErrorMsg("商品已下架");
                errorList.add(cart);
                continue;
            }
            if (tpmGoods.getIsSellOut()) {
                cart.setErrorMsg("商品已售罄");
                errorList.add(cart);
                continue;
            }
            if (Objects.isNull(tpmGoods.getInventoryNum()) || tpmGoods.getInventoryNum() <= 0) {
                cart.setErrorMsg("商品已售罄");
                errorList.add(cart);
                continue;
            }
            List<TpmGoodsAttributesDto> tpmGoodsAttributesDtos = goodsIdAttributesMap.get(tpmGoods.getId());
            String specifications = cart.getSpecifications();
            if (Objects.isNull(specifications) && !CollectionUtils.isEmpty(tpmGoodsAttributesDtos)) {
                cart.setErrorMsg("商品属性发生变化");
                errorList.add(cart);
                continue;
            }
            if (Objects.nonNull(specifications) && CollectionUtils.isEmpty(tpmGoodsAttributesDtos)) {
                cart.setErrorMsg("商品属性发生变化");
                errorList.add(cart);
                continue;
            }
            List<TpmGoodsAttributesDto> cartAttributes = new ArrayList<>();
            if (!StringUtils.isEmpty(cart.getSpecifications())) {
                cartAttributes = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
            }
            //比较属性
            String errorMsg = attributeSpecificationService.attributesSpecificationValid(cartAttributes, tpmGoodsAttributesDtos);
            if (!StringUtils.isEmpty(errorMsg)) {
                cart.setErrorMsg(errorMsg);
                errorList.add(cart);
                continue;
            }
            noErrorList.add(cart);
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            List<Integer> idList = errorList.stream().map(TpmCartResVo::getId).distinct().collect(Collectors.toList());
            cartService.deleteById(idList, userId);
        }
        Integer goodsCount = 0;
        BigDecimal goodsAmount = new BigDecimal("0.00");
        EveningDiscountConfigDTO currentEveningDiscount = discountService.getCurrentEveningDiscount(shopId);
        Boolean effectiveEveningDiscount = currentEveningDiscount.getEffective();
        BigDecimal discount = currentEveningDiscount.getDiscount();
        List<Integer> excludeCategory = currentEveningDiscount.getExcludeCategory();
        for (TpmCartResVo cart : noErrorList) {
            // 解析下属性
            BigDecimal specificationsPrice = new BigDecimal("0.00");
            String specifications = cart.getSpecifications();
            if (!StringUtils.isEmpty(specifications)) {
                List<TpmGoodsAttributesDto> goodsAttributesDtos = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
                cart.setGoodsAttributes(goodsAttributesDtos);
                TpmSpecificationsResultDto specificationsResultDto = attributeSpecificationService.getSelectedSpecificationsResult(goodsAttributesDtos);
                if (specificationsResultDto != null) {
                    specificationsPrice = specificationsResultDto.getSpecificationTotalPrice();
                    cart.setSpecifications(specificationsResultDto.getSpecificationsJoin());
                } else {
                    cart.setSpecifications("");
                }
            }
            goodsCount += cart.getNumber();
            TpmGoods tpmGoods = goodsIdMap.get(cart.getGoodsId());
            BigDecimal totalPrice = tpmGoods.getRetailPrice().add(specificationsPrice);
            //有折扣时为原价
            cart.setPrice(totalPrice);
            if (effectiveEveningDiscount && !excludeCategory.contains(tpmGoods.getCategoryId())) {
                //设置折扣
                totalPrice = totalPrice.multiply(discount);
                cart.setDiscountFlag(true);
                cart.setDiscountPrice(totalPrice);
            }
            goodsAmount = goodsAmount.add(totalPrice.multiply(new BigDecimal(cart.getNumber())));
            cart.setGoodsName(tpmGoods.getName());
            cart.setPicUrl(tpmGoods.getPicUrl());
        }
        BigDecimal freightLimit = SystemConfig.getFreightLimit(shopId);
        result.setAllowSubmitOrder(true);
        result.setNote("外送起送价:" + freightLimit + "元");
        if (goodsAmount.compareTo(freightLimit) < 0) {
            result.setAllowSubmitOrder(false);
            result.setReason("未满足起送价" + freightLimit + "元");
        }
        TpmCartIndexResVo.CartTotal cartTotal = new TpmCartIndexResVo.CartTotal();
        cartTotal.setGoodsCount(goodsCount);
        cartTotal.setGoodsAmount(goodsAmount);
        result.setErrorList(errorList);
        result.setCartTotal(cartTotal);
        result.setCartList(noErrorList);

        log.info("【请求结束】用户购物车信息列表,响应结果：{}", JSONObject.toJSONString(result));
        return WxResult.success(result);
    }


    @GetMapping("/shop/index")
    @Transactional(rollbackFor = Exception.class)
    public WxResult<TpmCartShopIndexResVo> shopIndex(@LoginUser @RequestParam Integer userId) {
        log.info("【请求开始】快递商城 用户购物车信息列表,请求参数,userId:{}", userId);
        if (userId == null) {
            log.error("获取用户购物车信息列表失败:用户未登录！！！");
            return WxResult.unlogin();
        }
        Integer businessType = TpmBusinessTypeEnums.EXPRESS.getCode();
        List<TpmCart> cartList = cartService.queryByUid(userId, businessType);
        List<Integer> goodsIdList = cartList.stream().map(TpmCart::getGoodsId).distinct().collect(Collectors.toList());
        List<TpmGoodsFullInfoDTO> fullInfoDTOByIds = goodsService.findFullInfoDTOByIds(goodsIdList);
        List<TpmGoods> goodsList = fullInfoDTOByIds.stream().map(TpmGoodsFullInfoDTO::getTpmGoods).collect(Collectors.toList());
        Map<Integer, TpmGoods> goodsIdMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));
        Map<Integer, List<TpmGoodsAttributesDto>> goodsIdAttributesMap = fullInfoDTOByIds.stream().collect(Collectors.toMap(fullInfo -> fullInfo.getTpmGoods().getId(), fullInfo -> fullInfo.getGoodsAttributes()));

        List<TpmCartResVo> tpmCartResVoList = new ArrayList<>();
        for (TpmCart cart : cartList) {
            TpmCartResVo tpmCartResVo = new TpmCartResVo();
            BeanUtils.copyProperties(cart, tpmCartResVo);
            tpmCartResVoList.add(tpmCartResVo);
        }

        List<TpmCartResVo> errorList = new ArrayList<>();
        List<TpmCartResVo> noErrorList = new ArrayList<>();

        for (TpmCartResVo cart : tpmCartResVoList) {
            if (!goodsIdMap.containsKey(cart.getGoodsId())) {
                cart.setErrorMsg("商品已下架");
                errorList.add(cart);
                continue;
            }
            TpmGoods tpmGoods = goodsIdMap.get(cart.getGoodsId());
            if (Objects.isNull(tpmGoods)) {
                cart.setErrorMsg("商品已下架");
                errorList.add(cart);
                continue;
            }
            cart.setTransportType(tpmGoods.getTransportType());
            if (!tpmGoods.getIsOnSale()) {
                cart.setErrorMsg("商品已下架");
                errorList.add(cart);
                continue;
            }
            if (tpmGoods.getIsSellOut()) {
                cart.setErrorMsg("商品已售罄");
                errorList.add(cart);
                continue;
            }
            if (Objects.isNull(tpmGoods.getInventoryNum()) || tpmGoods.getInventoryNum() <= 0) {
                cart.setErrorMsg("商品已售罄");
                errorList.add(cart);
                continue;
            }
            List<TpmGoodsAttributesDto> tpmGoodsAttributesDtos = goodsIdAttributesMap.get(tpmGoods.getId());
            String specifications = cart.getSpecifications();
            if (Objects.isNull(specifications) && !CollectionUtils.isEmpty(tpmGoodsAttributesDtos)) {
                cart.setErrorMsg("商品属性发生变化");
                errorList.add(cart);
                continue;
            }
            if (Objects.nonNull(specifications) && CollectionUtils.isEmpty(tpmGoodsAttributesDtos)) {
                cart.setErrorMsg("商品属性发生变化");
                errorList.add(cart);
                continue;
            }
            List<TpmGoodsAttributesDto> cartAttributes = new ArrayList<>();
            if (!StringUtils.isEmpty(cart.getSpecifications())) {
                cartAttributes = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
            }
            //比较属性
            String errorMsg = attributeSpecificationService.attributesSpecificationValid(cartAttributes, tpmGoodsAttributesDtos);
            if (!StringUtils.isEmpty(errorMsg)) {
                cart.setErrorMsg(errorMsg);
                errorList.add(cart);
                continue;
            }
            cart.setTransportType(tpmGoods.getTransportType());
            noErrorList.add(cart);
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            List<Integer> idList = errorList.stream().map(TpmCartResVo::getId).distinct().collect(Collectors.toList());
            cartService.deleteById(idList, userId);
        }
        TpmCartShopIndexResVo result = new TpmCartShopIndexResVo();
        if (!CollectionUtils.isEmpty(errorList)) {
            Map<Integer, List<TpmCartResVo>> typeErrorMap = errorList.stream().peek(e -> {
                if (Objects.isNull(e.getTransportType())) {
                    e.setTransportType(TpmTransportTypeEnums.COMMON.getCode());
                }
            }).collect(Collectors.groupingBy(TpmCartResVo::getTransportType));
            typeErrorMap.forEach((transportType, list) -> {
                TpmCartIndexResVo resultRes = new TpmCartIndexResVo();
                if (Objects.equals(transportType, TpmTransportTypeEnums.COLD_CHAIN.getCode())) {
                    resultRes.setErrorList(list);
                    result.setColdChainCart(resultRes);
                } else if (Objects.equals(transportType, TpmTransportTypeEnums.COMMON.getCode())) {
                    resultRes.setErrorList(list);
                    result.setCommonCart(resultRes);
                }
            });
            log.info("【请求结束】快递商城 无正常品，返回错误列表 响应结果:{}", JSON.toJSONString(result));
            return WxResult.success(result);
        }
        Map<Integer, List<TpmCartResVo>> transportTypeCartMap = noErrorList.stream().collect(Collectors.groupingBy(TpmCartResVo::getTransportType));
        transportTypeCartMap.forEach((transportType, list) -> {
            Integer goodsCount = 0;
            BigDecimal goodsAmount = new BigDecimal("0.00");
            for (TpmCartResVo cart : list) {
                // 解析下属性
                BigDecimal specificationsPrice = new BigDecimal("0.00");
                String specifications = cart.getSpecifications();
                if (!StringUtils.isEmpty(specifications)) {
                    List<TpmGoodsAttributesDto> goodsAttributesDtos = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
                    cart.setGoodsAttributes(goodsAttributesDtos);
                    TpmSpecificationsResultDto specificationsResultDto = attributeSpecificationService.getSelectedSpecificationsResult(goodsAttributesDtos);
                    if (specificationsResultDto != null) {
                        specificationsPrice = specificationsResultDto.getSpecificationTotalPrice();
                        cart.setSpecifications(specificationsResultDto.getSpecificationsJoin());
                    } else {
                        cart.setSpecifications("");
                    }
                }
                goodsCount += cart.getNumber();
                TpmGoods tpmGoods = goodsIdMap.get(cart.getGoodsId());
                BigDecimal totalPrice = tpmGoods.getRetailPrice().add(specificationsPrice);
                //有折扣时为原价
                cart.setPrice(totalPrice);
                goodsAmount = goodsAmount.add(totalPrice.multiply(new BigDecimal(cart.getNumber())));
                cart.setGoodsName(tpmGoods.getName());
                cart.setPicUrl(tpmGoods.getPicUrl());
            }
            TpmCartIndexResVo resultRes = new TpmCartIndexResVo();
            BigDecimal freightLimit = new BigDecimal("0.00");
            if (Objects.equals(transportType, TpmTransportTypeEnums.COLD_CHAIN.getCode())) {
                freightLimit = SystemConfig.getExpressColdChainLimit(1);
//                freightLimit = BigDecimal.ZERO;
            } else if (Objects.equals(transportType, TpmTransportTypeEnums.COMMON.getCode())) {
                freightLimit = SystemConfig.getExpressCommonLimit(1);
//                freightLimit = BigDecimal.ZERO;
            }
            resultRes.setNote("起送价:" + freightLimit + "元");
            resultRes.setAllowSubmitOrder(true);
            if (goodsAmount.compareTo(freightLimit) < 0) {
                resultRes.setAllowSubmitOrder(false);
                resultRes.setReason("未满足起送价" + freightLimit + "元");
            }
            TpmCartIndexResVo.CartTotal cartTotal = new TpmCartIndexResVo.CartTotal();
            cartTotal.setGoodsCount(goodsCount);
            cartTotal.setGoodsAmount(goodsAmount);
            resultRes.setErrorList(errorList);
            resultRes.setCartTotal(cartTotal);
            if (Objects.equals(transportType, TpmTransportTypeEnums.COLD_CHAIN.getCode())) {
                List<TpmCartResVo> coldChainCarts = noErrorList.stream().filter(c -> Objects.equals(c.getTransportType(), TpmTransportTypeEnums.COLD_CHAIN.getCode())).collect(Collectors.toList());
                resultRes.setCartList(coldChainCarts);
                result.setColdChainCart(resultRes);
            } else if (Objects.equals(transportType, TpmTransportTypeEnums.COMMON.getCode())) {
                List<TpmCartResVo> coldChainCarts = noErrorList.stream().filter(c -> Objects.equals(c.getTransportType(), TpmTransportTypeEnums.COMMON.getCode())).collect(Collectors.toList());
                resultRes.setCartList(coldChainCarts);
                result.setCommonCart(resultRes);
            }
        });
        log.info("【请求结束】快递商城 用户购物车信息列表,响应结果：{}", JSONObject.toJSONString(result));
        return WxResult.success(result);
    }

    /**
     * 加入商品到购物车
     * <p>
     * 如果已经存在购物车货品，则增加数量； 否则添加新的购物车货品项。
     *
     * @param reqVo 用户ID
     * @return 加入购物车操作结果
     */
    @PostMapping("add")
//    public WxResult<Integer> add(@LoginUser @RequestParam Integer userId, @RequestBody TpmCart cart) {
    public WxResult<Integer> add(@RequestBody TpmCartAddReqVo reqVo) {
        log.info("【请求开始】加入商品到购物车,请求参数,cart:{}", JSONObject.toJSONString(reqVo));
        if (reqVo == null) {
            return WxResult.badArgument();
        }
        if (Objects.isNull(reqVo.getShopId())){
            return WxResult.error("请先选择店铺");
        }
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("加入商品到购物车失败:用户未登录！！！");
            return WxResult.unlogin();
        }

//        Integer productId =reqVo.getProductId();
        Integer shopId = reqVo.getShopId();
        Integer number = reqVo.getNumber();
        Integer goodsId = reqVo.getGoodsId();
        Integer businessType = reqVo.getBusinessType();
        if (Objects.isNull(businessType)) {
            businessType = TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode();
        }

//        if (!ObjectUtils.allNotNull(productId, number, goodsId)) {
        if (!ObjectUtils.allNotNull(number, goodsId)) {
            return WxResult.badArgument();
        }

        if (number <= 0) {
            log.error("加入商品到购物车失败:{}", "数量必须大于0");
            return WxResult.error("数量必须大于0");
        }

        // 判断商品是否可以购买，支持店铺维度
        TpmGoods goods = goodsService.findById(goodsId, shopId);
        if (goods == null || !goods.getIsOnSale()) {
            log.error("加入商品到购物车失败:{}", GOODS_UNSHELVE.desc());
            return WxResult.error(GOODS_UNSHELVE);
        }

        if (goods.getIsSellOut() || goods.getInventoryNum() <= 0) {
            log.error("加入商品到购物车失败:{}", GOODS_NO_STOCK.desc());
            return WxResult.error(GOODS_NO_STOCK);
        }

        List<TpmGoodsAttributesDto> goodsAttributesList = reqVo.getGoodsAttributes();
        String validMsg = attributeSpecificationService.attributesSpecificationValid(goodsAttributesList, goodsId);
        if (!StringUtils.isEmpty(validMsg)) {
            return WxResult.error(validMsg);
        }

//        TpmGoodsProduct product = productService.findById(productId);
        // 判断购物车中是否存在此规格商品，支持店铺维度
        TpmCart existCart = cartService.queryExist(goodsId, userId, goodsAttributesList, businessType, shopId);
        if (existCart == null) {
//            // 取得规格的信息,判断规格库存
//            if (product == null || number > product.getNumber()) {
//                log.error("加入商品到购物车失败:{}", GOODS_NO_STOCK.desc());
//                return WxResult.error(GOODS_NO_STOCK);
//            }
            TpmCart cart = new TpmCart();
            cart.setId(null);
            cart.setGoodsSn(goods.getGoodsSn());
            cart.setBrandId(goods.getBrandId());// 新增入驻商户
            cart.setGoodsName((goods.getName()));
            cart.setPicUrl(goods.getPicUrl());
            cart.setPrice(goods.getRetailPrice());
            cart.setBusinessType(businessType);
            cart.setShopId(shopId); // 设置店铺ID，支持多店铺模式
            // 拼接下选择的规格，并获取规格价格
            this.setCartAttributesSpecifications(cart, goodsAttributesList);
            cart.setGoodsId(goodsId);
            cart.setNumber(number.shortValue());
//            cart.setSpecifications(goods.getSpecifications());
            cart.setUserId(userId);
            cart.setChecked(true);
            cartService.add(cart);
        } else {
            // 取得规格的信息,判断规格库存
            int num = existCart.getNumber() + number;
//            if (num > product.getNumber()) {
//                log.error("加入商品到购物车失败:{}", GOODS_NO_STOCK.desc());
//                return WxResult.error(GOODS_NO_STOCK);
//            }
            existCart.setNumber((short) num);
            this.setCartAttributesSpecifications(existCart, goodsAttributesList);
            if (cartService.updateById(existCart) == 0) {
                log.error("加入商品到购物车失败:更新购物车信息失败!");
                return WxResult.updatedDataFailed();
            }
        }
        log.info("【请求结束】加入商品到购物车成功!");
        return goodscount(userId,shopId, businessType);
    }

    public void setCartAttributesSpecifications(TpmCart existCart, List<TpmGoodsAttributesDto> goodsAttributesList) {
        // 拼接下选择的规格，并获取规格价格
        TpmSpecificationsResultDto specificationsResultDto = attributeSpecificationService.getSelectedSpecificationsResult(goodsAttributesList);
        if (specificationsResultDto != null) {
            BigDecimal specificationTotalPrice = specificationsResultDto.getSpecificationTotalPrice();
            if (specificationTotalPrice != null) {
                existCart.setPrice(existCart.getPrice().add(specificationTotalPrice));
            }
        }
        if (!CollectionUtils.isEmpty(goodsAttributesList)) {
            existCart.setSpecifications(JSON.toJSONString(goodsAttributesList));
        }
    }


    /**
     * 立即购买
     * <p>
     * 和add方法的区别在于： 1. 如果购物车内已经存在购物车货品，前者的逻辑是数量添加，这里的逻辑是数量覆盖 2.
     * 添加成功以后，前者的逻辑是返回当前购物车商品数量，这里的逻辑是返回对应购物车项的ID
     *
     * @param userId 用户ID
     * @param cart   购物车商品信息， { goodsId: xxx, productId: xxx, number: xxx }
     * @return 立即购买操作结果
     */
    @Deprecated
    @PostMapping("fastadd")
    public WxResult<Integer> fastadd(@LoginUser Integer userId, @RequestBody TpmCart cart) {
        log.info("【请求开始】立即购买,请求参数,userId:{},cart:{}", userId, JSONObject.toJSONString(cart));

        if (userId == null) {
            log.error("立即购买:用户未登录！！！");
            return WxResult.unlogin();
        }
        if (cart == null) {
            return WxResult.badArgument();
        }

        Integer productId = cart.getProductId();
        Integer number = cart.getNumber().intValue();
        Integer goodsId = cart.getGoodsId();
        if (!ObjectUtils.allNotNull(productId, number, goodsId)) {
            return WxResult.badArgument();
        }

        // 判断商品是否可以购买
        TpmGoods goods = goodsService.findById(goodsId);
        if (goods == null || !goods.getIsOnSale()) {
            log.error("立即购买失败:{}", GOODS_UNSHELVE.desc());
            return WxResult.error(GOODS_UNSHELVE);
        }

        TpmGoodsProduct product = productService.findById(productId);
        // 判断购物车中是否存在此规格商品
        TpmCart existCart = cartService.queryExist(goodsId, productId, userId);
        if (existCart == null) {
            // 取得规格的信息,判断规格库存
            if (product == null || number > product.getNumber()) {
                log.error("立即购买失败:{}", GOODS_NO_STOCK.desc());
                return WxResult.error(GOODS_NO_STOCK);
            }

            cart.setId(null);
            cart.setGoodsSn(goods.getGoodsSn());
            cart.setBrandId(goods.getBrandId());// 新增入驻商户
            cart.setGoodsName((goods.getName()));
            cart.setPicUrl(goods.getPicUrl());
            cart.setPrice(product.getPrice());
//            cart.setSpecifications(product.getSpecifications());
            cart.setUserId(userId);
            cart.setChecked(true);
            cartService.add(cart);
        } else {
            // 取得规格的信息,判断规格库存
            int num = number;
            if (num > product.getNumber()) {
                log.error("立即购买失败:{}", GOODS_NO_STOCK.desc());
                return WxResult.error(GOODS_NO_STOCK);
            }
            existCart.setNumber((short) num);
            if (cartService.updateById(existCart) == 0) {
                log.error("立即购买失败:更新购物车信息失败!");
                return WxResult.updatedDataFailed();
            }
        }
        log.info("【请求结束】立即购买成功!");
        return WxResult.success(existCart != null ? existCart.getId() : cart.getId());
    }

    @PostMapping("fastAddNumber")
    public WxResult<BigDecimal> fastAddNumber(@RequestBody TpmCartUpdateReqVo reqVo) {
        log.info("【请求开始】修改购物车,请求参数,cart:{}", JSONObject.toJSONString(reqVo));
        if (reqVo == null) {
            return WxResult.badArgument();
        }
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("修改购物车:用户未登录！！！");
            return WxResult.unlogin();
        }

        Integer number = reqVo.getNumber();
        Integer goodsId = reqVo.getGoodsId();
        Integer businessType = reqVo.getBusinessType();
        if (Objects.isNull(businessType)) {
            businessType = TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode();
        }
        if (!ObjectUtils.allNotNull(number, goodsId)) {
            return WxResult.badArgument();
        }
        boolean add = false;
        // 判断是否存在该订单
        // 如果不存在，直接返回错误
        List<TpmCart> tpmCarts = cartService.queryByUid(userId, businessType);
        if (CollectionUtils.isEmpty(tpmCarts)) {
            add = true;
        } else {
            TpmCart tpmCart = tpmCarts.stream().filter(t -> Objects.equals(t.getGoodsId(), goodsId)).findAny().orElse(null);
            if (Objects.isNull(tpmCart)) {
                add = true;
            } else {
                reqVo.setId(tpmCart.getId());
                this.update(reqVo);
            }
        }
        if (add && number > 0) {
            TpmCartAddReqVo tpmCartAddReqVo = new TpmCartAddReqVo();
            tpmCartAddReqVo.setUserId(userId);
            tpmCartAddReqVo.setGoodsId(goodsId);
            tpmCartAddReqVo.setNumber(number);
            this.add(tpmCartAddReqVo);
        }
        return WxResult.success("新增成功");
    }

    /**
     * 修改购物车商品货品数量
     *
     * @param reqVo
     * @return 修改结果
     */
    @PostMapping("update")
    public WxResult<String> update(@RequestBody TpmCartUpdateReqVo reqVo) {
        log.info("【请求开始】修改购物车,请求参数,cart:{}", JSONObject.toJSONString(reqVo));
        if (reqVo == null) {
            return WxResult.badArgument();
        }
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("修改购物车:用户未登录！！！");
            return WxResult.unlogin();
        }

        Integer number = reqVo.getNumber();
//        Integer productId = reqVo.getProductId().intValue();
        Integer goodsId = reqVo.getGoodsId();
        Integer id = reqVo.getId();
//        if (!ObjectUtils.allNotNull(id, productId, number, goodsId)) {
//            return WxResult.badArgument();
//        }
        if (!ObjectUtils.allNotNull(id, number, goodsId)) {
            return WxResult.badArgument();
        }

        // 判断是否存在该订单
        // 如果不存在，直接返回错误
        TpmCart existCart = cartService.findById(id);
        if (existCart == null) {
            return WxResult.badArgumentValue();
        }

        // 判断goodsId和productId是否与当前cart里的值一致
        if (!existCart.getGoodsId().equals(goodsId)) {
            return WxResult.badArgumentValue();
        }
//        if (!existCart.getProductId().equals(productId)) {
//            return WxResult.badArgumentValue();
//        }

        if (Objects.equals(0, number)) {
            cartService.deleteById(id, userId);
            return WxResult.success("购物车删除成功");
        }

        // 判断商品是否可以购买
        TpmGoods goods = goodsService.findById(goodsId);
        if (goods == null || !goods.getIsOnSale()) {
            log.error("修改购物车失败:{}", GOODS_UNSHELVE.desc());
            return WxResult.error(GOODS_UNSHELVE);
        }

//        // 取得规格的信息,判断规格库存
//        TpmGoodsProduct product = productService.findById(productId);
//        if (product == null || product.getNumber() < number) {
//            log.error("修改购物车失败:{}", GOODS_NO_STOCK.desc());
//            return WxResult.error(GOODS_NO_STOCK);
//        }

        existCart.setNumber(number.shortValue());
        if (cartService.updateById(existCart) == 0) {
            log.error("修改购物车失败:更新购物车信息失败!");
            return WxResult.updatedDataFailed();
        }
        log.info("【请求结束】修改购物车成功!");
        return WxResult.success("修改购物车成功");
    }

    /**
     * 购物车商品货品勾选状态
     * <p>
     * 如果原来没有勾选，则设置勾选状态；如果商品已经勾选，则设置非勾选状态。
     *
     * @param reqVo
     * @return 购物车信息
     */
    @PostMapping("checked")
    public WxResult<TpmCartIndexResVo> checked(@RequestBody TpmCartCheckedReqVo reqVo,
                                               @RequestParam(name = "addressId") Integer addressId,
                                               @RequestParam(name = "saleRange", defaultValue = "") Integer saleRange,
                                               @RequestParam(name = "businessType", defaultValue = "1") Integer businessType
    ) {
        log.info("【请求开始】勾选购物车商品,请求参数,cart:{}", JSONObject.toJSONString(reqVo));

        if (reqVo == null) {
            return WxResult.badArgument();
        }
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("勾选购物车商品失败:用户未登录！！！");
            return WxResult.unlogin();
        }
        Integer shopId = reqVo.getShopId();
        if (Objects.isNull(shopId)){
            return WxResult.error("请先选择店铺");
        }
//
//        List<Integer> productIds = JacksonUtil.parseIntegerList(body, "productIds");
//        if (productIds == null) {
//            return WxResult.badArgument();
//        }
        List<Integer> idList = reqVo.getIdList();

        Integer checkValue = reqVo.getIsChecked();
        if (checkValue == null) {
            return WxResult.badArgument();
        }
        Boolean isChecked = (checkValue == 1);
        try {
            cartService.updateCheckById(userId, idList, isChecked);
        } catch (Exception e) {
            log.error("勾选购物车商品失败:更新购物车商品的勾选状态失败！");
            e.printStackTrace();
        }

        log.info("【请求结束】勾选购物车商品成功!");
        return index(userId,addressId,shopId, businessType, saleRange);
    }

    /**
     * 购物车商品删除
     *
     * @param userId 用户ID
     * @param body   购物车商品信息， { productIds: xxx }
     * @return 购物车信息 成功则 { errno: 0, errmsg: '成功', data: xxx } 失败则 { errno: XXX,
     * errmsg: XXX }
     */
    @PostMapping("delete")
    public WxResult<TpmCartIndexResVo> delete(@RequestBody TpmCartDeleteReqVo reqVo,
                                              @RequestParam(name = "addressId") Integer addressId,
                                              @RequestParam(name = "saleRange", defaultValue = "") Integer saleRange,
                                              @RequestParam(name = "businessType", defaultValue = "1") Integer businessType
    ) {
        log.info("【请求开始】购物车商品删除,请求参数,cart:{}", JSONObject.toJSONString(reqVo));

        if (reqVo == null) {
            return WxResult.badArgument();
        }
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("购物车商品删除:用户未登录！！！");
            return WxResult.unlogin();
        }
        Integer shopId = reqVo.getShopId();
        if (Objects.isNull(shopId)){
            return WxResult.error("请先选择店铺");
        }

//        List<Integer> productIds = JacksonUtil.parseIntegerList(body, "productIds");

//        if (productIds == null || productIds.size() == 0) {
//            return WxResult.badArgument();
//        }
        Integer id = reqVo.getId();
        try {
            cartService.deleteById(id, userId);
        } catch (Exception e) {
            log.error("购物车商品删除失败:操作数据库删除用户商品失败！");
            e.printStackTrace();
        }

//        log.info("【请求结束】购物车商品删除成功:清理的productIds:{}", JSONObject.toJSONString(productIds));
        return index(userId,addressId,shopId, businessType, saleRange);
    }

    @PostMapping("deleteAll")
    public WxResult<TpmCartIndexResVo> deleteAll(@RequestBody TpmCartDeleteReqVo reqVo,
                                                 @RequestParam(name = "addressId", required = false) Integer addressId,
                                                 @RequestParam(name = "saleRange", defaultValue = "") Integer saleRange,
                                                 @RequestParam(name = "businessType", defaultValue = "1") Integer businessType) {
        log.info("【请求开始】购物车商品清空,请求参数,cart:{}", JSONObject.toJSONString(reqVo));

        if (reqVo == null) {
            return WxResult.badArgument();
        }
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("购物车商品删除:用户未登录！！！");
            return WxResult.unlogin();
        }
        Integer shopId = reqVo.getShopId();
        if (Objects.isNull(shopId)){
            return WxResult.error("请先选择店铺");
        }

        try {
            cartService.deleteByUserIdAndBusinessType(userId, businessType);
        } catch (Exception e) {
            log.error("购物车商品删除失败:操作数据库删除用户商品失败！error={}", e.getMessage(), e);
        }

        return index(userId,addressId,shopId, businessType, saleRange);
    }

    /**
     * 购物车商品货品数量
     * <p>
     * 如果用户没有登录，则返回空数据。
     *
     * @param userId 用户ID
     * @return 购物车商品货品数量
     */
    @GetMapping("goodscount")
    public WxResult<Integer> goodscount(@LoginUser @RequestParam("userId") Integer userId,
                                        @RequestParam(name = "shopId") Integer shopId,
                                        @RequestParam(name = "businessType", defaultValue = "1") Integer businessType) {
        log.info("【请求开始】登录用户购物车商品数量,请求参数,userId:{}", userId);

        if (userId == null) {// 如果用户未登录，则直接显示购物商品数量为0
            return WxResult.success(0);
        }
        if (shopId == null) {
            return WxResult.error("请先选择店铺");
        }
        int goodsCount = 0;
        try {
            List<TpmCart> cartList = cartService.queryByUidAndBusinessTypeAndShopId(userId, businessType,shopId);
            for (TpmCart cart : cartList) {
                goodsCount += cart.getNumber();
            }
        } catch (Exception e) {
            log.error("获取登录用户购物车商品数量失败！", e);
        }

        log.info("【请求结束】获取登录用户购物车商品数量成功:商品总数：{}", goodsCount);
        return WxResult.success(goodsCount);
    }

    /**
     * 购物车下单
     *
     * @param userId    用户ID
     * @param cartId    购物车商品ID： 如果购物车商品ID是空，则下单当前用户所有购物车商品； 如果购物车商品ID非空，则只下单当前购物车商品。
     * @param addressId 收货地址ID： 如果收货地址ID是空，则查询当前用户的默认地址。
     * @param couponId  优惠券ID： 如果优惠券ID是空，则自动选择合适的优惠券。
     * @return 购物车操作结果
     */
    @PostMapping("checkout")
    public WxResult<TpmCartCheckoutResVo> checkout(@RequestBody TpmCartCheckOutReqVo reqVo) {
        log.info("【请求开始】用户购物车下单,请求参数,userId:{} reqVo={}", reqVo.getUserId(), JSON.toJSONString(reqVo));
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("用户购物车下单失败:用户未登录！！！");
            return WxResult.unlogin();
        }
        Integer shopId = reqVo.getShopId();
        if (Objects.isNull(shopId)){
            return WxResult.error("请先选择店铺");
        }
        TpmUser tpmUser = tpmUserService.findById(userId);
        if (Objects.isNull(tpmUser)) {
            return WxResult.userNotExist();
        }
//        addressService.findDefault(userId);
        // 如果仍然没有地址，则是没有收获地址
        // 返回一个空的地址id=0，这样前端则会提醒添加地址
//        if (checkedAddress == null) {
//            checkedAddress = new TpmAddress();
//            checkedAddress.setId(0);
//        }
        // 商品价格
        List<TpmCart> _checkedGoodsList = cartService.findById(reqVo.getCartIdList());
        if (CollectionUtils.isEmpty(_checkedGoodsList)) {
            return WxResult.error("购物车商品信息有误，请刷新后重试");
        }
        // 转换下对象
        List<TpmCartResVo> checkedGoodsList = new ArrayList<>();
        _checkedGoodsList.stream().forEach(cart -> {
            TpmCartResVo tpmCartResVo = new TpmCartResVo();
            BeanUtils.copyProperties(cart, tpmCartResVo);
            checkedGoodsList.add(tpmCartResVo);
        });
        boolean anyMatch = checkedGoodsList.stream().anyMatch(checkedGoods -> !Objects.equals(checkedGoods.getUserId(), userId));
        if (anyMatch) {
            return WxResult.error("购物车商品信息有误，请刷新后重试");
        }

        // 验证购物车商品是否属于指定的店铺（多店铺模式支持）
        boolean shopMismatch = checkedGoodsList.stream().anyMatch(cart ->
            cart.getShopId() != null && !Objects.equals(cart.getShopId(), shopId));
        if (shopMismatch) {
            return WxResult.error("购物车中包含其他店铺的商品，请重新选择");
        }
        TpmShopOpenStatusDTO shopOpenStatus = tpmShopHoursService.getShopOpenStatus(shopId);
        if (Objects.isNull(shopOpenStatus)) {
            log.error("提交订单详情失败：店铺未配置营业时间!");
            return WxResult.error(-1, "店铺未配置营业时间");
        }
        if (!shopOpenStatus.getOpen()) {
            String statusDesc = shopOpenStatus.getStatusDesc();
            if (Objects.isNull(statusDesc) || StringUtils.isEmpty(statusDesc)) {
                statusDesc = "店铺未配置营业时间";
            }
            log.error("提交订单详情失败：{}!", statusDesc);
            return WxResult.error(-1, statusDesc);
        }
        TpmCartCheckoutResVo tpmCartCheckoutResVo = new TpmCartCheckoutResVo();

        tpmCartCheckoutResVo.setTelephone(tpmUser.getMobile());
        tpmCartCheckoutResVo.setAllowSubmitOrder(true);

        // 获取店铺特定的晚间折扣配置
        EveningDiscountConfigDTO currentEveningDiscount = discountService.getCurrentEveningDiscount(shopId);
        Boolean effectiveEveningDiscount = currentEveningDiscount.getEffective();
        BigDecimal discount = currentEveningDiscount.getDiscount();
        List<Integer> excludeCategory = currentEveningDiscount.getExcludeCategory();

        BigDecimal goodsTotalPrice = new BigDecimal("0.00");// 商品总价 （包含团购减免，即减免团购后的商品总价，多店铺需将所有商品相加）
        BigDecimal totalFreightPrice = new BigDecimal("0.00");// 总配送费 （单店铺模式一个，多店铺模式多个配送费的总和）
        BigDecimal packingFee = new BigDecimal("0.00"); //打包费
        List<Integer> collect = checkedGoodsList.stream().map(TpmCart::getGoodsId).distinct().collect(Collectors.toList());

        // 使用支持shopId的商品查询方法，获取店铺特定的商品信息
        List<TpmGoods> goodsList = new ArrayList<>();
        for (Integer goodsId : collect) {
            TpmGoods goods = goodsService.findById(goodsId);
            if (goods != null) {
                goodsList.add(goods);
            }
        }
        Map<Integer, TpmGoods> idGoodsMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));
        for (TpmCartResVo cart : checkedGoodsList) {
            if (idGoodsMap.containsKey(cart.getGoodsId())) {
                // 解析下属性
                BigDecimal specificationsPrice = new BigDecimal(0.00);
                String specifications = cart.getSpecifications();
                if (!StringUtils.isEmpty(specifications)) {
                    List<TpmGoodsAttributesDto> goodsAttributesDtos = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
                    TpmSpecificationsResultDto specificationsResultDto = attributeSpecificationService.getSelectedSpecificationsResult(goodsAttributesDtos);
                    if (specificationsResultDto != null) {
                        specificationsPrice = specificationsResultDto.getSpecificationTotalPrice();
                        cart.setSpecifications(specificationsResultDto.getSpecificationsJoin());
                    } else {
                        cart.setSpecifications("");
                    }
                }
                TpmGoods tpmGoods = idGoodsMap.get(cart.getGoodsId());
                if (Objects.nonNull(tpmGoods)) {
                    // 商品的销售范围是否包含当前
                    Boolean inDbSaleRange = SaleRangeEnum.pageSaleRangeInDbSaleRange(Integer.valueOf(reqVo.getFreightType()), tpmGoods.getSaleRange());
                    if (!inDbSaleRange) {
                        SaleRangeEnum saleRangeEnum = SaleRangeEnum.getEnumByPageSaleRange(Integer.valueOf(reqVo.getFreightType()));
                        if (saleRangeEnum != null) {
                            return WxResult.error("商品:" + tpmGoods.getName() + "不允许" + saleRangeEnum.getDesc());
                        }
                    }
                    //如果分类不在免配送费的类目中 则收取一块钱
                    BigDecimal retailPrice = tpmGoods.getRetailPrice().add(specificationsPrice);
                    cart.setPrice(retailPrice);
                    if (effectiveEveningDiscount && !excludeCategory.contains(tpmGoods.getCategoryId())) {
                        //设置折扣
                        retailPrice = retailPrice.multiply(discount);
                        cart.setDiscountFlag(true);
                        cart.setDiscountPrice(retailPrice);
                    }
                    goodsTotalPrice = goodsTotalPrice.add(retailPrice.multiply(new BigDecimal(cart.getNumber())));
                    cart.setGoodsName(tpmGoods.getName());
                    cart.setPicUrl(tpmGoods.getPicUrl());
                    cart.setCategoryId(tpmGoods.getCategoryId());
                } else {
                    return WxResult.error("购物车商品信息有误，请删除后重试");
//                    goodsTotalPrice = goodsTotalPrice.add(cart.getPrice().multiply(new BigDecimal(cart.getNumber())));
                }
            } else {
                return WxResult.error("购物车商品信息有误，请删除后重试");
//                goodsTotalPrice = goodsTotalPrice.add(cart.getPrice().multiply(new BigDecimal(cart.getNumber())));
            }
        }
        tpmCartCheckoutResVo.setGoodsTotalPrice(goodsTotalPrice);
        tpmCartCheckoutResVo.setCheckedGoodsList(checkedGoodsList);
        //堂食不计算打包费
        if (!Objects.equals(reqVo.getFreightType(), (byte) 2)) {
            packingFee = packingFeeService.calculatePackingFee(checkedGoodsList,shopId);
            tpmCartCheckoutResVo.setPackingFee(packingFee);
        } else {
            tpmCartCheckoutResVo.setPackingFee(BigDecimal.ZERO);
        }
        if (Objects.equals(reqVo.getFreightType(), (byte) 0)) {
            // 使用支持shopId的配置方法获取店铺特定的起送价
            BigDecimal freightLimit = SystemConfig.getFreightLimit(shopId);
            if (goodsTotalPrice.compareTo(freightLimit) < 0) {
                tpmCartCheckoutResVo.setAllowSubmitOrder(false);
                tpmCartCheckoutResVo.setReason("下单失败:未满足起送价" + freightLimit + "元");
                return WxResult.success(tpmCartCheckoutResVo);
            }
        }
        // 收货地址
        if (Objects.equals(reqVo.getFreightType(), (byte) 0)) {
            if (Objects.isNull(reqVo.getAddressId())) {
                totalFreightPrice = BigDecimal.ZERO;
            } else {
                TpmAddress checkedAddress = addressService.findById(reqVo.getAddressId());
                tpmCartCheckoutResVo.setCheckedAddress(checkedAddress);
                // 计算收件地址经纬度与商家的距离并在价格区间中获取报价
                // 使用支持shopId的配置方法
                if (SystemConfig.isAutoFreightCalculation(shopId)) {
                    // 优先从店铺信息服务获取店铺特定的经纬度
                    String shopLongitude = null;
                    String shopLatitude = null;

                    // 尝试获取特定店铺的经纬度信息
                    TpmShopInfo shopInfo = tpmShopInfoService.getShopInfoById(shopId);
                    if (shopInfo != null) {
                        if (shopInfo.getLongitude() != null) {
                            shopLongitude = shopInfo.getLongitude().toString();
                        }
                        if (shopInfo.getLatitude() != null) {
                            shopLatitude = shopInfo.getLatitude().toString();
                        }
                    }

//                    // 如果店铺没有配置经纬度，则使用默认配置或系统配置
//                    if (StringUtils.isEmpty(shopLongitude)) {
//                        shopLongitude = tpmShopInfoService.getLongitudeByDefaultShopInfo();
//                        if (StringUtils.isEmpty(shopLongitude)) {
//                            shopLongitude = SystemConfig.getBinjiangLongitude(shopId);
//                        }
//                    }
//                    if (StringUtils.isEmpty(shopLatitude)) {
//                        shopLatitude = tpmShopInfoService.getLatitudeByDefaultShopInfo();
//                        if (StringUtils.isEmpty(shopLatitude)) {
//                            shopLatitude = SystemConfig.getBinjiangLatitude(shopId);
//                        }
//                    }

                    String from = shopLatitude + "," + shopLongitude;
                    String to = checkedAddress.getLatitude() + "," + checkedAddress.getLongitude();
                    Integer eBicyclingDistance;
                    try {
                        eBicyclingDistance = MapUtils.getEBicyclingDistance(from, to);
                        tpmCartCheckoutResVo.setDistance(new BigDecimal(eBicyclingDistance));
                    } catch (Exception e) {
                        log.error("计算电动车配送距离失败");
                        tpmCartCheckoutResVo.setAllowSubmitOrder(false);
                        tpmCartCheckoutResVo.setReason("下单失败:计算配送距离失败");
                        return WxResult.success(tpmCartCheckoutResVo);
                    }

                    // 使用支持shopId的配置方法获取店铺特定的配送类型和店铺代码
                    //todo 这里改下名称 因为指代为店铺配送方式：如聚单客
//                    Integer sendType = SystemConfig.getBinjiangSendType(shopId);
//                    String shopCode = SystemConfig.getBinjiangShopCode(shopId);
                    try {
//                        TpmFreightStepPrice price = tpmFreightStepPriceService.getFreightPriceByShopCodeAndDistance(shopCode, sendType, eBicyclingDistance);
                        TpmFreightStepPrice price = tpmFreightStepPriceService.getFreightPriceByShopCodeAndDistance(shopId,eBicyclingDistance);
                        if (Objects.nonNull(price)) {
                            totalFreightPrice = price.getPrice();
                        } else {
                            tpmCartCheckoutResVo.setAllowSubmitOrder(false);
                            tpmCartCheckoutResVo.setReason("下单失败:获取配送费失败");
                            return WxResult.success(tpmCartCheckoutResVo);
                        }
                    } catch (Exception e) {
                        log.error("下单失败:" + e.getMessage());
                        tpmCartCheckoutResVo.setAllowSubmitOrder(false);
                        tpmCartCheckoutResVo.setReason("下单失败:" + e.getMessage());
                        return WxResult.success(tpmCartCheckoutResVo);
                    }
                } else {
                    // 使用支持shopId的配置方法获取店铺特定的运费
                    totalFreightPrice = SystemConfig.getFreight(shopId);
                }
            }
        }


        tpmCartCheckoutResVo.setIsMultiOrderModel(0);
        tpmCartCheckoutResVo.setFreightPrice(totalFreightPrice);


        Map<Integer, TpmGoods> idTpmGoodsMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));
        // 计算优惠券可用情况
        BigDecimal couponPrice = new BigDecimal("0.00");
        int tmpCouponId = 0;
        int tmpCouponUserId = 0;
        int availableCouponLength = 0;
        Integer couponUserId = reqVo.getCouponUserId();
        String couponName = null;
        try {
            CartCheckOutCouponInfoDTO cartCheckOutCouponInfoDTO = couponVerifyService.checkCouponWithGoods(userId, idTpmGoodsMap, goodsTotalPrice, couponUserId);
            if (Objects.nonNull(cartCheckOutCouponInfoDTO)) {
                availableCouponLength = cartCheckOutCouponInfoDTO.getCouponLength();
                tmpCouponId = cartCheckOutCouponInfoDTO.getCouponId();
                couponPrice = cartCheckOutCouponInfoDTO.getCouponPrice();
                couponName = cartCheckOutCouponInfoDTO.getCouponName();
                tmpCouponUserId = cartCheckOutCouponInfoDTO.getCouponUserId();
                int categoryId = cartCheckOutCouponInfoDTO.getCategoryId();
                if (effectiveEveningDiscount && !Objects.equals(categoryId, 0) && !excludeCategory.contains(categoryId)) {
                    couponPrice = couponPrice.multiply(discount);
                }
            }
        } catch (Exception e) {
            log.error("优惠券计算失败:" + e.getMessage(), e);
        }
        // 用户积分减免
        BigDecimal integralPrice = new BigDecimal("0.00");

        BigDecimal orderTotalPrice = goodsTotalPrice.add(packingFee).add(totalFreightPrice).subtract(couponPrice);
        BigDecimal actualPrice = orderTotalPrice.subtract(integralPrice);

        tpmCartCheckoutResVo.setCouponId(tmpCouponId);
        tpmCartCheckoutResVo.setCouponUserId(tmpCouponUserId);
        tpmCartCheckoutResVo.setAvailableCouponLength(availableCouponLength);
        tpmCartCheckoutResVo.setCouponPrice(couponPrice);
        tpmCartCheckoutResVo.setCouponName(couponName);
        // 订单总价：goodsTotalPrice + totalFreightPrice - couponPrice
        tpmCartCheckoutResVo.setOrderTotalPrice(orderTotalPrice);
        // 订单实际付款金额：orderTotalPrice - integralPrice
        if (actualPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("最终实付低于0,设置为0.01");
            actualPrice = new BigDecimal("0.01");
        }
        tpmCartCheckoutResVo.setActualPrice(actualPrice);
        log.info("【请求结束】用户购物车下单,响应结果：{}", JSONObject.toJSONString(tpmCartCheckoutResVo));
        return WxResult.success(tpmCartCheckoutResVo);
    }


    @PostMapping("/shop/checkout")
    public WxResult<TpmCartShopCheckoutResVo> shopCheckout(@RequestBody TpmCartShopCheckOutReqVo reqVo) {
        log.info("【请求开始】用户购物车下单,请求参数,userId:{} reqVo={}", reqVo.getUserId(), JSON.toJSONString(reqVo));
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("用户购物车下单失败:用户未登录！！！");
            return WxResult.unlogin();
        }
        TpmUser tpmUser = tpmUserService.findById(userId);
        if (Objects.isNull(tpmUser)) {
            return WxResult.userNotExist();
        }
        // 商品价格
        List<TpmCart> tpmCartList = cartService.findById(reqVo.getCartIdList());
        if (CollectionUtils.isEmpty(tpmCartList)) {
            return WxResult.error("购物车商品信息有误，请刷新后重试");
        }
        // 转换下对象
        List<TpmCartResVo> checkedGoodsList = ConvertUtil.listConvert(tpmCartList, TpmCartResVo.class);

        boolean anyMatch = checkedGoodsList.stream().anyMatch(checkedGoods -> !Objects.equals(checkedGoods.getUserId(), userId));
        if (anyMatch) {
            return WxResult.error("购物车商品信息有误，请刷新后重试");
        }

        TpmCartShopCheckoutResVo tpmCartShopCheckoutResVo = new TpmCartShopCheckoutResVo();
        // 收货地址
        TpmAddress checkedAddress = null;
        if (Objects.nonNull(reqVo.getAddressId())) {
            tpmCartShopCheckoutResVo.setAddressId(reqVo.getAddressId());
            checkedAddress = addressService.findById(reqVo.getAddressId());
            if (Objects.isNull(checkedAddress)) {
                return WxResult.error("收货地址信息有误，请刷新后重试");
            }
//            if (!Objects.equals(checkedAddress.getUserId(), userId)){
//                return WxResult.error("收货地址信息错误，请重新选择");
//            }
            tpmCartShopCheckoutResVo.setCheckedAddress(checkedAddress);
        } else {
            tpmCartShopCheckoutResVo.setAllowSubmitOrder(false);
        }
        tpmCartShopCheckoutResVo.setTelephone(tpmUser.getMobile());
        tpmCartShopCheckoutResVo.setAllowSubmitOrder(true);

        List<Integer> collect = checkedGoodsList.stream().map(TpmCart::getGoodsId).distinct().collect(Collectors.toList());
        List<TpmGoods> goodsList = goodsService.findById(collect);
        Map<Integer, TpmGoods> idGoodsMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));

        for (TpmCartResVo cart : checkedGoodsList) {
            if (idGoodsMap.containsKey(cart.getGoodsId())) {
                // 解析下属性
                BigDecimal specificationsPrice = new BigDecimal(0.00);
                TpmGoods tpmGoods = idGoodsMap.get(cart.getGoodsId());
                if (Objects.nonNull(tpmGoods)) {
                    BigDecimal retailPrice = tpmGoods.getRetailPrice().add(specificationsPrice);
                    cart.setPrice(retailPrice);
                    cart.setGoodsName(tpmGoods.getName());
                    cart.setPicUrl(tpmGoods.getPicUrl());
                    cart.setCategoryId(tpmGoods.getCategoryId());
                    cart.setTransportType(tpmGoods.getTransportType());
                } else {
                    return WxResult.error("购物车商品信息有误，请删除后重试");
                }
                String specifications = cart.getSpecifications();
                if (!StringUtils.isEmpty(specifications)) {
                    List<TpmGoodsAttributesDto> goodsAttributesDtos = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
                    TpmSpecificationsResultDto specificationsResultDto = attributeSpecificationService.getSelectedSpecificationsResult(goodsAttributesDtos);
                    if (specificationsResultDto != null) {
                        cart.setSpecifications(specificationsResultDto.getSpecificationsJoin());
                        specificationsPrice = specificationsResultDto.getSpecificationTotalPrice();
                        cart.setPrice(cart.getPrice().add(specificationsPrice));
                    } else {
                        cart.setSpecifications("");
                    }
                }
            } else {
                return WxResult.error("购物车商品信息有误，请删除后重试");
            }
        }

        Map<Integer, List<TpmCartResVo>> transportTypeCartMap = checkedGoodsList.stream().collect(Collectors.groupingBy(TpmCartResVo::getTransportType));


        BigDecimal totalGoodsPrice = new BigDecimal("0.00");
        BigDecimal totalPackingPrice = new BigDecimal("0.00");
        BigDecimal totalFreightPrice = new BigDecimal("0.00");// 总配送费 （单店铺模式一个，多店铺模式多个配送费的总和）
        boolean allowSubmitOrder = true;
        StringBuilder errorMsg = new StringBuilder();
        for (Map.Entry<Integer, List<TpmCartResVo>> entry : transportTypeCartMap.entrySet()) {
            Integer transportType = entry.getKey();
            List<TpmCartResVo> cartList = entry.getValue();
            TpmCartCheckoutResVo tpmCartCheckoutResVo = new TpmCartCheckoutResVo();
            tpmCartCheckoutResVo.setAllowSubmitOrder(true);
            tpmCartCheckoutResVo.setCheckedGoodsList(cartList);
            tpmCartCheckoutResVo.setPackingFee(BigDecimal.ZERO);
            tpmCartCheckoutResVo.setFreightPrice(BigDecimal.ZERO);
            if (Objects.equals(transportType, TpmTransportTypeEnums.COLD_CHAIN.getCode())) {
                BigDecimal coldChainTotalGoodsPrice = cartList.stream().filter(c -> Objects.equals(c.getTransportType(), TpmTransportTypeEnums.COLD_CHAIN.getCode()))
                        .map(c -> c.getPrice().multiply(BigDecimal.valueOf(c.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                tpmCartCheckoutResVo.setGoodsTotalPrice(coldChainTotalGoodsPrice);
                if (Objects.nonNull(reqVo.getAddressId())) {
                    BigDecimal confPriceByCityCode = tpmExpressConfService.getConfPriceByCityCode(TpmTransportTypeEnums.COLD_CHAIN.getCode(), checkedAddress);
                    if (coldChainTotalGoodsPrice.compareTo(BigDecimal.valueOf(300L))>=0) {
                        log.info("冷链满300元，原运费:{} 包邮",confPriceByCityCode);
                        confPriceByCityCode = BigDecimal.ZERO;
                    }else if (coldChainTotalGoodsPrice.compareTo(BigDecimal.valueOf(100L))>=0){
                        log.info("冷链满100元，原运费:{} 半价",confPriceByCityCode);
                        confPriceByCityCode = confPriceByCityCode.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
                    }
                    totalFreightPrice = totalFreightPrice.add(confPriceByCityCode);
                    tpmCartCheckoutResVo.setFreightPrice(confPriceByCityCode);
                }
                BigDecimal freightLimit = SystemConfig.getExpressColdChainLimit(1);
                if (coldChainTotalGoodsPrice.compareTo(freightLimit) < 0) {
                    tpmCartCheckoutResVo.setAllowSubmitOrder(false);
                    tpmCartCheckoutResVo.setReason("下单失败:冷链订单未满足起送价" + freightLimit + "元");
                    allowSubmitOrder = false;
                    errorMsg.append(tpmCartCheckoutResVo.getReason());
                }
                tpmCartShopCheckoutResVo.setColdChainResVo(tpmCartCheckoutResVo);
            } else if (Objects.equals(transportType, TpmTransportTypeEnums.COMMON.getCode())) {
                BigDecimal commonTotalGoodsPrice = cartList.stream().filter(c -> Objects.equals(c.getTransportType(), TpmTransportTypeEnums.COMMON.getCode()))
                        .map(c -> c.getPrice().multiply(BigDecimal.valueOf(c.getNumber()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                tpmCartCheckoutResVo.setGoodsTotalPrice(commonTotalGoodsPrice);
                if (Objects.nonNull(reqVo.getAddressId())) {
                    BigDecimal confPriceByCityCode = tpmExpressConfService.getConfPriceByCityCode(TpmTransportTypeEnums.COMMON.getCode(), checkedAddress);
                    if (commonTotalGoodsPrice.compareTo(BigDecimal.valueOf(300L))>=0) {
                        log.info("普快满300元，原运费:{} 包邮",confPriceByCityCode);
                        confPriceByCityCode = BigDecimal.ZERO;
                    }else if (commonTotalGoodsPrice.compareTo(BigDecimal.valueOf(100L))>=0){
                        log.info("普快满100元，原运费:{} 半价",confPriceByCityCode);
                        confPriceByCityCode = confPriceByCityCode.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
                    }
                    totalFreightPrice = totalFreightPrice.add(confPriceByCityCode);
                    tpmCartCheckoutResVo.setFreightPrice(confPriceByCityCode);
                }
                BigDecimal freightLimit = SystemConfig.getExpressCommonLimit(1);
                if (commonTotalGoodsPrice.compareTo(freightLimit) < 0) {
                    tpmCartCheckoutResVo.setAllowSubmitOrder(false);
                    tpmCartCheckoutResVo.setReason("下单失败:普快订单未满足起送价" + freightLimit + "元");
                    allowSubmitOrder = false;
                    errorMsg.append(tpmCartCheckoutResVo.getReason());
                }
                tpmCartShopCheckoutResVo.setCommonResVo(tpmCartCheckoutResVo);
            }

            totalGoodsPrice = totalGoodsPrice.add(tpmCartCheckoutResVo.getGoodsTotalPrice());
            totalPackingPrice = totalPackingPrice.add(tpmCartCheckoutResVo.getPackingFee());
        }
        if (transportTypeCartMap.entrySet().size() > 1) {
            tpmCartShopCheckoutResVo.setIsMultiOrderModel(1);
        } else {
            tpmCartShopCheckoutResVo.setIsMultiOrderModel(0);
        }
        tpmCartShopCheckoutResVo.setAllowSubmitOrder(allowSubmitOrder);
        tpmCartShopCheckoutResVo.setReason(errorMsg.toString());
        tpmCartShopCheckoutResVo.setTotalGoodsPrice(totalGoodsPrice);
        tpmCartShopCheckoutResVo.setTotalPackingFee(totalPackingPrice);
        tpmCartShopCheckoutResVo.setTotalFreightPrice(totalFreightPrice);
        BigDecimal totalActualPrice = totalGoodsPrice.add(totalPackingPrice).add(totalFreightPrice);
        // 订单实际付款金额：orderTotalPrice - integralPrice
        if (totalActualPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("最终实付低于0,设置为0.01");
            totalActualPrice = new BigDecimal("0.01");
        }
        tpmCartShopCheckoutResVo.setTotalActualPrice(totalActualPrice);
        log.info("【请求结束】用户购物车下单,响应结果：{}", JSONObject.toJSONString(tpmCartShopCheckoutResVo));
        return WxResult.success(tpmCartShopCheckoutResVo);
    }

    @RequestMapping("/doSomeTest")
    public void doSomeTest(@RequestParam("to") String to) throws Exception {
        //经纬度：120.215067,30.212846
        //度分秒格式：东经120°12'54.241",北纬30°12'46.246"
        //地址：浙江省杭州市滨江区江陵路1943号
        //详情请访问：http://jingweidu.757dy.com/share?xy=120.221524,30.218936
        String binjiangLongitude = "120.215067";
        String binjiangLatitude = "30.212846";
        String from = binjiangLatitude + "," + binjiangLongitude;
        //经纬度：120.220363,30.214204   30.214204,120.220363
        //度分秒格式：东经120°13'13.307",北纬30°12'51.134"
        //地址：浙江省杭州市滨江区瑞网巷
        //详情请访问：http://jingweidu.757dy.com/share?xy=120.226797,30.220385

        int eBicyclingDistance;
        eBicyclingDistance = MapUtils.getEBicyclingDistance(from, to);
//        Integer binjiangSendType = SystemConfig.getBinjiangSendType();
//        String shopCode = SystemConfig.getBinjiangShopCode();
//        TpmFreightStepPrice price = tpmFreightStepPriceService.getFreightPriceByShopCodeAndDistance(shopId, eBicyclingDistance);
//        BigDecimal totalFreightPrice;
//        if (Objects.nonNull(price)) {
//            totalFreightPrice = price.getPrice();
//        } else {
//            totalFreightPrice = SystemConfig.getFreight();
//        }
//        System.out.println(totalFreightPrice);
    }
}