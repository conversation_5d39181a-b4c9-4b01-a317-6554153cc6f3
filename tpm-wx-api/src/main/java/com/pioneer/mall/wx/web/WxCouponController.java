package com.pioneer.mall.wx.web;

import com.alibaba.fastjson.JSONObject;
import com.pioneer.mall.core.util.JacksonUtil;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.bean.dto.CartCheckOutCouponInfoDTO;
import com.pioneer.mall.db.domain.TpmCart;
import com.pioneer.mall.db.domain.TpmCoupon;
import com.pioneer.mall.db.domain.TpmCouponUser;
import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.util.CouponConstant;
import com.pioneer.mall.db.util.CouponUserConstant;
import com.pioneer.mall.db.util.WxResponseCode;
import com.pioneer.mall.db.util.WxResponseUtil;
import com.pioneer.mall.wx.bean.res.TpmCouponListResVo;
import com.pioneer.mall.wx.bean.res.TpmCouponUserListResVo;
import com.pioneer.mall.wx.dao.CouponVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优惠券服务
 */
@RestController
@RequestMapping("/wx/coupon")
@Validated
public class WxCouponController {
	private static final Logger logger = LoggerFactory.getLogger(WxCouponController.class);

	@Autowired
	private TpmCouponService couponService;
	@Autowired
	private TpmCouponUserService couponUserService;
	@Autowired
	private TpmGrouponRulesService grouponRulesService;
	@Autowired
	private TpmCartService cartService;
	@Autowired
	private CouponVerifyService couponVerifyService;
	@Autowired
	private TpmGoodsService goodsService;
	@Autowired
	private TpmGoodsService tpmGoodsService;

	/**
	 * 优惠券列表
	 *
	 * @param page
	 * @param size
	 * @param sort
	 * @param order
	 * @return
	 */
	@GetMapping("list")
	public WxResult<TpmCouponListResVo> list(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size,
			@Sort @RequestParam(defaultValue = "add_time") String sort,
			@Order @RequestParam(defaultValue = "desc") String order) {
		logger.info("【请求开始】获取优惠券列表,请求参数,page:{},size:{}", page, size);

		List<TpmCoupon> couponList = couponService.queryList(page, size, sort, order);
		int total = couponService.queryTotal();
		TpmCouponListResVo tpmCouponListResVo = new TpmCouponListResVo();
		tpmCouponListResVo.setCouponList(couponList);
		tpmCouponListResVo.setCount(total);
		logger.info("【请求结束】获取优惠券列表,响应内容:{}", JSONObject.toJSONString(tpmCouponListResVo));
		return WxResult.success(tpmCouponListResVo);
	}

	/**
	 * 个人优惠券列表
	 *
	 * @param userId
	 * @param status
	 * @param page
	 * @param size
	 * @param sort
	 * @param order
	 * @return
	 */
	@GetMapping("mylist")
	public WxResult<TpmCouponUserListResVo> mylist(@RequestParam("userId") Integer userId, @NotNull Short status,
			@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size,
			@Sort @RequestParam(defaultValue = "add_time") String sort,
			@Order @RequestParam(defaultValue = "desc") String order) {
		logger.info("【请求开始】个人优惠券列表,请求参数,userId:{},page:{},size:{}", userId, page, size);

		if (userId == null) {
			logger.error("个人优惠券列表失败:用户未登录！！！");
			return WxResult.unlogin();
		}

		List<TpmCouponUser> couponUserList = couponUserService.queryList(userId, null, status, page, size, sort, order);
		List<CouponVo> couponVoList = change(couponUserList);
		TpmCouponUserListResVo tpmCouponUserListResVo = new TpmCouponUserListResVo();
		tpmCouponUserListResVo.setCouponUserList(couponVoList);
		if (CollectionUtils.isEmpty(couponVoList)){
			tpmCouponUserListResVo.setCount(0);
		}else{
			tpmCouponUserListResVo.setCount(couponUserList.size());
		}

		logger.info("【请求结束】个人优惠券列表,响应内容:{}", JSONObject.toJSONString(tpmCouponUserListResVo));
		return WxResult.success(tpmCouponUserListResVo);
	}

	@GetMapping("couponUserDetail")
	public WxResult<CouponVo> couponUserDetail(@RequestParam("userId") Integer userId, @RequestParam("couponUserId") Integer couponUserId) {
		logger.info("【请求开始】个人优惠券详情,请求参数,userId:{},couponUserId:{}", userId, couponUserId);

		if (userId == null) {
			logger.error("个人优惠券详情失败:用户未登录！！！");
			return WxResult.unlogin();
		}

		TpmCouponUser couponUser = couponUserService.findById(couponUserId);
		logger.info("couponUser={}",JSONObject.toJSONString(couponUser));
		if (couponUser == null) {
			return WxResult.error("优惠券未找到");
		}
		if (!couponUser.getUserId().equals(userId)) {
			return WxResult.error("非本人优惠券");
		}
		Integer couponId = couponUser.getCouponId();
		TpmCoupon coupon = couponService.findById(couponId);
		if (coupon == null) {
			return WxResult.error("优惠券未找到");
		}
		String applicableGoods = "";
		if (Objects.equals(coupon.getGoodsType().intValue(), 0)) {
			applicableGoods ="全场适用";
		} else if (Objects.equals(coupon.getGoodsType().intValue(), 2)) {
			Integer[] goodsValue = coupon.getGoodsValue();
			List<TpmGoods> tpmGoodsList = tpmGoodsService.findById(Arrays.asList(goodsValue));
			applicableGoods = tpmGoodsList.stream().map(TpmGoods::getName).collect(Collectors.joining("、"));
		}
		CouponVo couponVo = new CouponVo();
		couponVo.setCouponUserId(couponUser.getId());
		couponVo.setId(coupon.getId());
		couponVo.setName(coupon.getName());
		couponVo.setDesc(coupon.getDesc());
		couponVo.setGoodsType(Integer.valueOf(coupon.getGoodsType()));
		couponVo.setTag(coupon.getTag());
		couponVo.setMin(coupon.getMin().toPlainString());
		couponVo.setDiscount(coupon.getDiscount().toPlainString());
		couponVo.setStartTime(couponUser.getStartTime());
		couponVo.setEndTime(couponUser.getEndTime());
		couponVo.setId(couponUser.getId());
		if (Objects.equals(couponUser.getStatus(), CouponUserConstant.STATUS_USABLE)) {
			couponVo.setCouponQrCode("coupon-"+couponUser.getCouponQrCode());
		}
		couponVo.setStatus(couponUser.getStatus());
		if (Objects.equals(couponUser.getStatus(), CouponUserConstant.STATUS_USABLE)){
			couponVo.setStatusDesc("待使用");
		}else if (Objects.equals(couponUser.getStatus(), CouponUserConstant.STATUS_USED)){
			couponVo.setStatusDesc("已使用");
		} else if (Objects.equals(couponUser.getStatus(), CouponUserConstant.STATUS_EXPIRED)) {
			couponVo.setStatusDesc("已过期");
		}
		if (Objects.equals(couponUser.getUsedType(),0)){
			couponVo.setUsedTypeDesc("线下核销");
		}else if (Objects.equals(couponUser.getUsedType(),1)){
			couponVo.setUsedTypeDesc("线上订单");
		}
		couponVo.setApplicableGoods(applicableGoods);

		logger.info("【请求结束】个人优惠券详情,响应内容:{}", JSONObject.toJSONString(couponVo));
		return WxResult.success(couponVo);
	}

	/**
	 * 个人可领取优惠券列表
	 *
	 * @param userId
	 * @param status
	 * @param page
	 * @param size
	 * @param sort
	 * @param order
	 * @return
	 */
	@GetMapping("getUserCoupon")
	public WxResult<TpmCouponListResVo> getUserCoupon(@RequestParam Integer userId) {
		logger.info("【请求开始】个人可领取优惠券列表,请求参数,userId:{}", userId);

		if (userId == null) {
			logger.error("个人可领取优惠券列表:用户未登录！！！");
			return WxResult.unlogin();
		}
		List<TpmCoupon> coupons = couponService.queryAvailableList(userId, 0, 10);
		TpmCouponListResVo tpmCouponListResVo = new TpmCouponListResVo();
		tpmCouponListResVo.setCouponList(coupons);
		logger.info("【请求结束】个人可领取优惠券列表,响应内容:{}", JSONObject.toJSONString(tpmCouponListResVo));
		return WxResult.success(tpmCouponListResVo);
	}

	private List<CouponVo> change(List<TpmCouponUser> couponList) {
		List<CouponVo> couponVoList = new ArrayList<>(couponList.size());
		for (TpmCouponUser couponUser : couponList) {
			Integer couponId = couponUser.getCouponId();
			TpmCoupon coupon = couponService.findById(couponId);
			CouponVo couponVo = new CouponVo();
			couponVo.setCouponUserId(couponUser.getId());
			couponVo.setId(coupon.getId());
			couponVo.setName(coupon.getName());
			couponVo.setDesc(coupon.getDesc());
			couponVo.setGoodsType(Integer.valueOf(coupon.getGoodsType()));
			couponVo.setTag(coupon.getTag());
			couponVo.setMin(coupon.getMin().toPlainString());
			couponVo.setDiscount(coupon.getDiscount().toPlainString());
			couponVo.setStartTime(couponUser.getStartTime());
			couponVo.setEndTime(couponUser.getEndTime());
			couponVo.setStatus(couponUser.getStatus());
			if (Objects.equals(couponUser.getStatus(), CouponUserConstant.STATUS_USABLE)){
				couponVo.setStatusDesc("待使用");
			}else if (Objects.equals(couponUser.getStatus(), CouponUserConstant.STATUS_USED)){
				couponVo.setStatusDesc("已使用");
			} else if (Objects.equals(couponUser.getStatus(), CouponUserConstant.STATUS_EXPIRED)) {
				couponVo.setStatusDesc("已过期");
			}
			if (Objects.equals(couponUser.getUsedType(),0)){
				couponVo.setUsedTypeDesc("线下核销");
			}else if (Objects.equals(couponUser.getUsedType(),1)){
				couponVo.setUsedTypeDesc("线上订单");
			}
			couponVoList.add(couponVo);
		}
		return couponVoList;
	}

	/**
	 * 当前购物车下单商品订单可用优惠券
	 *
	 * @param userId
	 * @param cartId
	 * @param grouponRulesId
	 * @return
	 */
	@GetMapping("selectlist")
	public WxResult<List<CouponVo>> selectlist(@RequestParam Integer userId) {
		logger.info("【请求开始】当前购物车下单商品订单可用优惠券,请求参数,userId:{}", userId);

		if (userId == null) {
			logger.error("当前购物车下单商品订单可用优惠券:用户未登录！！！");
			return WxResult.unlogin();
		}

//		// 团购优惠
//		BigDecimal grouponPrice = new BigDecimal(0.00);
//		TpmGrouponRules grouponRules = grouponRulesService.queryById(grouponRulesId);
//		if (grouponRules != null) {
//			grouponPrice = grouponRules.getDiscount();
//		}

		// 商品价格
		List<TpmCart> checkedGoodsList = cartService.queryByUidAndChecked(userId);
		List<Integer> goodsIdList = checkedGoodsList.stream().map(TpmCart::getGoodsId).distinct().collect(Collectors.toList());
		List<TpmGoods> goodsList = tpmGoodsService.findById(goodsIdList);
		Map<Integer, TpmGoods> goodsIdMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));

		BigDecimal checkedGoodsPrice = new BigDecimal(0.00);
		for (TpmCart cart : checkedGoodsList) {
			Integer goodsId = cart.getGoodsId();
			if (goodsIdMap.containsKey(goodsId)) {
				TpmGoods tpmGoods = goodsIdMap.get(goodsId);
				checkedGoodsPrice = checkedGoodsPrice.add(tpmGoods.getRetailPrice().multiply(new BigDecimal(cart.getNumber())));
			} else {
				checkedGoodsPrice = checkedGoodsPrice.add(cart.getPrice().multiply(new BigDecimal(cart.getNumber())));
			}
			// 只有当团购规格商品ID符合才进行团购优惠
//			if (grouponRules != null && grouponRules.getGoodsId().equals(cart.getGoodsId())) {
//				checkedGoodsPrice = checkedGoodsPrice
//						.add(cart.getPrice().subtract(grouponPrice).multiply(new BigDecimal(cart.getNumber())));
//			} else {
//			}
		}

		// 计算优惠券可用情况
		List<TpmCouponUser> couponUserList = couponUserService.queryAll(userId);

		List<TpmCouponUser> availableCouponUserList = new ArrayList<>(couponUserList.size());
		for (TpmCouponUser couponUser : couponUserList) {
			CartCheckOutCouponInfoDTO cartCheckOutCouponInfoDTO = null;
			try {
				cartCheckOutCouponInfoDTO = couponVerifyService.checkCouponWithGoods(userId, goodsIdMap, checkedGoodsPrice, couponUser.getId());
			} catch (Exception e) {
				logger.error("checkCouponWithGoods error={}", e.getMessage(), e);
				continue;
			}
			if (cartCheckOutCouponInfoDTO == null) {
				continue;
			}
			if (!Objects.equals(cartCheckOutCouponInfoDTO.getCouponId(), 0)) {
				availableCouponUserList.add(couponUser);
			}
		}

		List<CouponVo> couponVoList = change(availableCouponUserList);

		logger.info("【请求结束】当前购物车下单商品订单可用优惠券,响应内容:{}", JSONObject.toJSONString(couponVoList));
		return WxResult.success(couponVoList);
	}

	/**
	 * 优惠券领取
	 *
	 * @param userId
	 *            用户ID
	 * @param body
	 *            请求内容， { couponId: xxx }
	 * @return 操作结果
	 */
	@PostMapping("receive")
	public Object receive(Integer userId, @RequestBody String body) {
		logger.info("【请求开始】优惠券领取,请求参数,userId:{},body:{}", userId, body);

		if (userId == null) {
			logger.error("优惠券领取:用户未登录！！！");
			return ResponseUtil.unlogin();
		}

		Integer couponId = JacksonUtil.parseInteger(body, "couponId");
		if (couponId == null) {
			return ResponseUtil.badArgument();
		}

		TpmCoupon coupon = couponService.findById(couponId);
		if (coupon == null) {
			return ResponseUtil.badArgumentValue();
		}

		// 当前已领取数量和总数量比较
		Integer total = coupon.getTotal();
		Integer totalCoupons = couponUserService.countCoupon(couponId);
		if ((total != 0) && (totalCoupons >= total)) {
			logger.error("优惠券领取出错:{}", WxResponseCode.COUPON_EXCEED_LIMIT.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXCEED_LIMIT);
		}

		// 当前用户已领取数量和用户限领数量比较
		Integer limit = coupon.getLimit().intValue();
		Integer userCounpons = couponUserService.countUserAndCoupon(userId, couponId);
		if ((limit != 0) && (userCounpons >= limit)) {
			logger.error("优惠券领取出错:{}", WxResponseCode.COUPON_EXCEED_LIMIT.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXCEED_LIMIT);
		}

		// 优惠券分发类型
		// 例如注册赠券类型的优惠券不能领取
		Short type = coupon.getType();
		if (type.equals(CouponConstant.TYPE_REGISTER) || type.equals(CouponConstant.TYPE_CODE)
				|| !type.equals(CouponConstant.TYPE_COMMON)) {
			logger.error("优惠券领取出错:{}", WxResponseCode.COUPON_CODE_INVALID.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_CODE_INVALID);
		}

		// 优惠券状态，已下架或者过期不能领取
		Short status = coupon.getStatus();
		if (status.equals(CouponConstant.STATUS_OUT)) {
			logger.error("优惠券领取出错:{}", WxResponseCode.COUPON_EXCEED_LIMIT.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXCEED_LIMIT);
		} else if (status.equals(CouponConstant.STATUS_EXPIRED)) {
			logger.error("优惠券领取出错:{}", WxResponseCode.COUPON_EXPIRED.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXPIRED);
		}

		// 用户领券记录
		TpmCouponUser couponUser = new TpmCouponUser();
		couponUser.setCouponId(couponId);
		couponUser.setUserId(userId);
		Short timeType = coupon.getTimeType();
		if (timeType.equals(CouponConstant.TIME_TYPE_TIME)) {
			couponUser.setStartTime(coupon.getStartTime());
			couponUser.setEndTime(coupon.getEndTime());
		} else {
			LocalDate now = LocalDate.now();
			couponUser.setStartTime(now);
			couponUser.setEndTime(now.plusDays(coupon.getDays()));
		}
		couponUserService.add(couponUser);

		logger.info("【请求结束】优惠券领取成功!");
		return ResponseUtil.ok();
	}

	/**
	 * 一键领取优惠券
	 *
	 * @param userId
	 *            用户ID
	 * @return 操作结果
	 */
	@PostMapping("receiveAll")
	public Object receiveAll(Integer userId) {
		logger.info("【请求开始】一键领取优惠券，请求参数：userId:{}", userId);

		if (userId == null) {
			logger.error("一键领取优惠券:用户未登录！！！");
			return ResponseUtil.unlogin();
		}
		List<TpmCoupon> dtsCoupons = couponService.queryAvailableList(userId, 0, 10);
		if (dtsCoupons == null || dtsCoupons.size() == 0) {
			// return ResponseUtil.badArgument();
			return ResponseUtil.ok();// 没有可领取的优惠券也暂时不向前端报错
		}
		for (TpmCoupon dtsCoupon : dtsCoupons) {
			Integer couponId = dtsCoupon.getId();
			TpmCoupon coupon = couponService.findById(couponId);
			if (coupon == null) {
				continue;
			}
			// 当前用户已领取数量和用户限领数量比较
			Integer limit = coupon.getLimit().intValue();
			Integer userCounpons = couponUserService.countUserAndCoupon(userId, couponId);
			if ((limit != 0) && (userCounpons >= limit)) {// 用户已经领了此优惠券
				continue;
			}
			// 优惠券分发类型 例如兑换券类型的优惠券不能领取
			Short type = coupon.getType();
			if (type.equals(CouponConstant.TYPE_CODE)) {// 只能兑换
				continue;
			}
			// 优惠券状态，已下架或者过期不能领取
			Short status = coupon.getStatus();
			if (status.equals(CouponConstant.STATUS_OUT) || status.equals(CouponConstant.STATUS_EXPIRED)) {// 优惠券已领完 或
																											// 优惠券已经过期
				continue;
			}
			// 用户领券记录
			TpmCouponUser couponUser = new TpmCouponUser();
			couponUser.setCouponId(couponId);
			couponUser.setUserId(userId);
			Short timeType = coupon.getTimeType();
			if (timeType.equals(CouponConstant.TIME_TYPE_TIME)) {
				couponUser.setStartTime(coupon.getStartTime());
				couponUser.setEndTime(coupon.getEndTime());
			} else {
				LocalDate now = LocalDate.now();
				couponUser.setStartTime(now);
				couponUser.setEndTime(now.plusDays(coupon.getDays()));
			}
			couponUserService.add(couponUser);
		}

		logger.info("【请求结束】一键领取优惠券成功!");
		return ResponseUtil.ok();
	}

	/**
	 * 优惠券兑换
	 *
	 * @param userId
	 *            用户ID
	 * @param body
	 *            请求内容， { code: xxx }
	 * @return 操作结果
	 */
	@PostMapping("exchange")
	public Object exchange(Integer userId, @RequestBody String body) {
		logger.info("【请求开始】优惠券兑换，请求参数：userId:{},Body:{}", userId, body);

		if (userId == null) {
			logger.error("优惠券兑换:用户未登录！！！");
			return ResponseUtil.unlogin();
		}

		String code = JacksonUtil.parseString(body, "code");
		if (code == null) {
			return ResponseUtil.badArgument();
		}

		TpmCoupon coupon = couponService.findByCode(code);
		if (coupon == null) {
			logger.error("优惠券兑换出错:{}", WxResponseCode.COUPON_CODE_INVALID.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_CODE_INVALID);
		}
		Integer couponId = coupon.getId();

		// 当前已领取数量和总数量比较
		Integer total = coupon.getTotal();
		Integer totalCoupons = couponUserService.countCoupon(couponId);
		if ((total != 0) && (totalCoupons >= total)) {
			logger.error("优惠券兑换出错:{}", WxResponseCode.COUPON_EXCEED_LIMIT.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXCEED_LIMIT);
		}

		// 当前用户已领取数量和用户限领数量比较
		Integer limit = coupon.getLimit().intValue();
		Integer userCounpons = couponUserService.countUserAndCoupon(userId, couponId);
		if ((limit != 0) && (userCounpons >= limit)) {
			logger.error("优惠券兑换出错:{}", WxResponseCode.COUPON_EXCEED_LIMIT.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXCEED_LIMIT);
		}

		// 优惠券分发类型
		// 例如注册赠券类型的优惠券不能领取
		Short type = coupon.getType();
		if (type.equals(CouponConstant.TYPE_REGISTER) || type.equals(CouponConstant.TYPE_COMMON)
				|| !type.equals(CouponConstant.TYPE_CODE)) {
			logger.error("优惠券兑换出错:{}", WxResponseCode.COUPON_NOT_CHANGE.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_NOT_CHANGE);
		}

		// 优惠券状态，已下架或者过期不能领取
		Short status = coupon.getStatus();
		if (status.equals(CouponConstant.STATUS_OUT)) {
			logger.error("优惠券兑换出错:{}", WxResponseCode.COUPON_EXCEED_LIMIT.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXCEED_LIMIT);
		} else if (status.equals(CouponConstant.STATUS_EXPIRED)) {
			logger.error("优惠券兑换出错:{}", WxResponseCode.COUPON_EXPIRED.desc());
			return WxResponseUtil.fail(WxResponseCode.COUPON_EXPIRED);
		}

		// 用户领券记录
		TpmCouponUser couponUser = new TpmCouponUser();
		couponUser.setCouponId(couponId);
		couponUser.setUserId(userId);
		Short timeType = coupon.getTimeType();
		if (timeType.equals(CouponConstant.TIME_TYPE_TIME)) {
			couponUser.setStartTime(coupon.getStartTime());
			couponUser.setEndTime(coupon.getEndTime());
		} else {
			LocalDate now = LocalDate.now();
			couponUser.setStartTime(now);
			couponUser.setEndTime(now.plusDays(coupon.getDays()));
		}
		couponUser.setCouponQrCode(UUID.randomUUID().toString().replaceAll("-", ""));

		couponUserService.add(couponUser);

		logger.info("【请求结束】优惠券兑换成功!");
		return ResponseUtil.ok();
	}

}