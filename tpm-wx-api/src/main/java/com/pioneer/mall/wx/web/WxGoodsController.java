package com.pioneer.mall.wx.web;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.core.config.EveningDiscountConfigDTO;
import com.pioneer.mall.core.system.DiscountService;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.core.validator.Order;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.domain.TpmCart;
import com.pioneer.mall.db.domain.TpmCategory;
import com.pioneer.mall.db.domain.TpmGoods;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.wx.annotation.LoginUser;
import com.pioneer.mall.wx.bean.res.TpmGoodsCartResVo;
import com.pioneer.mall.wx.bean.res.TpmGoodsCategoryResVo;
import com.pioneer.mall.wx.bean.res.TpmGoodsDetailResVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品服务
 */
@RestController
@RequestMapping("/wx/goods")
@Validated
public class WxGoodsController {
    private static final Logger logger = LoggerFactory.getLogger(WxGoodsController.class);

    @Autowired
    private TpmGoodsService goodsService;

    @Autowired
    private TpmGoodsProductService productService;

    @Autowired
    private TpmIssueService goodsIssueService;

    @Autowired
    private TpmGoodsAttributeService goodsAttributeService;

    @Autowired
    private TpmBrandService brandService;

    @Autowired
    private TpmCommentService commentService;

    @Autowired
    private TpmUserService userService;

    @Autowired
    private TpmCollectService collectService;

    @Autowired
    private TpmFootprintService footprintService;

    @Autowired
    private TpmCategoryService categoryService;

    @Autowired
    private TpmSearchHistoryService searchHistoryService;

    @Autowired
    private TpmGoodsSpecificationService goodsSpecificationService;

    @Autowired
    private TpmGrouponRulesService rulesService;

    @Autowired
    private TpmCartService tpmCartService;

    @Autowired
    private TpmCategoryService tpmCategoryService;

    @Autowired
    private TpmAttributeSpecificationService attributeSpecificationService;

    @Autowired
    private DiscountService discountService;

    private final static ArrayBlockingQueue<Runnable> WORK_QUEUE = new ArrayBlockingQueue<>(9);

    private final static RejectedExecutionHandler HANDLER = new ThreadPoolExecutor.CallerRunsPolicy();

    private static ThreadPoolExecutor executorService = new ThreadPoolExecutor(16, 16, 1000, TimeUnit.MILLISECONDS,
            WORK_QUEUE, HANDLER);

    /**
     * 商品详情
     * <p>
     * 用户可以不登录。 如果用户登录，则记录用户足迹以及返回用户收藏信息。
     *
     * @param userId 用户ID
     * @param id     商品ID
     * @return 商品详情
     */
    @SuppressWarnings("rawtypes")
    @GetMapping("detail")
    public WxResult<TpmGoodsDetailResVo> detail(@LoginUser Integer userId, @NotNull Integer id) {
        logger.info("【请求开始】商品详情,请求参数,userId:{},id:{}", userId, id);

        // 商品信息
        TpmGoods info = goodsService.findById(id);
//
//        // 商品属性
//        Callable<List<TpmGoodsAttribute>> goodsAttributeListCallable = () -> goodsAttributeService.queryByGid(id);
//
//        // 商品规格 返回的是定制的GoodsSpecificationVo
//        Callable<List<TpmGoodsSpecificationService.VO>> objectCallable = () -> goodsSpecificationService.getSpecificationVoList(id);
//
//        // 商品规格对应的数量和价格
//        Callable<List<TpmGoodsProduct>> productListCallable = () -> productService.queryByGid(id);
//
//        // 商品问题，这里是一些通用问题
//        Callable<List<TpmIssue>> issueCallable = () -> goodsIssueService.query();
//
//        // 商品品牌商
//        Callable<TpmBrand> brandCallable = () -> {
//            Integer brandId = info.getBrandId();
//            TpmBrand brand;
//            if (brandId == 0) {
//                brand = new TpmBrand();
//            } else {
//                brand = brandService.findById(info.getBrandId());
//            }
//            return brand;
//        };

//        // 评论
//        Callable<List<TpmCommentResVo>> commentsCallable = () -> {
//            List<TpmComment> comments = commentService.queryGoodsByGid(id, 0, 2);
//            List<TpmCommentResVo> commentsVo = new ArrayList<>(comments.size());
//            for (TpmComment comment : comments) {
//                TpmCommentResVo tpmCommentResVo = new TpmCommentResVo();
//                tpmCommentResVo.setId(comment.getId());
//                tpmCommentResVo.setAddTime(comment.getAddTime());
//                tpmCommentResVo.setContent(comment.getContent());
//                TpmUser user = userService.findById(comment.getUserId());
//                tpmCommentResVo.setNickname(user.getNickname());
//                tpmCommentResVo.setAvatar(user.getAvatar());
//                tpmCommentResVo.setPicList(Arrays.asList(comment.getPicUrls()));
//                commentsVo.add(tpmCommentResVo);
//            }
//            return commentsVo;
//        };

        // 团购信息
//		Callable<List> grouponRulesCallable = () -> rulesService.queryByGoodsId(id.longValue());

//        // 用户收藏
//        int userHasCollect = 0;
//        if (userId != null) {
//            userHasCollect = collectService.count(userId, id);
//        }
//
//        // 记录用户的足迹 异步处理
//        if (userId != null) {
//            executorService.execute(() -> {
//                TpmFootprint footprint = new TpmFootprint();
//                footprint.setUserId(userId);
//                footprint.setGoodsId(id);
//                footprintService.add(footprint);
//                short num = 1;
//                productService.addBrowse(id, num);// 新增商品点击量
//            });
//        }

//        FutureTask<List<TpmGoodsAttribute>> goodsAttributeListTask = new FutureTask<>(goodsAttributeListCallable);
//        FutureTask<List<TpmGoodsSpecificationService.VO>> objectCallableTask = new FutureTask<>(objectCallable);
//        FutureTask<List<TpmGoodsProduct>> productListCallableTask = new FutureTask<>(productListCallable);
//        FutureTask<List<TpmIssue>> issueCallableTask = new FutureTask<>(issueCallable);
//        FutureTask<List<TpmCommentResVo>> commentsCallableTsk = new FutureTask<>(commentsCallable);
//        FutureTask<TpmBrand> brandCallableTask = new FutureTask<>(brandCallable);
//		FutureTask<List> grouponRulesCallableTask = new FutureTask<>(grouponRulesCallable);

//        executorService.submit(goodsAttributeListTask);
//        executorService.submit(objectCallableTask);
//        executorService.submit(productListCallableTask);
//        executorService.submit(issueCallableTask);
//        executorService.submit(commentsCallableTsk);
//        executorService.submit(brandCallableTask);
//		executorService.submit(grouponRulesCallableTask);

        List<TpmGoodsAttributesDto> goodsAttributes = attributeSpecificationService.queryByGid(id);

        TpmGoodsDetailResVo tpmGoodsDetailResVo = new TpmGoodsDetailResVo();
        try {
            tpmGoodsDetailResVo.setInfo(info);
//            tpmGoodsDetailResVo.setUserHasCollect(userHasCollect);
//            tpmGoodsDetailResVo.setIssue(issueCallableTask.get());
//            tpmGoodsDetailResVo.setComment(commentsCallableTsk.get());
//            tpmGoodsDetailResVo.setSpecificationList(objectCallableTask.get());
//            tpmGoodsDetailResVo.setProductList(productListCallableTask.get());
//            tpmGoodsDetailResVo.setAttribute(goodsAttributeListTask.get());
//            tpmGoodsDetailResVo.setBrand(brandCallableTask.get());
            tpmGoodsDetailResVo.setGoodsAttributes(goodsAttributes);
//			tpmGoodsDetailResVo.setGroupon(grouponRulesCallableTask.get());
        } catch (Exception e) {
            logger.error("获取商品详情出错:{}", e.getMessage(), e);
        }
//
//		// 商品分享图片地址
//		data.put("shareImage", info.getShareUrl());

        logger.info("【请求结束】获取商品详情成功!");// 这里不打印返回的信息，因为此接口查询量大，太耗日志空间
        return WxResult.success(tpmGoodsDetailResVo);
    }

    /**
     * 父级分类
     *
     * @return
     */
    @GetMapping("category/L1")
    public WxResult<List<TpmCategory>> categoryParent(
            @RequestParam(defaultValue = "1") Integer businessType) {
        List<TpmCategory> tpmCategories = categoryService.queryL1(businessType);
        logger.info("【请求结束】商品分类类目,响应结果:{}", JSONObject.toJSONString(tpmCategories));
        return WxResult.success(tpmCategories);
    }

    @GetMapping("category/L2")
    public WxResult<List<TpmCategory>> categoryL2(
            @RequestParam("shopId") Integer shopId,
            @RequestParam(defaultValue = "1") Integer businessType) {
        logger.info("【请求开始】商品分类类目 L2 ,请求参数,shopId:{},businessType:{}", shopId, businessType);
        List<TpmCategory> tpmCategories = categoryService.queryL2(shopId,businessType);
        logger.info("【请求结束】商品分类类目 L2 ,响应结果:{}", JSONObject.toJSONString(tpmCategories));
        return WxResult.success(tpmCategories);
    }

    /**
     * 商品分类类目
     *
     * @param id 分类类目ID
     * @return 商品分类类目
     */
    @GetMapping("category")
    public WxResult<TpmGoodsCategoryResVo> category(@NotNull Integer id) {
        logger.info("【请求开始】商品分类类目,请求参数,id:{}", id);

        TpmCategory cur = categoryService.findById(id);
        TpmCategory parent = null;
        List<TpmCategory> children = null;

        if (cur.getPid() == 0) {
            parent = cur;
            children = categoryService.queryByPid(cur.getId());
            cur = children.size() > 0 ? children.get(0) : cur;
        } else {
            parent = categoryService.findById(cur.getPid());
            children = categoryService.queryByPid(cur.getPid());
        }
        TpmGoodsCategoryResVo tpmGoodsCategoryResVo = new TpmGoodsCategoryResVo();
        tpmGoodsCategoryResVo.setTpmCategory(cur);
        tpmGoodsCategoryResVo.setParentCategory(parent);
        tpmGoodsCategoryResVo.setBrotherCategory(children);


        logger.info("【请求结束】商品分类类目,响应结果:{}", JSONObject.toJSONString(tpmGoodsCategoryResVo));
        return WxResult.success(tpmGoodsCategoryResVo);
    }

    /**
     * 根据条件搜素商品
     * <p>
     * 1. 这里的前五个参数都是可选的，甚至都是空 2. 用户是可选登录，如果登录，则记录用户的搜索关键字
     *
     * @param categoryId 分类类目ID，可选
     * @param brandId    品牌商ID，可选
     * @param keyword    关键字，可选
     * @param isNew      是否新品，可选
     * @param isHot      是否热买，可选
     * @param userId     用户ID
     * @param page       分页页数
     * @param size       分页大小
     * @param sort       排序方式，支持"add_time", "retail_price"或"name",浏览量 "browse",销售量："sales"
     * @param order      排序类型，顺序或者降序
     * @return 根据条件搜素的商品详情
     */
    @Deprecated
    @GetMapping("list")
    public Object list(Integer categoryId,
                       @RequestParam(defaultValue = "1") Integer businessType,
                       Integer brandId, String keyword, Boolean isNew, Boolean isHot,
                       @LoginUser Integer userId, @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer size,
                       @Sort(accepts = {"sort_order", "add_time", "retail_price", "browse", "name",
                               "sales"}) @RequestParam(defaultValue = "sort_order") String sort,
                       @Order @RequestParam(defaultValue = "asc") String order) {

        logger.info("【请求开始】根据条件搜素商品,请求参数,categoryId:{},brandId:{},keyword:{}", categoryId, brandId, keyword);

//		// 添加到搜索历史
//		if (userId != null && !StringUtils.isNullOrEmpty(keyword)) {
//			TpmSearchHistory searchHistoryVo = new TpmSearchHistory();
//			searchHistoryVo.setKeyword(keyword);
//			searchHistoryVo.setUserId(userId);
//			searchHistoryVo.setFrom("wx");
//			searchHistoryService.save(searchHistoryVo);
//		}

        // 查询列表数据
        List<TpmGoods> goodsList = goodsService.querySelective(categoryId, businessType, keyword, isHot, isNew, page, size,
                sort, order);

        // 查询商品所属类目列表。
        List<Integer> goodsCatIds = goodsService.getCatIds(brandId, businessType, keyword, isHot, isNew);
        List<TpmCategory> categoryList;
        if (!goodsCatIds.isEmpty()) {
            categoryList = categoryService.queryL2ByIds(goodsCatIds);
        } else {
            categoryList = new ArrayList<>(0);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("goodsList", goodsList);
        long count = PageInfo.of(goodsList).getTotal();
        int totalPages = (int) Math.ceil((double) count / size);
        data.put("count", PageInfo.of(goodsList).getTotal());
        data.put("filterCategoryList", categoryList);
        data.put("totalPages", totalPages);

        logger.info("【请求结束】根据条件搜素商品,响应结果:查询的商品数量:{},总数：{},总共 {} 页", goodsList.size(), count, totalPages);
        return ResponseUtil.ok(data);
    }

    /**
     * 获取商品列表（根据类型分类）
     *
     * @return freightType 0是外卖，1是自提，2是堂食
     */
    @GetMapping("listV2")
    public WxResult<Map<Integer, List<TpmGoodsCartResVo>>> listV2(@RequestParam(name = "userId", defaultValue = "") Integer userId,
                                                                  @RequestParam(name = "shopId", defaultValue = "") Integer shopId,
                                                                  @RequestParam(name = "businessType", defaultValue = "1") Integer businessType,
                                                                  @RequestParam(name = "freightType", defaultValue = "") Integer freightType) {

        logger.info("【请求开始】获取商品列表（根据类型分类）,请求参数,userId:{},businessType:{},freightType:{} shopId:{}", userId, businessType, freightType,shopId);
        if (Objects.equals(businessType, TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode()) && freightType == null) {
            return WxResult.error("销售范围参数为空");
        }
        if (Objects.isNull(shopId)){
            return WxResult.error("请先选择店铺");
        }
        List<TpmGoods> tpmGoods = goodsService.listAllOnSellBySaleRange(freightType, businessType, shopId);
        if (CollectionUtils.isEmpty(tpmGoods)) {
            return WxResult.success(new HashMap<>());
        }
        List<TpmCart> tpmCarts;
        if (Objects.nonNull(userId) && !StringUtils.isEmpty(userId)) {
            tpmCarts = tpmCartService.queryByUid(userId, businessType);
        } else {
            tpmCarts = new ArrayList<>();
        }
        List<TpmGoodsCartResVo> collect = this.convertGoodsToResVO(tpmGoods, tpmCarts, businessType,shopId);

        Map<Integer, List<TpmGoodsCartResVo>> result = collect.stream().collect(Collectors.groupingBy(TpmGoodsCartResVo::getCategoryId));
        // 使用 TreeMap 确保 key 按照升序排列
        Map<Integer, List<TpmGoodsCartResVo>> sortedResult = new TreeMap<>(result);
        List<Integer> categoryIdList = collect.stream().map(TpmGoodsCartResVo::getCategoryId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(categoryIdList)) {
            List<TpmCategory> tpmCategoryList = tpmCategoryService.findById(categoryIdList);
            Map<Integer, TpmCategory> categoryMap = tpmCategoryList.stream()
                    .collect(Collectors.toMap(TpmCategory::getId, Function.identity()));
            // Step 3: 按照 sortOrder 字段对 categoryMap 进行排序，并重新构建 sortedResult
            Map<Integer, List<TpmGoodsCartResVo>> sortedBySortOrder = categoryMap.values()
                    .stream()
                    .sorted(Comparator.comparing(TpmCategory::getSortOrder))
                    .collect(Collectors.toMap(
                            TpmCategory::getId,
                            category -> sortedResult.get(category.getId()).stream()
                                    .sorted(Comparator.comparing(TpmGoodsCartResVo::getSortOrder))
                                    .collect(Collectors.toList()),
                            (e1, e2) -> e1,
                            LinkedHashMap::new // 保持顺序
                    ));
            return WxResult.success(sortedBySortOrder);
        }
        return WxResult.success(result);
    }

    public List<TpmGoodsCartResVo> convertGoodsToResVO(List<TpmGoods> tpmGoods, List<TpmCart> tpmCarts, Integer businessType, Integer shopId) {
        Map<Integer, List<TpmCart>> tpmCartMap;
        if (!CollectionUtils.isEmpty(tpmCarts)) {
            tpmCartMap = tpmCarts.stream().collect(Collectors.groupingBy(TpmCart::getGoodsId));
        } else {
            tpmCartMap = new HashMap<>();
        }
        Integer inventoryNotifyCount = SystemConfig.getInventoryNotifyCount(shopId);
        EveningDiscountConfigDTO currentEveningDiscount = discountService.getCurrentEveningDiscount(shopId);
        Boolean effectiveEveningDiscount = currentEveningDiscount.getEffective();
        BigDecimal discount = currentEveningDiscount.getDiscount();
        List<Integer> excludeCategory = currentEveningDiscount.getExcludeCategory();
        logger.info("当前晚上折扣配置：{}", currentEveningDiscount);
        List<TpmGoodsCartResVo> collect = tpmGoods.stream().map(goods -> {
            TpmGoodsCartResVo tpmGoodsCartResVo = new TpmGoodsCartResVo();
            BeanUtils.copyProperties(goods, tpmGoodsCartResVo);
            if (Objects.isNull(goods.getInventoryNum()) || goods.getInventoryNum() <= 0) {
                //如果没库存直接设置为已售罄
                tpmGoodsCartResVo.setIsSellOut(true);
                return tpmGoodsCartResVo;
            }
            // 获取下商品属性和规格
            List<TpmGoodsAttributesDto> goodsAttributesDtos = attributeSpecificationService.queryByGid(goods.getId());
            if (!CollectionUtils.isEmpty(goodsAttributesDtos)) {
                tpmGoodsCartResVo.setGoodsAttributes(goodsAttributesDtos);
            }
            if (tpmCartMap.containsKey(goods.getId())) {
                List<TpmCart> tpmCartList = tpmCartMap.get(goods.getId());
                if (!CollectionUtils.isEmpty(tpmCartList)) {
                    int sum = tpmCartList.stream().mapToInt(t -> t.getNumber().intValue()).sum();
                    tpmGoodsCartResVo.setCheckedCount(sum);
                }
            }
            if (Objects.nonNull(goods.getInventoryNum())&&Objects.nonNull(inventoryNotifyCount)) {
                if (goods.getInventoryNum() <= inventoryNotifyCount) {
                    tpmGoodsCartResVo.setInventoryNotify("商品仅剩" + goods.getInventoryNum() + "件");
                }
            }
            //只有自取、外送有夜间折扣
            if (effectiveEveningDiscount && !excludeCategory.contains(goods.getCategoryId())) {
                tpmGoodsCartResVo.setDiscountFlag(true);
                tpmGoodsCartResVo.setDiscountPrice(goods.getRetailPrice().multiply(discount));
            }
            return tpmGoodsCartResVo;
        }).collect(Collectors.toList());
        return collect;
    }


    /**
     * 获取商品列表（根据类型分类）
     *
     * @return
     */
    @GetMapping("/shop/list")
    public WxResult<Map<Integer, List<TpmGoodsCartResVo>>> shopList(@RequestParam(name = "userId", defaultValue = "") Integer userId
                                                                 ) {
        logger.info("【请求开始】 /shop/list 获取商城商品列表（根据类型分类）,请求参数,userId:{}", userId);

        Integer businessType = TpmBusinessTypeEnums.EXPRESS.getCode();
        List<TpmGoods> tpmGoods = goodsService.listAllOnSellByBusinessType(businessType);
        if (CollectionUtils.isEmpty(tpmGoods)) {
            return WxResult.success(new HashMap<>());
        }
        List<TpmCart> tpmCarts;
        if (Objects.nonNull(userId) && !StringUtils.isEmpty(userId)) {
            tpmCarts = tpmCartService.queryByUid(userId, businessType);
        } else {
            tpmCarts = new ArrayList<>();
        }
        List<TpmGoodsCartResVo> collect = this.convertShopGoodsToResVO(tpmGoods, tpmCarts);

        Map<Integer, List<TpmGoodsCartResVo>> result = collect.stream().collect(Collectors.groupingBy(TpmGoodsCartResVo::getCategoryId));
        // 使用 TreeMap 确保 key 按照升序排列
        Map<Integer, List<TpmGoodsCartResVo>> sortedResult = new TreeMap<>(result);
        List<Integer> categoryIdList = collect.stream().map(TpmGoodsCartResVo::getCategoryId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(categoryIdList)) {
            List<TpmCategory> tpmCategoryList = tpmCategoryService.findById(categoryIdList);
            Map<Integer, TpmCategory> categoryMap = tpmCategoryList.stream()
                    .collect(Collectors.toMap(TpmCategory::getId, Function.identity()));
            // Step 3: 按照 sortOrder 字段对 categoryMap 进行排序，并重新构建 sortedResult
            Map<Integer, List<TpmGoodsCartResVo>> sortedBySortOrder = categoryMap.values()
                    .stream()
                    .sorted(Comparator.comparing(TpmCategory::getSortOrder))
                    .collect(Collectors.toMap(
                            TpmCategory::getId,
                            category -> sortedResult.get(category.getId()).stream()
                                    .sorted(Comparator.comparing(TpmGoodsCartResVo::getSortOrder))
                                    .collect(Collectors.toList()),
                            (e1, e2) -> e1,
                            LinkedHashMap::new // 保持顺序
                    ));
            return WxResult.success(sortedBySortOrder);
        }
        return WxResult.success(result);
    }

    @GetMapping("/shop/search")
    public WxResult<List<TpmGoodsCartResVo>> search(@RequestParam(name = "userId", defaultValue = "") Integer userId,
                                                                    @RequestParam(name = "goodsName", defaultValue = "") String goodsName) {
        logger.info("【请求开始】 /shop/list 获取商城商品列表（根据类型分类）,请求参数,userId:{},goodsName:{}", userId,goodsName);

        Integer businessType = TpmBusinessTypeEnums.EXPRESS.getCode();
        List<TpmGoods> tpmGoods = goodsService.listAllOnSellByBusinessTypeAndName(businessType,goodsName);
        if (CollectionUtils.isEmpty(tpmGoods)) {
            return WxResult.success(new ArrayList<>());
        }
        List<TpmCart> tpmCarts;
        if (Objects.nonNull(userId) && !StringUtils.isEmpty(userId)) {
            tpmCarts = tpmCartService.queryByUid(userId, businessType);
        } else {
            tpmCarts = new ArrayList<>();
        }
        List<TpmGoodsCartResVo> collect = this.convertShopGoodsToResVO(tpmGoods, tpmCarts);
        return WxResult.success(collect);
    }

    public List<TpmGoodsCartResVo> convertShopGoodsToResVO(List<TpmGoods> tpmGoods, List<TpmCart> tpmCarts) {
        Map<Integer, List<TpmCart>> tpmCartMap;
        if (!CollectionUtils.isEmpty(tpmCarts)) {
            tpmCartMap = tpmCarts.stream().collect(Collectors.groupingBy(TpmCart::getGoodsId));
        } else {
            tpmCartMap = new HashMap<>();
        }
//        Integer inventoryNotifyCount = SystemConfig.getInventoryNotifyCount();
        List<TpmGoodsCartResVo> collect = tpmGoods.stream().map(goods -> {
            TpmGoodsCartResVo tpmGoodsCartResVo = new TpmGoodsCartResVo();
            BeanUtils.copyProperties(goods, tpmGoodsCartResVo);
            if (Objects.isNull(goods.getInventoryNum()) || goods.getInventoryNum() <= 0) {
                //如果没库存直接设置为已售罄
                tpmGoodsCartResVo.setIsSellOut(true);
                return tpmGoodsCartResVo;
            }
            // 获取下商品属性和规格
            List<TpmGoodsAttributesDto> goodsAttributesDtos = attributeSpecificationService.queryByGid(goods.getId());
            if (!CollectionUtils.isEmpty(goodsAttributesDtos)) {
                tpmGoodsCartResVo.setGoodsAttributes(goodsAttributesDtos);
            }
            if (tpmCartMap.containsKey(goods.getId())) {
                List<TpmCart> tpmCartList = tpmCartMap.get(goods.getId());
                if (!CollectionUtils.isEmpty(tpmCartList)) {
                    int sum = tpmCartList.stream().mapToInt(t -> t.getNumber().intValue()).sum();
                    tpmGoodsCartResVo.setCheckedCount(sum);
                }
            }
            return tpmGoodsCartResVo;
        }).collect(Collectors.toList());
        return collect;
    }


    /**
     * 商品详情页面“大家都在看”推荐商品
     *
     * @param id, 商品ID
     * @return 商品详情页面推荐商品
     */
    @GetMapping("related")
    public Object related(@NotNull Integer id) {
        logger.info("【请求开始】商品详情页面“大家都在看”推荐商品,请求参数,id:{}", id);

        TpmGoods goods = goodsService.findById(id);
        if (goods == null) {
            return ResponseUtil.badArgumentValue();
        }

        // 目前的商品推荐算法仅仅是推荐同类目的其他商品
        int cid = goods.getCategoryId().intValue();
        int brandId = goods.getBrandId().intValue();

        // 查找六个相关商品,同店铺，同类优先
        int limitBid = 10;
        List<TpmGoods> goodsListBrandId = goodsService.queryByBrandId(brandId, cid, 0, limitBid);
        List<TpmGoods> relatedGoods = goodsListBrandId == null ? new ArrayList<TpmGoods>() : goodsListBrandId;
        if (goodsListBrandId == null || goodsListBrandId.size() < 6) {// 同店铺，同类商品小于6件，则获取其他店铺同类商品
            int limitCid = 6;
            List<TpmGoods> goodsListCategory = goodsService.queryByCategoryAndNotSameBrandId(brandId, cid, 0, limitCid);
            relatedGoods.addAll(goodsListCategory);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("goodsList", relatedGoods);

        logger.info("【请求结束】商品详情页面“大家都在看”推荐商品,响应结果:查询的商品数量:{}", relatedGoods.size());
        return ResponseUtil.ok(data);
    }

    /**
     * 在售的商品总数
     *
     * @return 在售的商品总数
     */
    @GetMapping("count")
    public WxResult<Integer> count(@RequestParam(name = "businessType", defaultValue = "1") Integer businessType) {
        logger.info("【请求开始】在售的商品总数...");
        Integer goodsCount = goodsService.queryOnSale(businessType);
        logger.info("【请求结束】在售的商品总数,响应结果:{}", JSONObject.toJSONString(goodsCount));
        return WxResult.success(goodsCount);
    }

}