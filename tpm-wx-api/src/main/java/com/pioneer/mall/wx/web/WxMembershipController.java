package com.pioneer.mall.wx.web;

import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.wx.service.WxMembershipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/wx/membership")
@RestController
public class WxMembershipController {

    @Autowired
    private WxMembershipService wxMembershipService;

    @GetMapping("/list")
    public WxResult listAll() {
        return WxResult.success(wxMembershipService.list());
    }
}
