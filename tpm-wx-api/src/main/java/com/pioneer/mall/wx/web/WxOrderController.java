package com.pioneer.mall.wx.web;

import com.alibaba.fastjson.JSON;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.bean.search.TpmOrderSearch;
import com.pioneer.mall.db.domain.TpmOrderGoods;
import com.pioneer.mall.wx.annotation.LoginUser;
import com.pioneer.mall.wx.bean.req.*;
import com.pioneer.mall.wx.bean.res.*;
import com.pioneer.mall.wx.service.WxOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/wx/order")
@Validated
public class WxOrderController {
    private static final Logger logger = LoggerFactory.getLogger(WxOrderController.class);

    @Autowired
    private WxOrderService wxOrderService;

    /**
     * 订单列表
     *
     * @param userId   用户ID
     * @param showType 订单信息
     * @param page     分页页数
     * @param size     分页大小
     * @return 订单列表
     */
    @PostMapping("list")
    public WxResult<TpmOrderListResVo> list(@RequestBody TpmOrderSearch tpmOrderSearch) {
        logger.info("【请求开始】订单列表,请求参数,tpmOrderSearch:{}", JSON.toJSONString(tpmOrderSearch));
        return wxOrderService.list(tpmOrderSearch);
    }

    /**
     * 订单详情
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @return 订单详情
     */
    @PostMapping("/detail")
    public WxResult<TpmOrderResVo> detail(@RequestBody TpmOrderOperateReqVo reqVo) {
        logger.info("【请求开始】查库订单详情,请求参数,reqVo:{}", reqVo);
        return wxOrderService.detail(reqVo.getUserId(), reqVo.getOrderId());
    }

    /**
     * 获取订单运单轨迹
     * @param reqVo
     * @return
     */
    @PostMapping("/getWaybillRoute")
    public WxResult<List<TpmWaybillRouteResVo>> getWaybillRoute(@RequestBody TpmOrderWaybillRouteReqVo reqVo) {
        logger.info("【请求开始】查库订单详情,请求参数,reqVo:{}", reqVo);
        return wxOrderService.getWaybillRoute(reqVo.getOrderId(),reqVo.getUserId());
    }

    /**
     * 物流跟踪
     *
     * @param userId
     * @param orderId
     * @return
     */
    @Deprecated
    @GetMapping("expressTrace")
    public Object expressTrace(@LoginUser Integer userId, @NotNull Integer orderId) {
        logger.info("【请求开始】查库订单物流跟踪,请求参数,userId:{},orderId:{}", userId, orderId);
//        return wxOrderService.expressTrace(userId, orderId);
        return null;
    }

    /**
     * 提交订单
     *
     * @param userId 用户ID
     * @param body   订单信息，{ cartId：xxx, addressId: xxx, couponId: xxx, message: xxx,
     *               grouponRulesId: xxx, grouponLinkId: xxx}
     * @return 提交订单操作结果
     */
    @PostMapping("submit")
    public WxResult<Integer> submit(@RequestBody TpmOrderSubmitReqVo tpmOrderSubmitReqVo) {
        logger.info("【请求开始】提交用户订单,请求参数,tpmOrderSubmitReqVo:{}", JSON.toJSONString(tpmOrderSubmitReqVo));
        if (Objects.isNull(tpmOrderSubmitReqVo)){
            return WxResult.badArgument();
        }
        if (Objects.isNull(tpmOrderSubmitReqVo.getShopId())){
            return WxResult.error("请选择店铺下单");
        }
        boolean allow = SystemConfig.getAllowSubmitOrder(tpmOrderSubmitReqVo.getShopId());
        if (allow) {
            return wxOrderService.submit(tpmOrderSubmitReqVo);
        }
        return WxResult.error("当前未开放下单");

    }

    @PostMapping("shop/submit")
    public WxResult<TpmShopSubmitResVo> shopSubmit(@RequestBody TpmOrderShopSubmitReqVo tpmOrderShopSubmitReqVo) {
        logger.info("【请求开始】提交用户订单,请求参数,tpmOrderShopSubmitReqVo:{}", JSON.toJSONString(tpmOrderShopSubmitReqVo));
        boolean allow = SystemConfig.getAllowSubmitShopOrder(1);
        if (allow) {
            return wxOrderService.shopSubmit(tpmOrderShopSubmitReqVo);
        }
        return WxResult.error("当前未开放下单");

    }

    /**
     * 取消订单
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 取消订单操作结果
     */
    @PostMapping("cancel")
    public WxResult<String> cancel(@RequestBody TpmOrderOperateReqVo reqVo) {
        logger.info("【请求开始】取消用户订单,请求参数,reqVo:{}", reqVo);
        return wxOrderService.cancel(reqVo);
    }

    /**
     * 付款订单的预支付会话标识
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 支付订单ID
     */
    @PostMapping("prepay")
    public WxResult<WxPayUnifiedOrderV3Result.JsapiResult> prepay(@RequestBody TpmOrderPrePayReqVo tpmOrderPrePayReqVo, HttpServletRequest request) {
        logger.info("【请求开始】付款订单的预支付会话标识,请求参数,userId:{},body:{}", tpmOrderPrePayReqVo.getUserId(), tpmOrderPrePayReqVo.getOrderId());
        return wxOrderService.prepay(tpmOrderPrePayReqVo, request);
    }

    @PostMapping("combinePrepay")
    public WxResult<WxPayMpOrderResult> combinePrepay(@RequestBody TpmOrderCombinePrePayReqVo tpmOrderCombinePrePayReqVo, HttpServletRequest request) {
        logger.info("【请求开始】付款订单的预支付会话标识,请求参数,reqVo:{}", JSON.toJSONString(tpmOrderCombinePrePayReqVo));
//        return wxOrderService.combinePrepay(tpmOrderCombinePrePayReqVo, request);
        return wxOrderService.combinePrepayV2(tpmOrderCombinePrePayReqVo, request);
    }

    //todo 余额的合单支付
    @PostMapping("/combine/balance/pay")
    public WxResult<String> combineBalancePay(@RequestBody TpmOrderCombinePrePayReqVo tpmOrderPrePayReqVo, HttpServletRequest request) {
        logger.info("【请求开始】付款订单的余额支付会话标识,请求参数,reqVo:{}", JSON.toJSONString(tpmOrderPrePayReqVo));
        return wxOrderService.combineBalancePay(tpmOrderPrePayReqVo, request);
    }


    /**
     * 余额支付
     *
     * @param tpmOrderPrePayReqVo
     * @param request
     * @return
     */
    @PostMapping("/balance/pay")
    public WxResult<String> balancePay(@RequestBody TpmOrderPrePayReqVo tpmOrderPrePayReqVo, HttpServletRequest request) {
        logger.info("【请求开始】付款订单的余额支付会话标识,请求参数,userId:{},body:{}", tpmOrderPrePayReqVo.getUserId(), tpmOrderPrePayReqVo.getOrderId());
        return wxOrderService.balancePay(tpmOrderPrePayReqVo, request);
    }

    /**
     * 微信付款成功或失败回调接口
     * <p>
     * TODO 注意，这里pay-notify是示例地址，建议开发者应该设立一个隐蔽的回调地址
     *
     * @param request  请求内容
     * @param response 响应内容
     * @return 操作结果
     */
    @PostMapping("tpmNotify")
    public Object payNotify(HttpServletRequest request, HttpServletResponse response) {
        logger.info("【请求开始】微信付款成功或失败回调...");
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setTimeStamp(request.getHeader("Wechatpay-Timestamp"));
        signatureHeader.setNonce(request.getHeader("Wechatpay-Nonce"));
        signatureHeader.setSerial(request.getHeader("Wechatpay-Serial"));
        signatureHeader.setSignature(request.getHeader("Wechatpay-Signature"));
        logger.info("signatureHeader={}",JSON.toJSONString(signatureHeader));
        String result = wxOrderService.tpmPayNotify(request, response,signatureHeader);
        logger.info("【请求开始】微信付款成功或失败回调...处理结果:{}", result);
        return result;
    }

   @PostMapping("combine/tpmNotify")
    public Object combinePayNotify(HttpServletRequest request, HttpServletResponse response) {
        logger.info("【请求开始】微信合单付款成功或失败回调...");
        String result = wxOrderService.tpmCombinePayNotify(request, response);
        logger.info("【请求开始】微信合单付款成功或失败回调...处理结果:{}", result);
        return result;
    }

    /**
     * 订单申请退款
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单退款操作结果
     */
    @PostMapping("refund")
    public WxResult<String> refund(@RequestBody TpmOrderOperateReqVo reqVo) {
        logger.info("【请求开始】订单申请退款,请求参数,reqVo:{}", reqVo);
        return wxOrderService.refund(reqVo);
    }

    /**
     * 确认收货
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    @PostMapping("confirm")
    public WxResult<String> confirm(@RequestBody TpmOrderOperateReqVo reqVo) {
        logger.info("【请求开始】用户确认收货,请求参数,reqVo:{}", reqVo);
        return wxOrderService.confirm(reqVo);
    }

    /**
     * 删除订单
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    @PostMapping("delete")
    public WxResult<String> delete(@RequestBody TpmOrderOperateReqVo reqVo) {
        logger.info("【请求开始】用户删除订单,请求参数,reqVo:{}", reqVo);
        return wxOrderService.delete(reqVo);
    }

    /**
     * 待评价订单商品信息
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @param goodsId 商品ID
     * @return 待评价订单商品信息
     */
    @GetMapping("goods")
    public WxResult<TpmOrderGoods> goods(@LoginUser Integer userId, @NotNull Integer orderId, @NotNull Integer goodsId) {
        logger.info("【请求开始】获取待评价订单商品信息,请求参数,userId:{},orderId:{},goodsId:{}", userId, orderId, goodsId);
        return wxOrderService.goods(userId, orderId, goodsId);
    }

    /**
     * 评价订单商品
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    @PostMapping("comment")
    public WxResult<String> comment(@LoginUser Integer userId, @RequestBody String body) {
        logger.info("【请求开始】评价订单商品,请求参数,userId:{},body:{}", userId, body);
        return wxOrderService.comment(userId, body);
    }

    @PostMapping("/existUnTakeMeal")
    public WxResult<TpmUnTakeMealResVo> existUnTakeMeal(@RequestBody TpmUnTakeMealReqVo reqVo) {
        logger.info("【请求开始】existUnTakeMeal reqVo:{}", JSON.toJSONString(reqVo));
        return wxOrderService.existUnTakeMeal(reqVo);
    }

    @PostMapping("/isFinishTakeMeal")
    public WxResult<Boolean> isFinishTakeMeal(@RequestBody TpmUnTakeMealReqVo reqVo) {
        logger.info("【请求开始】isFinishTakeMeal reqVo:{}", JSON.toJSONString(reqVo));
        Boolean finishTakeMeal = wxOrderService.isFinishTakeMeal(reqVo.getOrderId());
        return WxResult.success(finishTakeMeal);
    }

}