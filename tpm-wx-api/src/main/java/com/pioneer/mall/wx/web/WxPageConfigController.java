package com.pioneer.mall.wx.web;

import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.domain.TpmWxPageConfig;
import com.pioneer.mall.db.service.TpmWxPageConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/8 21:47
 */
@RestController
@RequestMapping("/wx/pageConfig")
public class WxPageConfigController {
    private static final Logger logger = LoggerFactory.getLogger(WxPageConfigController.class);

    @Autowired
    private TpmWxPageConfigService tpmWxPageConfigService;

    @RequestMapping("/aboutUs")
    public WxResult<List<TpmWxPageConfig>> aboutUs(){
        try {
            List<TpmWxPageConfig> wxPageConfigList = tpmWxPageConfigService.queryPageByMenu("aboutUs");
            return WxResult.success(wxPageConfigList);
        }catch (Exception e){
            return WxResult.error("获取关于我们图片失败");
        }
    }

    @RequestMapping("/aboutUs/desc")
    public WxResult<List<TpmWxPageConfig>> aboutUsDesc(){
        try {
            List<TpmWxPageConfig> wxPageConfigList = tpmWxPageConfigService.queryDescByMenu("aboutUs");
            return WxResult.success(wxPageConfigList);
        }catch (Exception e){
            return WxResult.error("获取关于我们图片失败");
        }
    }
}
