package com.pioneer.mall.wx.web;

import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.wx.service.WxMembershipService;
import com.pioneer.mall.wx.service.WxPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

//@RequestMapping("/wx/point")
@RestController
public class WxPointController {

    @Autowired
    private WxPointService wxPointService;

//    @GetMapping("/list")
//    public WxResult<List<TpmPointDto>> list(Integer userId) {
//        return WxResult.success(wxPointService.list(userId));
//    }
}
