package com.pioneer.mall.wx.web;

import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.core.validator.Sort;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.wx.annotation.LoginUser;
import com.pioneer.mall.wx.bean.req.TpmPointExchangeSubmitReqVo;
import com.pioneer.mall.wx.bean.res.TpmPointExchangeConfigResVo;
import com.pioneer.mall.wx.bean.res.TpmPointHisListResVo;
import com.pioneer.mall.wx.service.WxPointExchangeConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/wx/point")
public class WxPointExchangeConfigController {

    @Resource
    private WxPointExchangeConfigService wxPointExchangeConfigService;


    private static final Logger logger = LoggerFactory.getLogger(WxPointExchangeConfigController.class);

    /**
     * 历史积分列表    显示用户积分变更日志
     *
     * @param userId
     * @param page
     * @param size
     * @param sort
     * @return
     */
    @GetMapping("/his/list")
    public WxResult<List<TpmPointHisListResVo>> getHisList(@LoginUser @RequestParam Integer userId,
                                                           @RequestParam(defaultValue = "1") Integer page,
                                                           @RequestParam(defaultValue = "10") Integer size,
                                                           @Sort @RequestParam(defaultValue = "create_time") String sort) {

        List<TpmPointHisListResVo> tpmPointHisListResVoList = wxPointExchangeConfigService.getHisList(userId, page, size);
        return WxResult.success(tpmPointHisListResVoList);
    }

    /**
     * 用户积分兑换列表
     * 显示多少积分兑换多少优惠券
     * 并提示能否兑换
     */
    @GetMapping("/exchange/list")
    public WxResult<List<TpmPointExchangeConfigResVo>> getPointExchangeConfigList(@LoginUser @RequestParam Integer userId) {

        List<TpmPointExchangeConfigResVo> tpmPointExchangeConfigResVoList = wxPointExchangeConfigService.getPointExchangeConfigList(userId);
        return WxResult.success(tpmPointExchangeConfigResVoList);
    }

    @PostMapping("/exchange/submit")
    public WxResult<String> exchangeSubmit(@NotNull @RequestBody TpmPointExchangeSubmitReqVo pointPagingReq) {
        try {
            wxPointExchangeConfigService.exchangeSubmit(pointPagingReq);
        } catch (Exception ex) {
            return WxResult.error(ex.getMessage());
        }
        return WxResult.success("兑换成功");
    }

}
