package com.pioneer.mall.wx.web;

import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.bean.request.TpmRechargeRequest;
import com.pioneer.mall.db.bean.search.TpmRechargeConfigSearch;
import com.pioneer.mall.db.bean.search.TpmRechargeSearch;
import com.pioneer.mall.db.domain.TpmRecharge;
import com.pioneer.mall.db.domain.TpmRechargeConfig;
import com.pioneer.mall.db.service.TpmRechargeConfigService;
import com.pioneer.mall.db.service.TpmRechargeService;
import com.pioneer.mall.wx.service.WxRechargeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/7 21:59
 */
@RestController
@RequestMapping("/wx/recharge")
@Validated
public class WxRechargeController {
    private static final Logger logger = LoggerFactory.getLogger(WxRechargeController.class);

    @Autowired
    private TpmRechargeService tpmRechargeService;
    @Autowired
    private TpmRechargeConfigService tpmRechargeConfigService;
    @Autowired
    private WxRechargeService wxRechargeService;

    @RequestMapping("/list")
    public WxResult<List<TpmRecharge>> list(@RequestBody TpmRechargeSearch tpmRechargeSearch) {
        try {
            List<TpmRecharge> tpmRecharges = tpmRechargeService.querySelective(tpmRechargeSearch);
            return WxResult.success(tpmRecharges);
        } catch (Exception e) {
            logger.error("WxRechargeController list error={}", e.getMessage(), e);
            return WxResult.error(-1, "获取充值列表信息失败");
        }
    }

    @RequestMapping("/config/list")
    public WxResult<List<TpmRechargeConfig>> listConfig(@RequestBody TpmRechargeConfigSearch tpmRechargeConfigSearch) {
        tpmRechargeConfigSearch.setSort("sort");
        tpmRechargeConfigSearch.setOrder("asc");
        List<TpmRechargeConfig> list = tpmRechargeConfigService.querySelective(tpmRechargeConfigSearch);
        return WxResult.success(list);
    }

    @Autowired
    private HttpServletRequest request;

    @RequestMapping("/preRecharge")
    public WxResult<WxPayMpOrderResult> preRecharge(@RequestBody TpmRechargeRequest tpmRechargeRequest) {
        try {
            return wxRechargeService.preRecharge(tpmRechargeRequest, request);
        } catch (Exception e) {
            logger.error("WxRechargeController preRecharge error={}", e.getMessage(), e);
            return WxResult.error(-1, e.getMessage());
        }
    }

    //todo 接受微信回调
    @PostMapping("rechargeNotify")
    public String rechargePayNotify(HttpServletRequest request, HttpServletResponse response) {
        logger.info("【请求开始】微信付款成功或失败回调...");
        return wxRechargeService.rechargePayNotify(request, response);
    }
}
