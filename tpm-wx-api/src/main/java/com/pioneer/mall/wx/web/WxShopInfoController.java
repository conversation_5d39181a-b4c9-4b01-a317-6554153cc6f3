package com.pioneer.mall.wx.web;

import com.pioneer.mall.core.util.ConvertUtil;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.dto.TpmShopInfoDTO;
import com.pioneer.mall.db.dto.TpmShopOpenStatusDTO;
import com.pioneer.mall.db.service.TpmShopHoursService;
import com.pioneer.mall.db.service.TpmShopInfoService;
import com.pioneer.mall.wx.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/5 23:33
 */
@Slf4j
@RestController
@RequestMapping("/wx/shop")
public class WxShopInfoController {
    @Autowired
    private TpmShopHoursService tpmShopHoursService;
    @Autowired
    private TpmShopInfoService tpmShopInfoService;

    @GetMapping("/getOpenStatus")
    public WxResult<TpmShopOpenStatusDTO> getOpenStatus(@RequestParam(name = "shopId") Integer shopId) {
        log.info("【请求开始】获取店铺营业时间,请求参数,shopId:{}", shopId);
        try {
            TpmShopOpenStatusDTO shopOpenStatus = tpmShopHoursService.getShopOpenStatus(shopId);
            if (Objects.isNull(shopOpenStatus)){
                shopOpenStatus = new TpmShopOpenStatusDTO();
                shopOpenStatus.setShopId(shopId);
                shopOpenStatus.setOpen(false);
                shopOpenStatus.setStatusDesc("店铺未营业");
                return WxResult.success(shopOpenStatus);
            }
            if (!shopOpenStatus.getOpen()&&Objects.isNull(shopOpenStatus.getStatusDesc())){
                shopOpenStatus.setStatusDesc("店铺未营业");
            }
            return WxResult.success(shopOpenStatus);
        }catch (Exception e){
            return WxResult.error("获取店铺营业时间失败");
        }
    }

    /**
     * 获取全部门店列表
     * @return
     */
    @GetMapping("/getShopList")
    public WxResult<List<TpmShopInfoDTO>> getShopList() {
        log.info("【请求开始】获取店铺列表");
        List<TpmShopInfo> tpmShopInfoList = tpmShopInfoService.getList();
        List<TpmShopInfoDTO> tpmShopInfoDTOS = ConvertUtil.listConvert(tpmShopInfoList, TpmShopInfoDTO.class);

        // 为每个店铺添加营业时间信息
        for (TpmShopInfoDTO shopDTO : tpmShopInfoDTOS) {
            try {
                TpmShopOpenStatusDTO openStatus = tpmShopHoursService.getShopOpenStatus(shopDTO.getId());
                if (openStatus != null) {
                    shopDTO.setOpen(openStatus.getOpen());
                    shopDTO.setOpenTimeDesc(openStatus.getOpenTimeDesc());
                    shopDTO.setStatusDesc(openStatus.getStatusDesc());
                    shopDTO.setOpeningTime(openStatus.getOpeningTime());
                    shopDTO.setClosingTime(openStatus.getClosingTime());
                } else {
                    shopDTO.setOpen(false);
                    shopDTO.setStatusDesc("店铺未营业");
                }
            } catch (Exception e) {
                log.error("获取店铺{}营业时间失败: {}", shopDTO.getId(), e.getMessage());
                shopDTO.setOpen(false);
                shopDTO.setStatusDesc("店铺未营业");
            }
        }

        // 排序：营业中的店铺排在前面，未营业的排在后面
        tpmShopInfoDTOS.sort((shop1, shop2) -> {
            // 首先按营业状态排序，营业中的排在前面
            Boolean open1 = shop1.getOpen() != null ? shop1.getOpen() : false;
            Boolean open2 = shop2.getOpen() != null ? shop2.getOpen() : false;

            if (!open1.equals(open2)) {
                return open2.compareTo(open1); // true排在前面
            }

            // 如果营业状态相同，按店铺ID排序（保持稳定排序）
            return shop1.getId().compareTo(shop2.getId());
        });

        return WxResult.success(tpmShopInfoDTOS);
    }

    /**
     * 获取全部门店列表
     * @return
     */
    @GetMapping("/getShopListByLatAndLng")
    public WxResult<List<TpmShopInfoDTO>> getShopList(String latAndLng) throws Exception {
        log.info("【请求开始】获取店铺列表 latAndLng={}",latAndLng);
        List<TpmShopInfo> tpmShopInfoList = tpmShopInfoService.getList();
        List<TpmShopInfoDTO> tpmShopInfoDTOS = tpmShopInfoList.stream().map(t->{
            TpmShopInfoDTO tpmShopInfoDTO = ConvertUtil.beanConvert(t, TpmShopInfoDTO.class);
            tpmShopInfoDTO.setTelephone(t.getPhoneNumber());
            return tpmShopInfoDTO;
        }).collect(Collectors.toList());
        List<TpmShopInfoDTO> result = MapUtils.calculateDistance(latAndLng, tpmShopInfoDTOS);

        // 为每个店铺添加营业时间信息和距离描述
        for (TpmShopInfoDTO shopDTO : result) {
            // 添加营业时间信息
            try {
                TpmShopOpenStatusDTO openStatus = tpmShopHoursService.getShopOpenStatus(shopDTO.getId());
                if (openStatus != null) {
                    shopDTO.setOpen(openStatus.getWorking());
                    shopDTO.setOpenTimeDesc(openStatus.getOpenTimeDesc());
                    shopDTO.setStatusDesc(openStatus.getStatusDesc());
                    shopDTO.setOpeningTime(openStatus.getOpeningTime());
                    shopDTO.setClosingTime(openStatus.getClosingTime());
                } else {
                    shopDTO.setOpen(false);
                    shopDTO.setStatusDesc("店铺未营业");
                }
            } catch (Exception e) {
                log.error("获取店铺{}营业时间失败: {}", shopDTO.getId(), e.getMessage());
                shopDTO.setOpen(false);
                shopDTO.setStatusDesc("店铺未营业");
            }

            // 添加距离描述
            if (shopDTO.getDistance() != null) {
                shopDTO.setDistanceDesc(formatDistanceDescription(shopDTO.getDistance()));
            }
        }

        // 排序：1.营业中的店铺排在前面 2.按距离排序
        result.sort((shop1, shop2) -> {
            // 首先按营业状态排序，营业中的排在前面
            Boolean open1 = shop1.getOpen() != null ? shop1.getOpen() : false;
            Boolean open2 = shop2.getOpen() != null ? shop2.getOpen() : false;

            if (!open1.equals(open2)) {
                return open2.compareTo(open1); // true排在前面
            }

            // 如果营业状态相同，按距离排序
            Integer distance1 = shop1.getDistance() != null ? shop1.getDistance() : Integer.MAX_VALUE;
            Integer distance2 = shop2.getDistance() != null ? shop2.getDistance() : Integer.MAX_VALUE;

            return distance1.compareTo(distance2);
        });

        log.info("【请求结束】获取店铺列表,返回结果:{}", result);
        return WxResult.success(result);
    }

    @GetMapping("/getShopLocation")
    public WxResult<TpmShopInfoDTO> getShopLocation(@RequestParam(name = "shopId") Integer shopId) {
        log.info("【请求开始】获取店铺地址,请求参数,shopId:{}", shopId);
        TpmShopInfo tpmShopInfo = tpmShopInfoService.getShopInfoById(shopId);
        TpmShopInfoDTO tpmShopInfoDTO = new TpmShopInfoDTO();
        tpmShopInfoDTO.setShopName(tpmShopInfo.getShopName());
        tpmShopInfoDTO.setLongitude(tpmShopInfo.getLongitude());
        tpmShopInfoDTO.setLatitude(tpmShopInfo.getLatitude());
        tpmShopInfoDTO.setAddress(tpmShopInfo.getAddress());
        tpmShopInfoDTO.setId(tpmShopInfo.getId());
        tpmShopInfoDTO.setTelephone(tpmShopInfo.getPhoneNumber());
        return WxResult.success(tpmShopInfoDTO);
    }

    /**
     * 格式化距离描述
     * @param distance 距离（米）
     * @return 格式化后的距离描述
     */
    private String formatDistanceDescription(Integer distance) {
        if (distance == null) {
            return null;
        }

        if (distance < 1000) {
            return distance + "米";
        } else {
            double km = distance / 1000.0;
            return String.format("%.2fkm", km);
        }
    }
}
