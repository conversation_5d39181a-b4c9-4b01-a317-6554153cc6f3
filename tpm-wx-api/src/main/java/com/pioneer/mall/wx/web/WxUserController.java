package com.pioneer.mall.wx.web;

import com.alibaba.fastjson.JSONObject;
import com.pioneer.mall.core.util.ResponseUtil;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.domain.TpmMembership;
import com.pioneer.mall.db.domain.TpmUser;
import com.pioneer.mall.db.domain.TpmUserAccount;
import com.pioneer.mall.db.enums.MembershipLevel;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.util.WxResponseCode;
import com.pioneer.mall.db.util.WxResponseUtil;
import com.pioneer.mall.wx.annotation.LoginUser;
import com.pioneer.mall.wx.bean.req.TpmUserInfoUpdateReqVo;
import com.pioneer.mall.wx.bean.res.TpmUserDetailResVo;
import com.pioneer.mall.wx.bean.res.TpmUserInfoResVo;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 用户服务
 */
@RestController
@RequestMapping("/wx/user")
@Validated
public class WxUserController {
    private static final Logger logger = LoggerFactory.getLogger(WxUserController.class);

    @Autowired
    private TpmOrderService orderService;

    @Autowired
    private TpmAccountService accountService;

    @Autowired
    private TpmUserService userService;

    @Autowired
    private TpmCouponService couponService;

    @Autowired
    private TpmMembershipService tpmMembershipService;

    @Autowired
    private TpmCouponUserService tpmCouponUserService;

    /**
     * 用户个人页面数据
     * <p>
     *
     * @param userId 用户ID
     * @return 用户个人页面数据
     */
    @GetMapping("index")
    public Object list(@LoginUser @RequestParam Integer userId) {
        logger.info("【请求开始】用户个人页面数据,请求参数,userId:{}", userId);

        if (userId == null) {
            logger.error("用户个人页面数据查询失败:用户未登录！！！");
            return ResponseUtil.unlogin();
        }

        Map<Object, Object> data = new HashMap<Object, Object>();
        data.put("order", orderService.orderInfo(userId));

        // 查询用户账号的总金额和剩余金额
        TpmUserAccount userAccount = accountService.findShareUserAccountByUserId(userId);
        BigDecimal totalAmount = new BigDecimal(0.00);
        BigDecimal remainAmount = new BigDecimal(0.00);
        if (userAccount != null) {
            totalAmount = userAccount.getTotalAmount();
            remainAmount = userAccount.getRemainAmount();
        }

        // 可提现金额 = 已结算未提现 remainAmount + 未结算 unSettleAmount
        BigDecimal unSettleAmount = accountService.getUnSettleAmount(userId);
        data.put("totalAmount", totalAmount);
        data.put("remainAmount", remainAmount.add(unSettleAmount));

        // 用户积分
        TpmUser tpmUser = userService.findById(userId);
        data.put("points", tpmUser.getPoints());
        TpmMembership tpmMembership = tpmMembershipService.findByPoints(tpmUser.getAccumulativePoints());
        data.put("membership", tpmMembership.getCode());
        data.put("membershipName", tpmMembership.getName());

        // 查询用户的优惠券
        int total = couponService.queryTotal();
        data.put("couponCount", total);

        logger.info("【请求结束】用户个人页面数据,响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    /**
     * 用户详情接口
     *
     * @param userId
     * @return
     */
    @RequestMapping("detail")
    public WxResult<TpmUserDetailResVo> detail(@RequestParam Integer userId) {

        if (userId == null) {
            logger.error("用户个人页面数据查询失败:用户未登录！！！");
            return WxResult.unlogin();
//            return WxResult.success(new TpmUserDetailResVo());
        }
        TpmUser tpmUser = userService.findById(userId);
        if (Objects.isNull(tpmUser)) {
            return WxResult.error(-1, "无该用户信息");
        }

        TpmUserDetailResVo resVo = new TpmUserDetailResVo();
        resVo.setId(tpmUser.getId());
        resVo.setBalance(tpmUser.getBalance() == null ? BigDecimal.ZERO : tpmUser.getBalance());
        resVo.setPoints(tpmUser.getPoints() == null ? BigDecimal.ZERO : tpmUser.getPoints());
        resVo.setNickName(tpmUser.getNickname());
        resVo.setAvatar(tpmUser.getAvatar());
        resVo.setMembershipLevel(tpmUser.getMembershipLevel());
        resVo.setMembershipCode(tpmUser.getMembershipCode());
        TpmMembership tpmMembership = tpmMembershipService.findById(tpmUser.getMembershipId());
        if (Objects.nonNull(tpmMembership)) {
            resVo.setMembershipCardUrl(tpmMembership.getPicUrl());
        }
        try {
            Integer availableCoupon = tpmCouponUserService.countAvailableCoupon(userId);
            resVo.setCouponCount(availableCoupon);
        } catch (Exception e) {
            logger.error("获取用户优惠券失败 error={}", e.getMessage(), e);
        }
        return WxResult.success(resVo);
    }

    /**
     * 个人信息
     *
     * @param userId
     * @return
     */
    @RequestMapping("/userInfo")
    public WxResult<TpmUserInfoResVo> userInfo(@RequestParam Integer userId) {

        if (userId == null) {
            logger.error("用户个人页面数据查询失败:用户未登录！！！");
            return WxResult.unlogin();
//            return WxResult.success(new TpmUserDetailResVo());
        }
        TpmUser tpmUser = userService.findById(userId);
        if (Objects.isNull(tpmUser)) {
            return WxResult.error(-1, "无该用户信息");
        }

        TpmUserInfoResVo resVo = new TpmUserInfoResVo();
        resVo.setUserName(tpmUser.getUsername());
        resVo.setGender(tpmUser.getGender().intValue());
        resVo.setTelephone(tpmUser.getMobile());
        resVo.setBirthday(tpmUser.getBirthday());
        resVo.setId(tpmUser.getId());
        resVo.setNickName(tpmUser.getNickname());
        resVo.setAvatar(tpmUser.getAvatar());
        resVo.setMembershipCode(tpmUser.getMembershipCode());
        LocalDateTime addTime = tpmUser.getAddTime();
        Date date = Date.from(addTime.atZone(ZoneId.systemDefault()).toInstant());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        resVo.setJoinMemberDateDesc(sdf.format(date) + " 成为7RiverLight 会员");
        logger.info("userInfo resVo={}", JSONObject.toJSONString(resVo));
        return WxResult.success(resVo);
    }


    /**
     * 更新用户信息
     *
     * @param tpmUser
     * @return
     */
    @PostMapping("updateUserInfo")
    public WxResult<String> updateUserInfo(@RequestBody TpmUserInfoUpdateReqVo reqVo) {
        if (Objects.isNull(reqVo)) {
            return WxResult.badArgument();
        }
        TpmUser tpmUser = new TpmUser();
        BeanUtils.copyProperties(reqVo, tpmUser);
        tpmUser.setId(reqVo.getUserId());
        tpmUser.setNickname(reqVo.getNickName().trim());
        if (Objects.nonNull(reqVo.getUserName())) {
            tpmUser.setUsername(reqVo.getUserName().trim());
        }
        tpmUser.setMobile(reqVo.getTelephone());
        tpmUser.setGender(reqVo.getGender().byteValue());
        userService.updateUserInfo(tpmUser);
        return WxResult.success("更新成功");
    }

    /**
     * 申请代理用户
     * <p>
     *
     * @param userId 用户ID
     * @return 用户个人页面数据
     */
    @PostMapping("applyAgency")
    public Object applyAgency(@LoginUser Integer userId) {
        logger.info("【请求开始】用户个人页面代理申请,请求参数,userId:{}", userId);

        if (userId == null) {
            logger.error("用户个人页面代理申请:用户未登录！！！");
            return ResponseUtil.unlogin();
        }

        TpmUser user = userService.findById(userId);

        //用户存在且未注销，未禁用
        if (user != null && user.getStatus().intValue() != 1 && user.getStatus().intValue() != 2) {
            // 查询用户账号,不存在则删除，如已经存在，不管状态如何都不做改变
            TpmUserAccount userAccount = accountService.findShareUserAccountByUserId(userId);
            if (userAccount == null) {//如果不存在则新建一个账户
                userAccount = new TpmUserAccount();
                userAccount.setRemainAmount(new BigDecimal(0));
                userAccount.setSettlementRate(5);//默认5%的比例
                userAccount.setStatus((byte) 1);//生效
                userAccount.setTotalAmount(new BigDecimal(0));
                userAccount.setUserId(userId);
                accountService.add(userAccount);
            }
            user.setStatus((byte) 3);//代理申请中
            userService.updateById(user);
        } else {
            logger.error("用户个人页面代理申请出错:{}", WxResponseCode.COUPON_EXCEED_LIMIT.desc());
            return WxResponseUtil.fail(WxResponseCode.INVALID_USER);
        }

        logger.info("【请求结束】用户个人页面代理申请,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    /**
     * 获取用户
     * <p>
     *
     * @param userId 用户ID
     * @return 用户个人页面数据
     */
    @GetMapping("getSharedUrl")
    public Object getSharedUrl(@LoginUser Integer userId) {
        logger.info("【请求开始】获取用户推广二维码图片URL,请求参数,userId:{}", userId);

        Map<String, Object> data = new HashMap<>();
        data.put("userSharedUrl", "");//默认设置没有
        if (userId == null) {
            logger.error("获取用户推广二维码图片URL:用户未登录！！！");
        } else {
            TpmUserAccount userAccount = accountService.findShareUserAccountByUserId(userId);
            //如果没申请，数据则不存在，存在数据且审批通过则会形成推广二维码
            if (userAccount != null && StringUtils.isNotBlank(userAccount.getShareUrl())) {
                data.put("userSharedUrl", userAccount.getShareUrl());
            }
        }

        logger.info("【请求结束】获取用户推广二维码图片URL,响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }
}